"""
User management and authentication endpoints
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import uuid
from sqlalchemy import select
from sqlalchemy.orm import Session

from src.rag_engine.analitic.db.postgresql.models import User
from src.rag_engine.analitic.db.postgresql.database import get_session
from src.agents.datasets.database import chat_history_db

router = APIRouter(prefix="/users")

class UserLoginRequest(BaseModel):
    """User login request model"""
    username: str

class UserLoginResponse(BaseModel):
    """User login response model"""
    user_id: str

class UserChatsResponse(BaseModel):
    """User chats response model"""
    chats: List[Dict[str, Any]]

@router.post("/login", response_model=UserLoginResponse)
async def login_user(request: UserLoginRequest):
    """
    Login a user with username. Returns the user_id.

    Args:
        request: UserLoginRequest with username

    Returns:
        User ID if successful, HTTP error otherwise
    """
    print(f"Login request=========================: {request}")
    try:
        if not request.username:
            raise HTTPException(status_code=400, detail="Kullanıcı adı gereklidir")
        if request.username.strip() == "":
            raise HTTPException(status_code=422, detail="Kullanıcı adı boş olamaz")
        if len(request.username) < 3:
            raise HTTPException(status_code=422, detail="Kullanıcı adı en az 3 karakter olmalıdır")

        with get_session() as session:
            result = session.execute(select(User).filter(User.username == request.username))
            user = result.scalars().first()

            if not user:
                new_user_id = str(uuid.uuid4())
                user = User(id=new_user_id, username=request.username)
                try:
                    session.add(user)
                    session.commit()
                except Exception as db_error:
                    raise HTTPException(status_code=503, detail=f"Veritabanı işlemi başarısız: {str(db_error)}")
                return UserLoginResponse(user_id=user.id)
            
            return UserLoginResponse(user_id=user.id)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Giriş sırasında hata: {str(e)}")

@router.get("/{user_id}/chats", response_model=UserChatsResponse)
async def get_user_chats(user_id: str):
    """
    Get all chat sessions for a specific user.

    Args:
        user_id: The ID of the user whose chats are to be retrieved.

    Returns:
        List of chat sessions with chat_id, chat_name, and created_at.
    """
    try:
        if not user_id:
            raise HTTPException(status_code=400, detail="Kullanıcı ID'si gereklidir")
        if not user_id.strip():
            raise HTTPException(status_code=422, detail="Geçersiz kullanıcı ID formatı")

        with get_session() as session:
            result = session.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()
            if not user:
                raise HTTPException(status_code=404, detail=f"Kullanıcı bulunamadı: {user_id}")

        try:
            chats = chat_history_db.get_user_chats(user_id)
        except Exception as db_error:
            raise HTTPException(status_code=503, detail=f"Chat oturumları alınamadı: {str(db_error)}")

        simplified_chats = [
            {
                "chat_id": chat["chat_id"],
                "chat_name": chat["chat_name"],
                "created_at": chat["created_at"]
            }
            for chat in chats
        ]
        return UserChatsResponse(chats=simplified_chats)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Kullanıcı chat oturumları alınırken hata: {str(e)}")
