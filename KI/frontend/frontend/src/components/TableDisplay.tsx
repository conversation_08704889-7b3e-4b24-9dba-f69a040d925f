import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  <PERSON>alogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Download as DownloadIcon,
  TableChart as TableChartIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { downloadReportFile } from '../app/services/api';
import { TableInfo } from './types';
import { parseCSV } from './utils/csvParser';
import { parseExcel } from './utils/excelParser';

interface TableDisplayProps {
  open: boolean;
  onClose: () => void;
  tables: TableInfo[];
  chatId: string;
  userId: string;
}

interface TableData {
  headers: string[];
  rows: string[][];
}

const TableDisplay: React.FC<TableDisplayProps> = ({
  open,
  onClose,
  tables,
  chatId,
  userId
}) => {
  const [selectedTable, setSelectedTable] = useState<TableInfo | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedTable(null);
      setTableData(null);
      setError(null);
    }
  }, [open]);

  const handleDownloadFile = async (table: TableInfo) => {
    try {
      setLoading(true);
      setError(null);
      
      const blob = await downloadReportFile(chatId, table.file_name, userId);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = table.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log('✅ File downloaded successfully:', table.file_name);
    } catch (error: any) {
      console.error('❌ File download failed:', error);
      setError(`Dosya indirilemedi: ${error.message || 'Bilinmeyen hata'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleViewTable = async (table: TableInfo) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedTable(table);
      
      const blob = await downloadReportFile(chatId, table.file_name, userId);
      
      if (table.type === 'csv') {
        const text = await blob.text();
        const parsedCSV = parseCSV(text);
        setTableData(parsedCSV);
      } else if (table.type === 'excel') {
        const arrayBuffer = await blob.arrayBuffer();
        const parsedExcel = await parseExcel(arrayBuffer);
        // Ensure all row data are strings for consistent rendering
        const stringifiedRows = parsedExcel.rows.map(row => 
          row.map(cell => (cell !== null && cell !== undefined) ? String(cell) : '')
        );
        setTableData({ headers: parsedExcel.headers, rows: stringifiedRows });
      } else {
        throw new Error(`Unsupported table type for viewing: ${table.type}`);
      }
    } catch (error: any) {
      console.error('❌ Table view failed:', error);
      setError(`Tablo görüntülenemedi: ${error.message || 'Bilinmeyen hata'}`);
      setSelectedTable(null);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToList = () => {
    setSelectedTable(null);
    setTableData(null);
    setError(null);
  };

  if (!open) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      scroll="paper"
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TableChartIcon color="primary" />
          <Typography variant="h6">
            {selectedTable ? `Tablo: ${selectedTable.table_name}` : 'Rapor Tabloları'}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}
        
        {!selectedTable && !loading && (
          <Box>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Aşağıdaki tabloları görüntüleyebilir veya indirebilirsiniz:
            </Typography>
            
            {tables.map((table, index) => (
              <Paper
                key={index}
                sx={{
                  p: 2,
                  mb: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" sx={{ mb: 1 }}>
                      {table.table_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {table.table_description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Dosya: {table.file_name} • Oluşturulma: {new Date(table.generated_at * 1000).toLocaleString('tr-TR')}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
                    <Tooltip title={(table.type === 'csv' || table.type === 'excel') ? "Tabloyu Görüntüle" : "Görüntüleme sadece CSV ve Excel dosyaları için geçerlidir"}>
                      <span>
                        <IconButton
                          color="primary"
                          onClick={() => handleViewTable(table)}
                          disabled={loading || !['csv', 'excel'].includes(table.type || '')}
                        >
                          <TableChartIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                    
                    <Tooltip title="Dosyayı İndir">
                      <IconButton
                        color="secondary"
                        onClick={() => handleDownloadFile(table)}
                        disabled={loading}
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            ))}
          </Box>
        )}
        
        {selectedTable && tableData && !loading && (
          <Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {selectedTable.table_description}
              </Typography>
            </Box>
            
            <TableContainer component={Paper} sx={{ maxHeight: '60vh' }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    {tableData.headers.map((header, index) => (
                      <TableCell
                        key={index}
                        sx={{
                          fontWeight: 'bold',
                          bgcolor: 'primary.main',
                          color: 'primary.contrastText'
                        }}
                      >
                        {header}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tableData.rows.map((row, rowIndex) => (
                    <TableRow
                      key={rowIndex}
                      sx={{ '&:nth-of-type(odd)': { bgcolor: 'action.hover' } }}
                    >
                      {row.map((cell, cellIndex) => (
                        <TableCell key={cellIndex}>
                          {cell}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        {selectedTable && (
          <Button onClick={handleBackToList} disabled={loading}>
            Listeye Dön
          </Button>
        )}
        {selectedTable && (
          <Button
            onClick={() => handleDownloadFile(selectedTable)}
            startIcon={<DownloadIcon />}
            disabled={loading}
          >
            İndir
          </Button>
        )}
        <Button onClick={onClose}>
          Kapat
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TableDisplay; 