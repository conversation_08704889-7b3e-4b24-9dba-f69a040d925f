#!/usr/bin/env python3
"""
Database migration script to add new columns to user_tables table.

This script adds the following columns to user_tables:
- chat_id (VARCHAR(255))
- file_hash (VARCHAR(64))
- upload_session_id (VARCHAR(255))
- file_size_bytes (BIGINT)
- delimiter (VARCHAR(10))
- encoding (VARCHAR(50))
- upload_order (INTEGER)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from src.rag_engine.analitic.db.postgresql.database import get_session
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(session, table_name, column_name):
    """Check if a column exists in a table"""
    query = text("""
        SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_name = :table_name 
            AND column_name = :column_name
            AND table_schema = 'public'
        );
    """)
    result = session.execute(query, {"table_name": table_name, "column_name": column_name})
    return result.scalar()

def add_column_if_not_exists(session, table_name, column_name, column_definition):
    """Add a column to a table if it doesn't exist"""
    if not check_column_exists(session, table_name, column_name):
        logger.info(f"Adding column {column_name} to {table_name}")
        query = text(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition};")
        session.execute(query)
        logger.info(f"✅ Column {column_name} added successfully")
    else:
        logger.info(f"⏭️ Column {column_name} already exists, skipping")

def migrate_user_tables():
    """Add new columns to user_tables table"""
    logger.info("🔄 Starting user_tables migration...")
    
    try:
        with get_session() as session:
            with session.begin():
                # Define new columns to add
                columns_to_add = [
                    ("chat_id", "VARCHAR(255)"),
                    ("file_hash", "VARCHAR(64)"),
                    ("upload_session_id", "VARCHAR(255)"),
                    ("file_size_bytes", "BIGINT"),
                    ("delimiter", "VARCHAR(10) DEFAULT ','"),
                    ("encoding", "VARCHAR(50) DEFAULT 'utf-8'"),
                    ("upload_order", "INTEGER DEFAULT 1")
                ]
                
                # Add each column
                for column_name, column_definition in columns_to_add:
                    add_column_if_not_exists(session, "user_tables", column_name, column_definition)
                
                logger.info("🎉 Migration completed successfully!")
                
    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        raise

def verify_migration():
    """Verify that all columns were added correctly"""
    logger.info("🔍 Verifying migration...")
    
    expected_columns = [
        "id", "user_id", "table_name", "original_file_name", 
        "created_at", "updated_at", "row_count",
        "chat_id", "file_hash", "upload_session_id", 
        "file_size_bytes", "delimiter", "encoding", "upload_order"
    ]
    
    try:
        with get_session() as session:
            query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'user_tables' 
                AND table_schema = 'public'
                ORDER BY ordinal_position;
            """)
            result = session.execute(query)
            existing_columns = [row[0] for row in result]
            
            logger.info(f"Existing columns: {existing_columns}")
            
            missing_columns = [col for col in expected_columns if col not in existing_columns]
            if missing_columns:
                logger.warning(f"⚠️ Missing columns: {missing_columns}")
                return False
            else:
                logger.info("✅ All expected columns are present!")
                return True
                
    except Exception as e:
        logger.error(f"❌ Verification failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting database migration for user_tables...")
    
    try:
        # Run migration
        migrate_user_tables()
        
        # Verify migration
        if verify_migration():
            print("✅ Migration completed and verified successfully!")
        else:
            print("⚠️ Migration completed but verification failed!")
            
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        sys.exit(1)
