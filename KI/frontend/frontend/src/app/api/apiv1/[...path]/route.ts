import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_API_URL || 'http://127.0.0.1:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  return handleRequest(request, resolvedParams, 'GET');
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  return handleRequest(request, resolvedParams, 'POST');
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  return handleRequest(request, resolvedParams, 'PUT');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  return handleRequest(request, resolvedParams, 'DELETE');
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  return handleRequest(request, resolvedParams, 'PATCH');
}

async function handleRequest(
  request: NextRequest,
  params: { path: string[] },
  method: string
) {
  try {
    const pathSegments = params.path || [];
    const backendPath = `/apiv1/${pathSegments.join('/')}`;
    const backendUrl = `${BACKEND_URL}${backendPath}`;
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams;
    const queryString = searchParams.toString();
    const finalUrl = queryString ? `${backendUrl}?${queryString}` : backendUrl;
    
    console.log(`🔄 Proxying ${method} request to:`, finalUrl);
    
    // Prepare headers - start with empty object
    const headers: HeadersInit = {};
    
    // Copy important headers from the original request
    const authHeader = request.headers.get('authorization');
    const contentTypeHeader = request.headers.get('content-type');
    const contentLengthHeader = request.headers.get('content-length');
    
    if (authHeader) {
      headers['authorization'] = authHeader;
    }
    
    // Preserve content-type for proper handling of different content types
    if (contentTypeHeader) {
      headers['content-type'] = contentTypeHeader;
    }
    
    // Preserve content-length if present (important for multipart uploads)
    if (contentLengthHeader) {
      headers['content-length'] = contentLengthHeader;
    }
    
    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
      // Don't follow redirects - handle them manually
      redirect: 'manual',
    };
    
    // Get and store request body for POST, PUT, PATCH requests
    let requestBody: string | ArrayBuffer | undefined;
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        // Handle different content types properly
        if (contentTypeHeader?.includes('multipart/form-data') || 
            contentTypeHeader?.includes('application/octet-stream')) {
          // For multipart/form-data and binary data, preserve as ArrayBuffer
          requestBody = await request.arrayBuffer();
          console.log('📦 Handling multipart/form-data or binary request');
        } else {
          // For JSON and other text-based content
          requestBody = await request.text();
          console.log('📦 Handling text/JSON request');
        }
        
        if (requestBody) {
          requestOptions.body = requestBody;
        }
      } catch (error) {
        console.error('Error reading request body:', error);
      }
    }
    
    // Make the request to backend
    let response = await fetch(finalUrl, requestOptions);
    
    // Handle redirects manually
    let redirectCount = 0;
    const maxRedirects = 5;
    
    while (
      (response.status === 301 || 
       response.status === 302 || 
       response.status === 307 || 
       response.status === 308) &&
      redirectCount < maxRedirects
    ) {
      const location = response.headers.get('location');
      if (!location) break;
      
      console.log(`🔄 Following redirect ${redirectCount + 1} to:`, location);
      
      // Make sure the redirect URL is still pointing to our backend
      let redirectUrl = location;
      if (location.startsWith('/')) {
        // Relative URL - prepend backend base
        redirectUrl = `${BACKEND_URL}${location}`;
      } else if (!location.startsWith(BACKEND_URL)) {
        // Absolute URL not pointing to our backend - break the loop
        console.warn('⚠️ Redirect pointing to external URL, stopping:', location);
        break;
      }
      
      // Follow the redirect - preserve body for POST/PUT/PATCH requests
      const redirectOptions = { ...requestOptions };
      if (['POST', 'PUT', 'PATCH'].includes(method) && requestBody) {
        redirectOptions.body = requestBody;
        console.log('📦 Preserving request body for redirect');
      } else {
        // Remove body for GET requests or when no body was provided
        redirectOptions.body = undefined;
      }
      
      response = await fetch(redirectUrl, redirectOptions);
      
      redirectCount++;
    }
    
    if (redirectCount >= maxRedirects) {
      console.error('❌ Too many redirects');
      return NextResponse.json(
        { error: 'Too many redirects' },
        { status: 508 }
      );
    }
    
    // The body of a fetch Response is a ReadableStream.
    // We can pass it directly to NextResponse to avoid buffering the entire response
    // in memory and to prevent corruption of binary files.
    const nextResponse = new NextResponse(response.body, {
      status: response.status,
      statusText: response.statusText,
    });
    
    // Copy important headers from backend response
    const headersToForward = [
      'content-type',
      'content-length',
      'cache-control',
      'etag',
      'last-modified',
      'content-disposition',
    ];
    
    headersToForward.forEach(headerName => {
      const headerValue = response.headers.get(headerName);
      if (headerValue) {
        nextResponse.headers.set(headerName, headerValue);
      }
    });
    
    // Add CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*');
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    console.log(`✅ Proxy response: ${response.status} for ${finalUrl}`);
    
    return nextResponse;
    
  } catch (error) {
    console.error('❌ Proxy error:', error);
    return NextResponse.json(
      { 
        error: 'Proxy request failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
} 