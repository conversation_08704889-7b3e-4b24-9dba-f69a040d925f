# First Iteration Plan

First iteration plan consists of simple questions asking merchants about their financial life. These questions can be:

- My Business Is Shrinking, What Should I Do?
- Mevcut müşterilerim<PERSON> kaybetmemek i<PERSON>in ne yapabiliri<PERSON> (How can I avoid losing my existing customers?)

In today's world, these questions can be easily responded to by free AI tools, but we need to establish a structure that is constantly updated and can provide personalized answers.

## Basic Components

### 1. A Strong and Diverse RAG Engine
- Must keep user's personal information
- It should keep the user's past memories
- It should keep environmental data related to the user's geopolitical, economic, and sociological location information
- We should keep the data that will be used for analytics that our user provides us at any time in an aggregation-friendly (SQL) manner
- All functionality should be provided to agents in a consistent and easy way

### 2. Our Agents
- Data collection and storing/manipulating related agents
- Agents that can run analytical functions and evaluate results over datasets
- Managers

### 3. Crew Design
- Should design architecture of our crew to be consistent, fast, and reliable
- A design that dynamically informs the user about the process
- Easily connectable by interfaces (HTTP, SOCKET APIs)
    
### 4. HTTP Interface
- Interface for HTTP (may be a gRPC interface would be better)

### 5. Documentation
- Our client may expect documentation and design documents for better understanding

## Notes

The task seems huge, but we can make it as simple as possible for the first showcase:

- Instead of adding multiple databases to RAG storage, we can work in-memory at the first stage
- Instead of creating too many agents, we create only the most necessary ones with the most essential tasks
- Keep documentation simple and not overly complicated
