from src.agents.agno_agents.agent_factory import TableOutput
import pandas as pd
import os
from datetime import datetime
from src.rag_engine.interface import RAGInterface
from typing import Optional, List, Dict, Tuple, Any
import json
from agno.tools import Toolk<PERSON>, tool
from pydantic import BaseModel, Field

# Function to generate csv from sql query, csv will store in dir/reports/csv/
async def generate_csv_from_sql_query(table_output: Any, user_id: Optional[str] = None) -> str:
    """
    Generate a CSV file from either a SQL query result or direct table data.
    
    Args:
        table_output: Either a TableOutput object containing SQL query or a dict with direct data
        user_id: Optional user ID for data access control
        
    Returns:
        str: Path to the generated CSV file
    """
    # Initialize RAG interface (only needed for SQL queries)
    rag_interface = RAGInterface()
    
    try:
        # Create reports directory if it doesn't exist
        reports_dir = os.path.join(os.getcwd(), 'reports', 'csv')
        os.makedirs(reports_dir, exist_ok=True)
        
        # Generate unique filename using timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_table_name = getattr(table_output, 'table_name', 'table').replace(' ', '_').lower()
        filename = f"{safe_table_name}_{timestamp}.csv"
        file_path = os.path.join(reports_dir, filename)

        # Handle both SQL-based and direct data cases
        if hasattr(table_output, 'data_sql') and table_output.data_sql:
            # SQL-based data
            result = rag_interface.agregate_csv_data(user_id, table_output.data_sql)
            
            if not result.get("success", False):
                error_msg = result.get("error") or result.get("data", {}).get("error") or "Unknown error"
                raise Exception(f"Failed to execute SQL query: {error_msg}")
            
            # Extract data from result
            data = result.get("data", {})
            
            # Handle both possible data structures
            if isinstance(data, dict):
                rows = data.get("rows", [])
                columns = data.get("columns", [])
            else:
                # If data is directly the result
                rows = data if isinstance(data, list) else []
                columns = list(rows[0].keys()) if rows else []
        else:
            # Direct data case
            # Check if table_output has direct data
            if hasattr(table_output, 'data') and isinstance(table_output.data, (list, dict)):
                if isinstance(table_output.data, dict):
                    # If data is a dictionary, convert to list of rows
                    rows = [table_output.data]
                else:
                    rows = table_output.data
                columns = list(rows[0].keys()) if rows else []
            else:
                raise Exception("No SQL query or direct data provided in table_output")

        if not rows or not columns:
            raise Exception("No data available for CSV generation")
        
        # Create DataFrame from data
        # Convert all values to string to avoid JSON serialization issues
        rows_processed = []
        for row in rows:
            processed_row = {}
            for key, value in row.items():
                if isinstance(value, (dict, list)):
                    processed_row[key] = str(value)
                else:
                    processed_row[key] = str(value) if value is not None else ""
            rows_processed.append(processed_row)
        
        df = pd.DataFrame(rows_processed, columns=columns)
        
        # Save to CSV
        df.to_csv(file_path, index=False)
        
        return file_path
        
    except Exception as e:
        print(f"Error generating CSV file: {str(e)}")
        raise Exception(f"Error generating CSV file: {str(e)}")

# Function to generate Excel from sql query, files will store in dir/reports/excel/
async def generate_excel_from_sql_query(table_output: Any, user_id: Optional[str] = None) -> str:
    """
    Generate an Excel file from either a SQL query result or direct table data.
    Uses pandas for reliable Excel generation.
    
    Args:
        table_output: Either a TableOutput object containing SQL query or a dict with direct data
        user_id: Optional user ID for data access control
        
    Returns:
        str: Path to the generated Excel file
    """
    # Initialize RAG interface (only needed for SQL queries)
    rag_interface = RAGInterface()
    
    try:
        # Create reports directory if it doesn't exist
        reports_dir = os.path.join(os.getcwd(), 'reports', 'excel')
        os.makedirs(reports_dir, exist_ok=True)
        
        # Generate unique filename using timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_table_name = getattr(table_output, 'table_name', 'table').replace(' ', '_').lower()
        filename = f"{safe_table_name}_{timestamp}.xlsx"
        file_path = os.path.join(reports_dir, filename)

        # Handle both SQL-based and direct data cases
        if hasattr(table_output, 'data_sql') and table_output.data_sql:
            # SQL-based data
            result = rag_interface.agregate_csv_data(user_id, table_output.data_sql)
            
            if not result.get("success", False):
                error_msg = result.get("error") or result.get("data", {}).get("error") or "Unknown error"
                raise Exception(f"Failed to execute SQL query: {error_msg}")
            
            # Extract data from result
            data = result.get("data", {})
            
            # Handle both possible data structures
            if isinstance(data, dict):
                rows = data.get("rows", [])
                columns = data.get("columns", [])
            else:
                # If data is directly the result
                rows = data if isinstance(data, list) else []
                columns = list(rows[0].keys()) if rows else []
        else:
            # Direct data case
            if hasattr(table_output, 'data') and isinstance(table_output.data, (list, dict)):
                if isinstance(table_output.data, dict):
                    rows = [table_output.data]
                else:
                    rows = table_output.data
                columns = list(rows[0].keys()) if rows else []
            else:
                raise Exception("No SQL query or direct data provided in table_output")

        if not rows or not columns:
            raise Exception("No data available for Excel generation")
        
        # Create DataFrame from data
        # Convert all values to string to avoid JSON serialization issues
        rows_processed = []
        for row in rows:
            processed_row = {}
            for key, value in row.items():
                if isinstance(value, (dict, list)):
                    processed_row[key] = str(value)
                else:
                    processed_row[key] = str(value) if value is not None else ""
            rows_processed.append(processed_row)
        
        df = pd.DataFrame(rows_processed, columns=columns)
        
        # Simple Excel export with basic formatting
        try:
            # First attempt: Basic Excel export
            df.to_excel(file_path, index=False, engine='openpyxl')
        except Exception as e:
            print(f"Basic Excel export failed, trying alternative method: {str(e)}")
            try:
                # Second attempt: CSV as fallback
                temp_csv = file_path.replace('.xlsx', '.temp.csv')
                df.to_csv(temp_csv, index=False)
                
                # Read CSV and save as Excel
                pd.read_csv(temp_csv).to_excel(file_path, index=False, engine='openpyxl')
                
                # Clean up temporary CSV
                if os.path.exists(temp_csv):
                    os.remove(temp_csv)
            except Exception as e2:
                print(f"Alternative Excel export failed: {str(e2)}")
                raise Exception(f"Failed to generate Excel file: {str(e2)}")
        
        return file_path
        
    except Exception as e:
        print(f"Error generating Excel file: {str(e)}")
        raise Exception(f"Error generating Excel file: {str(e)}")

