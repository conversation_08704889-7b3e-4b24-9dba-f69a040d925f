"""
CSV Validation Toolkit for Agno Agents
Provides tools for validating CSV files and reporting results to users.
"""

from agno.tools import Toolkit
from typing import Dict, Any, Optional
import logging
import time
import pandas as pd
import numpy as np
from src.rag_engine.analitic.csv_validator import validate_csv_content, CSVValidationResult
from src.rag_engine.analitic.processor import get_connector
from src.agents.agno_agents.tools.anomaly_detection_tool import AnomalyDetectionTool
import asyncio

logger = logging.getLogger(__name__)

class CSVValidationToolkit(Toolkit):
    """Toolkit for CSV validation operations"""

    def __init__(self):
        super().__init__(
            name="CSVValidationToolkit",
            instructions=None,
            add_instructions=False,
        )
        self.user_id = None  # Will be injected by context manager

        # Initialize anomaly detection tool
        self.anomaly_tool = AnomalyDetectionTool()

        # Register methods as tools
        self.register(self.validate_csv_data, name="validate_csv_data")

        print(f"✅ CSVValidationToolkit initialized with tools: validate_csv_data (with fast anomaly detection)")

    def set_context(self, user_id: str = None, chat_id: str = None, **kwargs):
        """Set user context for the toolkit"""
        if user_id:
            self.user_id = user_id
        print(f"🔧 CSVValidationToolkit: Set user_id={self.user_id}")

    def _get_latest_chat_table(self, chat_id: str, processor) -> Optional[Dict]:
        """
        Get the latest uploaded table for a specific chat

        Args:
            chat_id: Chat identifier
            processor: Database processor instance

        Returns:
            Latest table dictionary or None if not found
        """
        try:
            # Get all user tables
            user_tables = processor.get_user_tables(self.user_id)

            if not user_tables:
                return None

            # Filter tables that belong to this chat
            chat_tables = []
            for table in user_tables:
                table_name = table.get('name', '')
                # Check if table name contains the chat_id
                if f"chat_{chat_id}" in table_name:
                    chat_tables.append(table)

            if not chat_tables:
                return None

            # Return the most recently created table
            # Sort by created_at timestamp (assuming it exists)
            # Handle both new format tables and legacy tables
            def get_sort_key(table):
                # For new tables, use created_at
                created_at = table.get('created_at', '')
                if created_at:
                    return created_at
                # For legacy tables, use a default old timestamp
                return '1900-01-01 00:00:00'

            latest_table = max(chat_tables, key=get_sort_key)

            # Add legacy detection info
            is_legacy = not latest_table.get('chat_id') or not latest_table.get('file_hash')
            if is_legacy:
                logger.info(f"Found legacy table: {latest_table.get('name')} - missing new metadata fields")

            return latest_table

        except Exception as e:
            logger.error(f"Error getting latest chat table for chat {chat_id}: {str(e)}")
            return None

    def _get_latest_chat_table_with_user(self, chat_id: str, user_id: str, processor) -> Optional[Dict]:
        """
        Get the latest uploaded table for a specific chat with explicit user_id

        Args:
            chat_id: Chat identifier
            user_id: User identifier
            processor: Database processor instance

        Returns:
            Latest table dictionary or None if not found
        """
        try:
            print(f"🔍 Getting tables for user: {user_id}, chat: {chat_id}")
            # Use DatabaseToolkit approach: Get tables filtered by chat_id directly from database
            user_tables = processor.get_user_tables(user_id, chat_id)

            if not user_tables:
                print(f"🔍 No tables found for user: {user_id} in chat: {chat_id}")
                return None

            print(f"🔍 Found {len(user_tables)} tables for user: {user_id} in chat: {chat_id}")

            # No need for manual filtering - database already filtered by chat_id
            # Return the most recently created table
            def get_sort_key(table):
                # For new tables, use created_at
                created_at = table.get('created_at', '')
                if created_at:
                    return created_at
                # For legacy tables, use a default old timestamp
                return '1900-01-01 00:00:00'

            latest_table = max(user_tables, key=get_sort_key)
            print(f"🔍 Selected latest table: {latest_table.get('name')}")

            return latest_table

        except Exception as e:
            print(f"🔍 Error getting latest chat table for chat {chat_id}: {str(e)}")
            return None

    def _run_fast_anomaly_detection(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Run fast anomaly detection on DataFrame

        Args:
            df: DataFrame to analyze

        Returns:
            Anomaly detection results
        """
        try:
            # Convert DataFrame to list of dictionaries for anomaly tool
            data = df.to_dict('records')

            # Run anomaly detection with multiple methods
            methods = ['isolation_forest', 'z_score']
            if len(df) > 100:  # Only use IQR for larger datasets
                methods.append('iqr')

            # Adjust contamination based on data size
            contamination = 0.1 if len(df) > 50 else 0.2

            print(f"🔍 Running anomaly detection with methods: {methods}")
            print(f"🔍 Contamination rate: {contamination}")

            # Use the anomaly detection tool
            results = self.anomaly_tool.detect_anomalies_fast(
                data=data,
                contamination=contamination,
                methods=methods
            )

            if results.get("success"):
                print(f"🔍 Anomaly detection completed: {results.get('anomaly_count', 0)} anomalies found")
            else:
                print(f"🔍 Anomaly detection failed: {results.get('error', 'Unknown error')}")

            return results

        except Exception as e:
            print(f"🔍 Error in fast anomaly detection: {str(e)}")
            return {
                "success": False,
                "error": f"Anomali tespiti sırasında hata: {str(e)}",
                "anomalies_found": False,
                "anomaly_count": 0
            }

    def analyze_csv_with_samples(self, csv_content: str, delimiter: str, filename: str, validation_summary: str, issues_detail: str) -> Dict[str, Any]:
        """
        Analyze CSV content with data samples for failed uploads

        Args:
            csv_content: Raw CSV content as string
            delimiter: CSV delimiter character
            filename: Name of the CSV file
            validation_summary: Summary of validation results
            issues_detail: Detailed issues found during validation

        Returns:
            Dictionary with detailed analysis and recommendations
        """
        try:
            import pandas as pd
            import io

            print(f"DEBUG: Starting CSV analysis for {filename}")
            print(f"DEBUG: CSV content length: {len(csv_content)}")
            print(f"DEBUG: Delimiter: '{delimiter}'")

            # Parse CSV content
            df = pd.read_csv(io.StringIO(csv_content), delimiter=delimiter)
            print(f"DEBUG: Successfully parsed CSV. Shape: {df.shape}")
            print(f"DEBUG: Columns: {list(df.columns)}")

            # Get data samples
            head_sample = df.head(10).to_string(index=False, max_cols=None, max_colwidth=50)
            print(f"DEBUG: Head sample created, length: {len(head_sample)}")

            # Get random sample (limited for token efficiency)
            sample_size = min(15, len(df))
            if len(df) > 10:
                random_sample = df.sample(n=sample_size, random_state=42).to_string(index=False, max_cols=None, max_colwidth=50)
                print(f"DEBUG: Random sample created, size: {sample_size}, length: {len(random_sample)}")
            else:
                random_sample = "Dosya çok küçük, random sample alınamadı."
                print("DEBUG: File too small for random sampling")

            # Basic file info
            file_info = f"""
            DOSYA BİLGİLERİ:
            - Dosya adı: {filename}
            - Satır sayısı: {len(df):,}
            - Sütun sayısı: {len(df.columns)}
            - Sütunlar: {', '.join(df.columns)}

            VALIDATION SONUCU:
            {validation_summary}

            TESPIT EDİLEN HATALAR:
            {issues_detail}

            VERİ ÖRNEKLERİ:

            === İLK 10 SATIR ===
            {head_sample}

            === RANDOM {sample_size} SATIR ÖRNEĞİ ===
            {random_sample}
            """

            print(f"DEBUG: File info prepared, length: {len(file_info)}")
            print(f"DEBUG: Analysis successful for {filename}")

            return {
                'success': True,
                'analysis': file_info,
                'recommendations': 'Yukarıdaki gerçek veri örneklerini inceleyerek spesifik düzeltme önerileri sunun.'
            }

        except Exception as e:
            logger.error(f"Error analyzing CSV samples: {str(e)}")
            return {
                'success': False,
                'error': f'CSV analizi sırasında hata: {str(e)}',
                'analysis': validation_summary + issues_detail
            }
        
    def analyze_successful_upload(self, chat_id: str, table_name: str = None) -> Dict[str, Any]:
        """
        Analyze successfully uploaded CSV file with detailed business insights

        Args:
            chat_id: Chat ID to check for uploaded CSV
            table_name: Optional specific table name to analyze

        Returns:
            Dictionary with detailed analysis and business insights
        """
        try:
            processor = get_connector()

            # NEW: Find the latest target table for this chat
            if table_name:
                target_table = processor.get_table_info(self.user_id, table_name)
            else:
                target_table = self._get_latest_chat_table(chat_id, processor)

            if not target_table:
                return {
                    'success': False,
                    'error': 'No CSV data found for this chat.',
                    'analysis': None
                }

            # Get all data for comprehensive analysis
            all_data = processor.get_sample_data(self.user_id, target_table['table_name'], limit=None)

            if not all_data or len(all_data) == 0:
                return {
                    'success': False,
                    'error': 'No data found in the uploaded CSV table.',
                    'analysis': None
                }

            # Convert to DataFrame for analysis
            import pandas as pd
            df = pd.DataFrame(all_data)

            # Basic statistics
            total_rows = len(df)
            total_cols = len(df.columns)

            # Business insights
            insights = []

            # Detect and analyze different column types
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            text_cols = df.select_dtypes(include=['object']).columns.tolist()

            # ID columns analysis
            id_patterns = ['id', 'customer', 'müşteri', 'user', 'kullanıcı', 'account', 'hesap']
            id_cols = [col for col in df.columns if any(pattern in col.lower() for pattern in id_patterns)]

            for id_col in id_cols:
                unique_count = df[id_col].nunique()
                insights.append(f"Benzersiz {id_col}: {unique_count:,}")

            # Amount/Price analysis
            amount_patterns = ['price', 'fiyat', 'amount', 'tutar', 'total', 'toplam', 'value', 'değer', 'revenue', 'gelir']
            amount_cols = [col for col in numeric_cols if any(pattern in col.lower() for pattern in amount_patterns)]

            for amount_col in amount_cols:
                total_amount = df[amount_col].sum()
                avg_amount = df[amount_col].mean()
                max_amount = df[amount_col].max()
                min_amount = df[amount_col].min()
                insights.append(f"{amount_col} - Toplam: {total_amount:,.2f}, Ortalama: {avg_amount:,.2f}, En yüksek: {max_amount:,.2f}, En düşük: {min_amount:,.2f}")

            # Quantity analysis
            qty_patterns = ['quantity', 'miktar', 'count', 'sayı', 'adet']
            qty_cols = [col for col in numeric_cols if any(pattern in col.lower() for pattern in qty_patterns)]

            for qty_col in qty_cols:
                total_qty = df[qty_col].sum()
                avg_qty = df[qty_col].mean()
                insights.append(f"{qty_col} - Toplam: {total_qty:,}, Ortalama: {avg_qty:.2f}")

            # Category analysis (top values)
            category_patterns = ['product', 'ürün', 'category', 'kategori', 'type', 'tip', 'service', 'hizmet']
            category_cols = [col for col in text_cols if any(pattern in col.lower() for pattern in category_patterns)]

            for cat_col in category_cols:
                top_categories = df[cat_col].value_counts().head(3)
                insights.append(f"{cat_col} - En çok: {top_categories.index[0]} ({top_categories.iloc[0]} adet)")

            # Location analysis
            location_patterns = ['city', 'şehir', 'region', 'bölge', 'location', 'konum']
            location_cols = [col for col in text_cols if any(pattern in col.lower() for pattern in location_patterns)]

            for loc_col in location_cols:
                top_locations = df[loc_col].value_counts().head(3)
                insights.append(f"{loc_col} - En aktif: {top_locations.index[0]} ({top_locations.iloc[0]} kayıt)")

            # Date analysis
            date_patterns = ['date', 'tarih', 'time', 'zaman']
            date_cols = [col for col in df.columns if any(pattern in col.lower() for pattern in date_patterns)]

            for date_col in date_cols:
                try:
                    df[date_col] = pd.to_datetime(df[date_col])
                    min_date = df[date_col].min()
                    max_date = df[date_col].max()
                    insights.append(f"{date_col} - Tarih aralığı: {min_date.strftime('%Y-%m-%d')} - {max_date.strftime('%Y-%m-%d')}")
                except:
                    pass

            return {
                'success': True,
                'analysis': {
                    'total_rows': total_rows,
                    'total_cols': total_cols,
                    'columns': list(df.columns),
                    'numeric_columns': numeric_cols,
                    'text_columns': text_cols,
                    'insights': insights
                }
            }

        except Exception as e:
            logger.error(f"Error analyzing successful upload for chat {chat_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Analysis error: {str(e)}',
                'analysis': None
            }

    def validate_uploaded_csv(self, chat_id: str, table_name: str = None) -> Dict[str, Any]:
        """
        Validate the most recently uploaded CSV file for a chat
        
        Args:
            chat_id: Chat ID to check for uploaded CSV
            table_name: Optional specific table name to validate
            
        Returns:
            Dictionary with validation results
        """
        try:
            # Get the processor to access uploaded data
            processor = asyncio.run(get_connector())

            # NEW: Get the latest table for this chat instead of using fixed table name
            target_table = self._get_latest_chat_table(chat_id, processor)

            if not target_table:
                return {
                    'success': False,
                    'error': 'No CSV data found for this chat. Please upload a CSV file first.',
                    'validation_result': None
                }

            # Use the actual table name from the latest table
            table_name = target_table.get('name')
            
            # Get sample data to reconstruct CSV for validation (reduced for LLM token limits)
            sample_data = processor.get_sample_data(self.user_id, table_name, limit=50)
            
            if not sample_data or len(sample_data) == 0:
                return {
                    'success': False,
                    'error': 'No data found in the uploaded CSV table.',
                    'validation_result': None
                }
            
            # Convert sample data back to CSV format for validation
            import pandas as pd
            df = pd.DataFrame(sample_data)
            
            # Create CSV content string
            csv_content = df.to_csv(index=False)
            
            # Validate the CSV content
            validation_result = validate_csv_content(csv_content, ',', f"{table_name}.csv")
            
            return {
                'success': True,
                'validation_result': {
                    'is_valid': validation_result.is_valid,
                    'should_save': validation_result.should_save,
                    'overall_level': validation_result.overall_level.value,
                    'summary': validation_result.summary,
                    'stats': validation_result.stats,
                    'issues': [
                        {
                            'level': issue.level.value,
                            'column': issue.column,
                            'message': issue.message,
                            'count': issue.count,
                            'percentage': issue.percentage,
                            'examples': issue.examples
                        }
                        for issue in validation_result.issues
                    ]
                },
                'table_info': target_table
            }
            
        except Exception as e:
            logger.error(f"Error validating CSV for chat {chat_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Validation error: {str(e)}',
                'validation_result': None
            }
    
    def get_csv_statistics(self, chat_id: str, table_name: str = None) -> Dict[str, Any]:
        """
        Get detailed statistics about uploaded CSV data
        
        Args:
            chat_id: Chat ID
            table_name: Optional specific table name
            
        Returns:
            Dictionary with CSV statistics
        """
        try:
            processor = asyncio.run(get_connector())

            # NEW: Get the latest table for this chat instead of using fixed table name
            if not table_name:
                target_table = self._get_latest_chat_table(chat_id, processor)
                if not target_table:
                    return {
                        'success': False,
                        'error': 'No CSV data found for this chat.'
                    }
                table_name = target_table.get('name')

            # Get table statistics
            stats = processor.get_table_statistics(self.user_id, table_name)
            
            if not stats:
                return {
                    'success': False,
                    'error': 'No statistics available for this CSV data.'
                }
            
            return {
                'success': True,
                'statistics': stats
            }
            
        except Exception as e:
            logger.error(f"Error getting CSV statistics for chat {chat_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Statistics error: {str(e)}'
            }
    
    def validate_csv_content_direct(self, csv_content: str, delimiter: str = ',', filename: str = 'data.csv') -> Dict[str, Any]:
        """
        Directly validate CSV content string
        
        Args:
            csv_content: CSV content as string
            delimiter: CSV delimiter
            filename: Filename for context
            
        Returns:
            Dictionary with validation results
        """
        try:
            validation_result = validate_csv_content(csv_content, delimiter, filename)
            
            return {
                'success': True,
                'validation_result': {
                    'is_valid': validation_result.is_valid,
                    'should_save': validation_result.should_save,
                    'overall_level': validation_result.overall_level.value,
                    'summary': validation_result.summary,
                    'stats': validation_result.stats,
                    'issues': [
                        {
                            'level': issue.level.value,
                            'column': issue.column,
                            'message': issue.message,
                            'count': issue.count,
                            'percentage': issue.percentage,
                            'examples': issue.examples
                        }
                        for issue in validation_result.issues
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Error validating CSV content: {str(e)}")
            return {
                'success': False,
                'error': f'Validation error: {str(e)}',
                'validation_result': None
            }
    
    def get_simple_csv_summary(self, chat_id: str, table_name: str = None) -> Dict[str, Any]:
        """
        Get simple CSV summary for upload confirmation

        Args:
            chat_id: Chat ID
            table_name: Optional specific table name

        Returns:
            Dictionary with simple summary
        """
        try:
            processor = asyncio.run(get_connector())

            # NEW: Get the latest table for this chat instead of using fixed table name
            target_table = self._get_latest_chat_table(chat_id, processor)

            if not target_table:
                return {
                    'success': False,
                    'error': 'CSV verisi bulunamadı.'
                }

            # Use the actual table name from the latest table
            table_name = target_table.get('name')

            # Get sample data to get column names
            sample_data = processor.get_sample_data(self.user_id, table_name, limit=5)

            if not sample_data or len(sample_data) == 0:
                return {
                    'success': False,
                    'error': 'CSV verisi boş.'
                }

            # Get column names
            column_names = list(sample_data[0].keys()) if sample_data else []

            # Get row count from table metadata (this is the correct row count)
            row_count = target_table.get('row_count', 0)

            return {
                'success': True,
                'summary': {
                    'row_count': row_count,
                    'column_count': len(column_names),
                    'column_names': column_names,
                    'table_name': table_name
                }
            }

        except Exception as e:
            logger.error(f"Error getting simple CSV summary for chat {chat_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Özet hatası: {str(e)}'
            }

    def format_validation_report(self, validation_result: Dict[str, Any]) -> str:
        """
        Format validation results into a user-friendly report
        
        Args:
            validation_result: Validation result dictionary
            
        Returns:
            Formatted report string
        """
        if not validation_result.get('success', False):
            return f"❌ Validation hatası: {validation_result.get('error', 'Bilinmeyen hata')}"
        
        result = validation_result['validation_result']
        
        # Start with summary
        report = f"{result['summary']}\n\n"
        
        # Add statistics
        stats = result.get('stats', {})
        report += "📊 **Dosya İstatistikleri:**\n"
        report += f"• Toplam satır: {stats.get('total_rows', 'N/A')}\n"
        report += f"• Toplam sütun: {stats.get('total_columns', 'N/A')}\n"
        report += f"• Hata sayısı: {stats.get('error_count', 0)}\n"
        report += f"• Uyarı sayısı: {stats.get('warning_count', 0)}\n\n"
        
        # Add issues if any
        issues = result.get('issues', [])
        if issues:
            report += "🔍 **Tespit Edilen Sorunlar:**\n"
            
            for issue in issues:
                level_icon = {
                    'error': '❌',
                    'warning': '⚠️',
                    'success': '✅'
                }.get(issue['level'], '•')
                
                report += f"{level_icon} "
                
                if issue['column']:
                    report += f"**{issue['column']}**: "
                
                report += issue['message']
                
                if issue.get('percentage'):
                    report += f" ({issue['percentage']:.1f}%)"
                
                if issue.get('count'):
                    report += f" ({issue['count']} kayıt)"
                
                if issue.get('examples'):
                    report += f" - Örnekler: {', '.join(map(str, issue['examples']))}"
                
                report += "\n"
        
        return report

    def validate_csv_data(self, **kwargs) -> Dict[str, Any]:
        """
        Comprehensive CSV analysis: validation + profiling + statistics in one call.

        Performs:
        1. CSV validation and quality checks
        2. Quick data profiling using ydata-profiling
        3. Basic statistical analysis
        4. Data quality assessment and recommendations

        Args:
            chat_id: Chat identifier to get the latest table
            user_id: User identifier (injected by context manager)

        Returns:
            Dictionary containing comprehensive analysis results
        """
        try:
            print(f"🔍 Starting comprehensive CSV analysis...")
            print(f"🔍 Method received kwargs: {kwargs}")

            # Extract parameters from kwargs
            chat_id = kwargs.get('chat_id')
            user_id = kwargs.get('user_id')

            # If not in kwargs, try to get from UserContextManager
            if not chat_id or not user_id:
                try:
                    from src.agents.agno_agents.user_context_manager import UserContextManager
                    context_manager = UserContextManager()
                    context_chat_id = context_manager.get_current_chat_id()
                    context_user_id = context_manager.get_current_user_id()
                    chat_id = chat_id or context_chat_id
                    user_id = user_id or context_user_id
                    print(f"🔍 Got from context: chat_id={chat_id}, user_id={user_id}")
                except Exception as e:
                    print(f"🔍 Could not get context: {e}")

            # Use instance user_id as fallback
            user_id = user_id or self.user_id

            print(f"🔍 Final values: chat_id={chat_id}, user_id={user_id}")
            print(f"🔍 Types: chat_id type={type(chat_id)}, user_id type={type(user_id)}")

            # Use provided user_id or fallback to instance user_id
            effective_user_id = user_id
            if not effective_user_id:
                return {
                    "success": False,
                    "error": "User ID bulunamadı. Analiz yapılamıyor."
                }

            print(f"🔍 Analysis for user_id: {effective_user_id}, chat_id: {chat_id}")

            # Check if chat_id is provided
            if not chat_id:
                return {
                    "success": False,
                    "error": "Chat ID bulunamadı. Analiz yapılamıyor."
                }

            # Get database processor
            from src.rag_engine.analitic.processor import PostgreSQLProcessor
            processor = PostgreSQLProcessor()

            # Get the latest table for this chat
            latest_table = self._get_latest_chat_table_with_user(chat_id, effective_user_id, processor)
            if not latest_table:
                return {
                    "success": False,
                    "error": f"Chat {chat_id} için CSV verisi bulunamadı. Önce bir CSV dosyası yükleyin."
                }

            table_name = latest_table['name']
            print(f"🔍 Quick profiling for table: {table_name}")

            # Get data from database
            import pandas as pd
            from src.rag_engine.analitic.db.postgresql.database import get_session
            from sqlalchemy import text

            with get_session() as session:
                # Read data into DataFrame
                query = f"SELECT * FROM {table_name} LIMIT 10000"  # Limit for performance
                df = pd.read_sql(text(query), session.connection())

                if df.empty:
                    return {
                        "success": False,
                        "error": f"Tablo {table_name} boş görünüyor."
                    }

                print(f"📊 Analyzing {df.shape[0]} rows, {df.shape[1]} columns")

                # 1. Basic validation
                validation_issues = []
                if df.empty:
                    validation_issues.append("Veri seti boş")
                if df.shape[1] == 0:
                    validation_issues.append("Hiç sütun yok")
                if df.columns.duplicated().any():
                    validation_issues.append("Tekrarlanan sütun isimleri var")

                # 2. Data quality assessment
                missing_data = df.isnull().sum()
                missing_percentage = (missing_data / len(df) * 100).round(2)
                duplicate_rows = df.duplicated().sum()

                # 3. Data types analysis
                data_types = df.dtypes.astype(str).to_dict()
                numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
                categorical_columns = df.select_dtypes(include=['object', 'category']).columns.tolist()
                datetime_columns = df.select_dtypes(include=['datetime']).columns.tolist()

                # 4. Enhanced Statistical Analysis
                basic_stats = {}

                # Enhanced numeric statistics
                if numeric_columns:
                    # Standard pandas describe
                    numeric_stats = df[numeric_columns].describe().to_dict()

                    # Add advanced statistics
                    for col in numeric_columns:
                        col_data = df[col].dropna()
                        if len(col_data) > 0:
                            # Add skewness and kurtosis
                            numeric_stats[col]['skewness'] = float(col_data.skew())
                            numeric_stats[col]['kurtosis'] = float(col_data.kurtosis())

                            # Add percentiles
                            numeric_stats[col]['p5'] = float(col_data.quantile(0.05))
                            numeric_stats[col]['p95'] = float(col_data.quantile(0.95))
                            numeric_stats[col]['iqr'] = float(col_data.quantile(0.75) - col_data.quantile(0.25))

                            # Add variance and coefficient of variation
                            numeric_stats[col]['variance'] = float(col_data.var())
                            if col_data.mean() != 0:
                                numeric_stats[col]['cv'] = float(col_data.std() / abs(col_data.mean()))
                            else:
                                numeric_stats[col]['cv'] = None

                    # Convert any non-serializable values to strings
                    for col, stats in numeric_stats.items():
                        for stat_name, stat_value in stats.items():
                            if hasattr(stat_value, 'isoformat'):  # datetime/date objects
                                numeric_stats[col][stat_name] = stat_value.isoformat()
                            elif pd.isna(stat_value):
                                numeric_stats[col][stat_name] = None
                            else:
                                numeric_stats[col][stat_name] = float(stat_value) if isinstance(stat_value, (int, float)) else str(stat_value)

                    basic_stats["numeric"] = numeric_stats

                # Enhanced categorical statistics
                if categorical_columns:
                    cat_stats = {}
                    for col in categorical_columns:
                        col_data = df[col].dropna()
                        value_counts = df[col].value_counts()

                        most_frequent = df[col].mode().iloc[0] if not df[col].mode().empty else None
                        # Convert date/datetime to string if needed
                        if hasattr(most_frequent, 'isoformat'):
                            most_frequent = most_frequent.isoformat()
                        elif pd.isna(most_frequent):
                            most_frequent = None
                        else:
                            most_frequent = str(most_frequent)

                        # Calculate entropy (diversity measure)
                        entropy = 0
                        if len(col_data) > 0:
                            probabilities = value_counts / len(col_data)
                            entropy = -sum(p * np.log2(p) for p in probabilities if p > 0)

                        cat_stats[col] = {
                            "unique_count": int(df[col].nunique()),
                            "most_frequent": most_frequent,
                            "frequency": int(value_counts.iloc[0]) if not value_counts.empty else 0,
                            "frequency_percentage": float(value_counts.iloc[0] / len(df) * 100) if not value_counts.empty and len(df) > 0 else 0,
                            "entropy": float(entropy),
                            "cardinality_ratio": float(df[col].nunique() / len(df)) if len(df) > 0 else 0,
                            "top_5_values": {str(k): int(v) for k, v in value_counts.head(5).items()}
                        }
                    basic_stats["categorical"] = cat_stats

                # 5. Generate recommendations
                recommendations = []

                # Validation issues
                if validation_issues:
                    recommendations.extend([f"🚨 {issue}" for issue in validation_issues])

                # Missing values recommendations
                high_missing = [col for col, pct in missing_percentage.items() if pct > 50]
                if high_missing:
                    recommendations.append(f"⚠️ Yüksek eksik değer oranı: {', '.join(high_missing)} sütunlarında %50'den fazla eksik değer var.")

                medium_missing = [col for col, pct in missing_percentage.items() if 10 < pct <= 50]
                if medium_missing:
                    recommendations.append(f"⚠️ Orta seviye eksik değer: {', '.join(medium_missing)} sütunlarında %10-50 arası eksik değer var.")

                # Duplicate rows recommendation
                if duplicate_rows > 0:
                    recommendations.append(f"🔄 {duplicate_rows} adet tekrarlanan satır tespit edildi.")

                # Data type recommendations
                potential_numeric = []
                for col in categorical_columns:
                    if df[col].dropna().astype(str).str.replace('.', '').str.replace('-', '').str.isdigit().all():
                        potential_numeric.append(col)
                if potential_numeric:
                    recommendations.append(f"🔢 Bu sütunlar sayısal olabilir: {', '.join(potential_numeric)}")

                # Memory usage recommendation
                memory_mb = round(df.memory_usage(deep=True).sum() / 1024 / 1024, 2)
                if memory_mb > 100:
                    recommendations.append(f"💾 Yüksek bellek kullanımı: {memory_mb} MB - veri optimizasyonu önerilir.")

                if not recommendations:
                    recommendations.append("✅ Veri kalitesi genel olarak iyi görünüyor.")

                # 6. Fast Anomaly Detection
                print(f"🔍 Running fast anomaly detection...")
                anomaly_results = self._run_fast_anomaly_detection(df)

                # Add anomaly recommendations
                if anomaly_results.get("success") and anomaly_results.get("anomalies_found"):
                    anomaly_count = anomaly_results.get("anomaly_count", 0)
                    anomaly_percentage = anomaly_results.get("anomaly_percentage", 0)
                    recommendations.append(f"🚨 {anomaly_count} adet anomali tespit edildi (%{anomaly_percentage:.1f})")
                    recommendations.append("💡 Anomalileri inceleyip silmek ister misiniz?")

                # Build comprehensive summary
                summary = {
                    "success": True,
                    "analysis_type": "comprehensive_csv_analysis",
                    "table_info": {
                        "name": table_name,
                        "original_file": latest_table.get('original_file_name', 'Unknown'),
                        "rows": df.shape[0],
                        "columns": df.shape[1],
                        "memory_usage_mb": memory_mb,
                        "column_types": {
                            "numeric": len(numeric_columns),
                            "categorical": len(categorical_columns),
                            "datetime": len(datetime_columns)
                        }
                    },
                    "validation": {
                        "is_valid": len(validation_issues) == 0,
                        "issues": validation_issues
                    },
                    "data_quality": {
                        "missing_values": {k: int(v) for k, v in missing_data.to_dict().items()},
                        "missing_percentage": {k: float(v) for k, v in missing_percentage.to_dict().items()},
                        "duplicate_rows": int(duplicate_rows),
                        "data_types": data_types,
                        "column_classification": {
                            "numeric_columns": numeric_columns,
                            "categorical_columns": categorical_columns,
                            "datetime_columns": datetime_columns
                        }
                    },
                    "basic_statistics": basic_stats,
                    "anomaly_detection": anomaly_results,
                    "recommendations": recommendations
                }

                print(f"✅ Comprehensive CSV analysis completed for {table_name}")
                print(f"📊 Analysis summary: {df.shape[0]} rows, {df.shape[1]} columns, {len(recommendations)} recommendations")
                return summary

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ Quick profiling error: {str(e)}")
            print(f"Error details: {error_details}")
            return {
                "success": False,
                "error": f"Profiling sırasında hata oluştu: {str(e)}"
            }

def get_csv_validation_toolkit(user_id: str = None) -> CSVValidationToolkit:
    """Factory function to create CSV validation toolkit"""
    toolkit = CSVValidationToolkit()
    toolkit.user_id = user_id  # Set user_id after instantiation
    return toolkit
