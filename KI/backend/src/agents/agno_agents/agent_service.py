import json
import re
from typing import Dict, Any, List, Optional, <PERSON>ple
import asyncio
import concurrent.futures
import threading
from functools import partial

from src.agents.agno_agents.agent_factory import AgentFactory, PlanerOutputFormat, PlannedAgent, OutputAgentOutputFormat
from src.agents.agno_agents.user_context_manager import UserContextManager
from src.agents.agno_agents.tools.database import DatabaseToolkit
from agno.agent import Agent # For type hinting
from src.api.routes.chats.websocket import ConnectionManager
from src.api.routes.chats.models import Task, TaskStatus, TaskMetadata
import time
import os
from src.agents.agno_agents.utils import generate_csv_from_sql_query, generate_excel_from_sql_query



class AgentService:
    def __init__(self, factory: AgentFactory):
        self.factory = factory
        self.planner_agent: Optional[Agent] = factory.get_planner_agent()
        self.output_agent: Optional[Agent] = factory.get_output_agent()
        
        # Thread pool for CPU-bound agent operations
        self.agent_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=10,  # Adjust based on your server capacity
            thread_name_prefix="agent_worker"
        )
        
        # Semaphore to limit concurrent agent executions per user
        self.user_semaphores = {}
        self.semaphore_lock = asyncio.Lock()

    async def _get_user_semaphore(self, user_id: str) -> asyncio.Semaphore:
        """Get or create a semaphore for rate limiting per user"""
        async with self.semaphore_lock:
            if user_id not in self.user_semaphores:
                # Limit each user to 3 concurrent agent operations
                self.user_semaphores[user_id] = asyncio.Semaphore(3)
            return self.user_semaphores[user_id]

    async def _run_agent_async(
        self, 
        agent: Agent, 
        request_str: str, 
        user_id: str, 
        session_id: str
    ) -> Any:
        """
        Async wrapper for agent.run() to avoid blocking the event loop
        Uses ThreadPoolExecutor to run agent in separate thread with secure user context
        """
        loop = asyncio.get_event_loop()
        
        def run_agent_with_context():
            """Run agent with proper user context set"""
            context_manager = UserContextManager()
            
            print(f"🤖 AgentService: Starting agent execution in thread {threading.current_thread().ident}")
            print(f"🤖 AgentService: Agent name: {getattr(agent, 'name', 'unknown')}")
            print(f"🤖 AgentService: user_id={user_id}, session_id={session_id}")
            
            # Set user context for secure tool execution
            with context_manager.user_context(user_id=user_id, chat_id=session_id):
                print(f"🤖 AgentService: User context set - starting agent.run()")
                try:
                    result = agent.run(
                        request_str,
                        user_id=user_id,
                        session_id=session_id
                    )
                    print(f"🤖 AgentService: Agent execution completed successfully")
                    return result
                except Exception as e:
                    print(f"❌ AgentService: Agent execution failed: {e}")
                    raise
        
        # Run agent in thread pool to avoid blocking event loop
        try:
            result = await loop.run_in_executor(
                self.agent_executor,
                run_agent_with_context
            )
            return result
        except Exception as e:
            print(f"Error in async agent execution: {e}")
            raise

    async def _run_multiple_agents_concurrent(
        self,
        agent_tasks: List[Dict[str, Any]],
        user_query: str,
        user_id: str,
        chat_id: str,
        task_key: str,
        socket_manager: ConnectionManager,
        agent_outputs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute multiple agents concurrently when possible
        """
        # For now, we'll keep sequential execution for dependency management
        # But this method can be enhanced to run independent agents concurrently
        
        concurrent_tasks = []
        sequential_tasks = []
        
        # Simple heuristic: agents that don't depend on others can run concurrently
        # For advanced implementation, analyze dependencies in agent tasks
        
        for i, task_item in enumerate(agent_tasks):
            if not isinstance(task_item, PlannedAgent):
                if isinstance(task_item, dict):
                    try:
                        task_item = PlannedAgent(**task_item)
                    except Exception as e:
                        continue
                else:
                    continue
            
            # For now, treat all as sequential to maintain data flow integrity
            # This can be optimized based on actual agent dependencies
            sequential_tasks.append((i, task_item))
        
        # Execute sequential tasks (current behavior)
        for i, task_item in sequential_tasks:
            await self._execute_single_agent(
                task_item, i, user_query, user_id, chat_id, 
                task_key, socket_manager, agent_outputs
            )
        
        return agent_outputs

    async def _execute_single_agent(
        self,
        task_item: PlannedAgent,
        index: int,
        user_query: str,
        user_id: str,
        chat_id: str,
        task_key: str,
        socket_manager: ConnectionManager,
        agent_outputs: Dict[str, Any]
    ):
        """Execute a single agent with proper async handling and user rate limiting"""
        
        # Get user semaphore for rate limiting
        user_semaphore = await self._get_user_semaphore(user_id)
        
        async with user_semaphore:  # Rate limit per user
            agent_id_from_plan = task_item.agent_id
            agent_name_for_log = task_item.agent_name
            agent_task_description = task_item.task_for_agent
            reason_for_agent = task_item.reason

            current_agent = self.factory.get_agent(agent_id_from_plan)

            if current_agent:
                try:
                    # Send RUNNING update for this agent
                    await socket_manager.send_task_update(
                        task=Task(
                            task_key=task_key,
                            user_id=user_id,
                            chat_id=chat_id,
                            status=TaskStatus.EXECUTING,
                            current_task=agent_task_description,
                            title=f"Agent: {agent_name_for_log}",
                            input_text=user_query,
                            metadata=TaskMetadata(
                                started_at=int(time.time()),
                                completed_at=None
                            )
                        )
                    )

                    # Prepare request for the agent
                    user_message = self._get_user_message(user_query, user_id)
                    request_details = {
                        "original_user_query": user_message,
                        "assigned_task": agent_task_description,
                        "reason_for_assignment": reason_for_agent,
                        "available_outputs_from_previous_agents": agent_outputs
                    }
                    request_str = json.dumps(request_details, ensure_ascii=False, indent=2)

                    # Use async agent execution
                    agent_run_response = await self._run_agent_async(
                        current_agent, 
                        request_str, 
                        user_id, 
                        chat_id
                    )
                    
                    # Try to get content from response
                    response_content = None
                    if hasattr(agent_run_response, "content"):
                        response_content = agent_run_response.content
                    else:
                        response_content = agent_run_response.to_dict().get("content")

                    # Apply security filtering to agent output before storing
                    if response_content and isinstance(response_content, str):
                        response_content = self.apply_security_filtering(response_content)
                        print(f"🔒 Applied security filtering to {agent_name_for_log} output")

                    agent_outputs[agent_id_from_plan] = response_content

                    # Send COMPLETED update for this agent
                    await socket_manager.send_task_update(
                        task=Task(
                            task_key=task_key,
                            user_id=user_id,
                            chat_id=chat_id,
                            status=TaskStatus.ROUTING,
                            current_task=agent_task_description,
                            title=f"Agent: {agent_name_for_log}",
                            input_text=user_query,
                            output={"content": response_content},
                            metadata=TaskMetadata(
                                started_at=int(time.time()),
                                completed_at=int(time.time())
                            )
                        )
                    )
                except Exception as e:
                    print(f"Error during {current_agent.name} execution: {e}")
                    agent_outputs[agent_id_from_plan] = f"Error executing task with {agent_name_for_log} (ID: {agent_id_from_plan}): {e}"
                    # Send FAILED update for this agent
                    await socket_manager.send_task_update(
                        task=Task(
                            task_key=task_key,
                            user_id=user_id,
                            chat_id=chat_id,
                            status=TaskStatus.FAILED,
                            current_task=agent_task_description,
                            title=f"Agent: {agent_name_for_log}",
                            input_text=user_query,
                            error=str(e),
                            metadata=TaskMetadata(
                                started_at=int(time.time()),
                                completed_at=int(time.time())
                            )
                        )
                    )
            else:
                print(f"Warning: Agent with ID '{agent_id_from_plan}' not found. Skipping task.")
                agent_outputs[agent_id_from_plan] = f"Error: Agent with ID '{agent_id_from_plan}' not found."
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.FAILED,
                        current_task=agent_task_description,
                        title=f"Agent: {agent_name_for_log}",
                        input_text=user_query,
                        error=f"Agent with ID '{agent_id_from_plan}' not found.",
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=int(time.time())
                        )
                    )
                )

    def _is_chart_data_empty(self, chart_data: Dict[str, Any]) -> bool:
        """
        Check if chart data fields are empty and need to be populated.
        
        Args:
            chart_data: The parsed chart schema dictionary
            
        Returns:
            True if data fields are empty, False otherwise
        """
        try:
            data_section = chart_data.get("data", {})
            if not data_section:
                return True
                
            labels = data_section.get("labels", [])
            datasets = data_section.get("datasets", [])
            
            # Check if labels are empty
            if not labels or len(labels) == 0:
                print(f"📊 Chart data check: Labels are empty")
                return True
                
            # Check if any dataset has empty data
            if not datasets or len(datasets) == 0:
                print(f"📊 Chart data check: No datasets found")
                return True
                
            for i, dataset in enumerate(datasets):
                dataset_data = dataset.get("data", [])
                if not dataset_data or len(dataset_data) == 0:
                    print(f"📊 Chart data check: Dataset {i} has empty data")
                    return True
                    
            print(f"📊 Chart data check: Data appears populated")
            return False
            
        except Exception as e:
            print(f"❌ Error checking chart data: {e}")
            return True  # Assume empty if we can't check
    
    async def _populate_chart_data(self, chart_schema: Dict[str, Any], data_sql: str, user_id: str) -> Dict[str, Any]:
        """
        Populate empty chart data using SQL query results.
        
        Args:
            chart_schema: The chart schema with empty data fields
            data_sql: SQL query to get the data
            user_id: User ID for secure database access
            
        Returns:
            Chart schema with populated data
        """
        try:
            print(f"📊 Populating chart data with SQL: {data_sql}")
            
            # Initialize database toolkit with secure context
            db_toolkit = DatabaseToolkit()
            
            # Execute SQL query to get data
            query_result = db_toolkit.agregate_data(sql_query=data_sql, user_id=user_id)
            
            if not query_result or not query_result.get("success"):
                error_msg = query_result.get("error", "Unknown error") if query_result else "No result returned"
                print(f"❌ SQL query failed: {error_msg}")
                return chart_schema  # Return original schema if query fails
                
            # Extract data from query result
            sql_data = query_result.get("data", {})
            sql_rows = sql_data.get("rows", [])
            sql_columns = sql_data.get("columns", [])
            
            if not sql_rows or not sql_columns:
                print(f"⚠️ SQL query returned no data or columns")
                return chart_schema
                
            print(f"📊 SQL returned {len(sql_rows)} rows with columns: {sql_columns}")
            
            # Ensure chart has data section
            if "data" not in chart_schema:
                chart_schema["data"] = {}
                
            data_section = chart_schema["data"]
            
            # Populate labels from first column
            if len(sql_columns) > 0:
                label_column = sql_columns[0]
                labels = [row.get(label_column, '') for row in sql_rows]
                data_section["labels"] = labels
                print(f"📊 Populated {len(labels)} labels from column '{label_column}'")
            
            # Ensure datasets exist
            if "datasets" not in data_section:
                data_section["datasets"] = []
                
            datasets = data_section["datasets"]
            
            # Create datasets for remaining columns (skip first column used for labels)
            data_columns = sql_columns[1:] if len(sql_columns) > 1 else []
            
            # If no datasets exist, create them
            if not datasets and data_columns:
                for i, column in enumerate(data_columns):
                    dataset = {
                        "label": column.replace('_', ' ').title(),  # Beautify column name
                        "data": [],
                        "backgroundColor": f"rgba({75 + i*50}, {192 - i*30}, {192 + i*20}, 0.6)",
                        "borderColor": f"rgba({75 + i*50}, {192 - i*30}, {192 + i*20}, 1)",
                        "borderWidth": 1
                    }
                    datasets.append(dataset)
                    
            # Populate data for each dataset
            for i, dataset in enumerate(datasets):
                if i < len(data_columns):
                    column_name = data_columns[i]
                    column_data = [row.get(column_name, 0) for row in sql_rows]
                    dataset["data"] = column_data
                    print(f"📊 Populated dataset {i} with {len(column_data)} values from column '{column_name}'")
                    
            print(f"✅ Successfully populated chart data")
            return chart_schema
            
        except Exception as e:
            print(f"❌ Error populating chart data: {e}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return chart_schema  # Return original schema on error

    def apply_security_filtering(self, content: str) -> str:
        """Apply security filtering to remove sensitive information"""
        if not content:
            return content

        # Security patterns to filter - improved patterns
        security_patterns = [
            # User ID patterns - more comprehensive
            (r'user_[a-f0-9-_]+_[a-f0-9]+', 'veri tablonuz'),  # Full user table pattern
            (r'user_d5d28cef_83c9_4ab7_b3db_aa772a8ed887_[a-f0-9]+', 'veri tablonuz'),  # Specific pattern
            (r'user_[a-f0-9-_]+', 'kullanıcı'),  # General user pattern
            (r'table_[a-f0-9-_]+', 'veri setiniz'),
            (r'[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', 'verileriniz'),
            (r'database|schema|table_name|user_id', 'veri sistemi'),
            ]

        # Remove <result> tags if present
        filtered_content = re.sub(r'<result>\s*', '', content, flags=re.IGNORECASE)
        filtered_content = re.sub(r'\s*</result>', '', filtered_content, flags=re.IGNORECASE)

        # Apply security pattern filtering
        for pattern, replacement in security_patterns:
            if callable(replacement):
                filtered_content = re.sub(pattern, replacement, filtered_content, flags=re.IGNORECASE)
            else:
                filtered_content = re.sub(pattern, replacement, filtered_content, flags=re.IGNORECASE)

        return filtered_content

    def _get_user_message(self, user_query: str, user_id: str) -> str:
        """
        Gets the user message from the database.
        """
        return f"""
        User query: {user_query}
        User id: {user_id}
        """

    async def run_planner(self, user_query: str, user_id: str, chat_id: str, task_key: str, socket_manager: ConnectionManager) -> Optional[PlanerOutputFormat]:
        """
        Runs the PlannerAgent with the user query.

        Args:
            user_query: The query from the user.
            user_id: The user id.
            chat_id: The chat id.
            task_key: The task key.
            socket_manager: The socket manager.
        Returns:
            A PlanerOutputFormat object representing the plan, or None if planning fails.
        """
        if not self.planner_agent:
            print("Error: Planner agent is not initialized.")
            return None

        try:

            # Send task update to the websocket
            if task_key and socket_manager:
                 await socket_manager.send_task_update(task=Task(
                    task_key=task_key,
                    user_id=user_id,
                    chat_id=chat_id,
                    status=TaskStatus.EXECUTING,
                    current_task="görev_belirleme",
                    title="Analysis Task",  # Added required title field
                    input_text=user_query,  # Added required input_text field
                    metadata=TaskMetadata(
                        started_at=int(time.time()),
                        completed_at=None
                    )
                ))

            user_message = self._get_user_message(user_query, user_id)

            # The planner_agent is configured with PlanerOutputFormat as response_model
            # So, response.content should be an instance of PlanerOutputFormat
            print(f"Running planner agent with message: {user_message}")
            print(f"Planner agent config - response_model: {getattr(self.planner_agent, 'response_model', None)}")
            print(f"Planner agent config - use_json_mode: {getattr(self.planner_agent, 'use_json_mode', None)}")
            print(f"Planner agent config - markdown: {getattr(self.planner_agent, 'markdown', None)}")
            print(f"Planner agent model: {getattr(self.planner_agent, 'model', None)}")
            print(f"Planner agent name: {getattr(self.planner_agent, 'name', None)}")

            response = await self._run_agent_async(
                self.planner_agent, 
                user_message, 
                user_id, 
                chat_id
            )
            print("*************************************************************************")
            print(f"Planner agent response type: {type(response)}")
            print(f"Planner agent response attributes: {dir(response)}")

            # Check if response has different attributes
            if hasattr(response, 'content'):
                print(f"Response.content type: {type(response.content)}")
                print(f"Response.content: {response.content}")
            if hasattr(response, 'data'):
                print(f"Response.data type: {type(response.data)}")
                print(f"Response.data: {response.data}")
            if hasattr(response, 'model_response'):
                print(f"Response.model_response type: {type(response.model_response)}")
                print(f"Response.model_response: {response.model_response}")

            # When response_model is used, response.content should be the Pydantic model instance
            plan_object = response.content
            print(f"Plan object type: {type(plan_object)}")
            print(f"Plan object content (first 500 chars): {str(plan_object)[:500]}...")

            # Validate that we have a proper PlanerOutputFormat object
            if not isinstance(plan_object, PlanerOutputFormat):
                print(f"Error: Planner output is not of type PlanerOutputFormat. Got: {type(plan_object)}")
                print(f"Raw response content (first 500 chars): {str(plan_object)[:500]}...")

                # Try to handle string responses that might be JSON or markdown
                if isinstance(plan_object, str):
                    try:
                        import json
                        import re

                        if plan_object.strip():  # Only try to parse non-empty strings
                            # First, try to extract JSON from markdown if present
                            json_content = plan_object.strip()

                            # Look for JSON blocks in markdown
                            json_pattern = r'```json\s*\n(.*?)\n```'
                            json_matches = re.findall(json_pattern, plan_object, re.DOTALL)
                            if json_matches:
                                json_content = json_matches[0].strip()
                                print(f"Found JSON in markdown block")

                            # Also try to find JSON without markdown wrapper
                            elif '{' in plan_object and '}' in plan_object:
                                # Try to extract JSON from the response
                                start_idx = plan_object.find('{')
                                end_idx = plan_object.rfind('}') + 1
                                if start_idx >= 0 and end_idx > start_idx:
                                    json_content = plan_object[start_idx:end_idx]
                                    print(f"Extracted JSON from response text")
                            # Use the extracted JSON content
                            cleaned_json = json_content.strip()
                            print(f"Attempting to parse JSON (length: {len(cleaned_json)})")

                            # Try different approaches to handle the JSON
                            parsed_json = None

                            # First, try direct parsing
                            try:
                                parsed_json = json.loads(cleaned_json)
                                print(f"✓ Successfully parsed JSON directly")
                            except json.JSONDecodeError as e:
                                print(f"Direct parsing failed: {e}")
                                print(f"Error at position: {e.pos if hasattr(e, 'pos') else 'unknown'}")

                                # Try to fix truncated JSON
                                try:
                                    # Remove any potential markdown formatting
                                    cleaned = cleaned_json.strip()
                                    if cleaned.startswith('```json'):
                                        cleaned = cleaned[7:]
                                    if cleaned.endswith('```'):
                                        cleaned = cleaned[:-3]
                                    cleaned = cleaned.strip()

                                    # Check if JSON might be truncated and try to fix it
                                    if not cleaned.endswith('}') and not cleaned.endswith(']'):
                                        print(f"JSON appears to be truncated, attempting to fix...")
                                        # Try to find the last complete object/array
                                        if '"agents":[' in cleaned:
                                            # Find the last complete agent object
                                            last_brace = cleaned.rfind('}')
                                            if last_brace > 0:
                                                # Try to close the JSON properly
                                                truncated_fixed = cleaned[:last_brace + 1] + ']}'
                                                try:
                                                    json.loads(truncated_fixed)  # Test if the fix works
                                                    cleaned = truncated_fixed
                                                    print(f"✓ Successfully fixed truncated JSON")
                                                except:
                                                    print(f"Failed to fix truncated JSON")

                                    # Try parsing the cleaned/fixed JSON
                                    parsed_json = json.loads(cleaned)
                                    print(f"✓ Successfully parsed JSON after cleaning/fixing")
                                except Exception as e2:
                                    print(f"Error: Even after cleaning, failed to parse JSON: {e2}")
                                    return None

                            if parsed_json:
                                print(f"Parsed JSON structure: {list(parsed_json.keys()) if isinstance(parsed_json, dict) else type(parsed_json)}")
                                # Create PlanerOutputFormat object
                                plan_object = PlanerOutputFormat(**parsed_json)
                                print(f"✓ Successfully created PlanerOutputFormat with {len(plan_object.agents)} agents")
                            else:
                                print(f"Error: No valid JSON could be parsed")
                                return None
                        else:
                            print(f"Error: Received empty string from planner agent")
                            return None
                    except Exception as e:
                        print(f"Error: Failed to create PlanerOutputFormat from parsed JSON: {e}")
                        if 'parsed_json' in locals():
                            print(f"Parsed JSON was: {parsed_json}")
                        return None
                else:
                    print(f"Error: Expected string or PlanerOutputFormat, got {type(plan_object)}")
                    return None

            # Ensure the plan_object has agents attribute and it's accessible
            if not hasattr(plan_object, 'agents'):
                print(f"Error: Plan object does not have 'agents' attribute")
                return None

            # VALIDATION: Check if all selected agents actually exist
            available_agents = list(self.factory.agents_config_data.get("agents", {}).keys())
            print(f"🔍 DEBUG: Available agents in system: {available_agents}")

            valid_agents = []
            for agent_task in plan_object.agents:
                agent_id = agent_task.agent_id
                if agent_id in available_agents:
                    valid_agents.append(agent_task)
                    print(f"✅ Agent '{agent_id}' is valid")
                else:
                    print(f"❌ WARNING: Planner selected non-existent agent '{agent_id}'. Skipping.")
                    print(f"   Available agents: {available_agents}")

            # Update plan with only valid agents
            if len(valid_agents) != len(plan_object.agents):
                print(f"🔧 Filtered {len(plan_object.agents)} -> {len(valid_agents)} valid agents")
                plan_object.agents = valid_agents

            if len(valid_agents) == 0:
                print(f"❌ ERROR: No valid agents selected by planner!")
                return None

            if task_key and socket_manager:
                try:
                    agents_count = len(plan_object.agents) if plan_object.agents else 0
                    await socket_manager.send_task_update(task= Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.ROUTING if agents_count > 0 else TaskStatus.COMPLETED,
                        current_task="görev_belirleme",
                        title="Analysis Task",
                        input_text=user_query,
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=None
                        )
                    ))
                except Exception as e:
                    print(f"Error sending task update: {e}")
                    # Continue execution even if websocket update fails

            return plan_object
        except Exception as e:
            print(f"Error during Planner Agent execution: {e}")
            return None

    async def run_planned_agents(
        self,
        plan: PlanerOutputFormat,
        user_query: str,
        user_id: str,
        chat_id: str,
        task_key: str,
        socket_manager: ConnectionManager
    ) -> Dict[str, Any]:
        """
        Executes the tasks defined in the plan using the specified agents.
        Sends detailed task updates (EXECUTING, RUNNING, COMPLETED, FAILED) to the websocket.
        """
        agent_outputs: Dict[str, Any] = {}

        if not plan or not plan.agents:
            print("No tasks in the plan to execute.")
            # Send a failed update if nothing to do
            if task_key and socket_manager:
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.FAILED,
                        current_task="No tasks in plan",
                        title="Analysis Task",
                        input_text=user_query,
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=None
                        )
                    )
                )
            return agent_outputs

        # Send EXECUTING update for the overall task
        if task_key and socket_manager:
            await socket_manager.send_task_update(
                task=Task(
                    task_key=task_key,
                    user_id=user_id,
                    chat_id=chat_id,
                    status=TaskStatus.EXECUTING,
                    current_task="Planned agent execution",
                    title="Analysis Task",
                    input_text=user_query,
                    metadata=TaskMetadata(
                        started_at=int(time.time()),
                        completed_at=None
                    )
                )
            )

        await self._run_multiple_agents_concurrent(
            plan.agents,
            user_query,
            user_id,
            chat_id,
            task_key,
            socket_manager,
            agent_outputs
        )

        # After all agents, send COMPLETED for the overall task
        if task_key and socket_manager:
            await socket_manager.send_task_update(
                task=Task(
                    task_key=task_key,
                    user_id=user_id,
                    chat_id=chat_id,
                    status=TaskStatus.ROUTING,
                    current_task="All planned agents executed",
                    title="Analysis Task",
                    input_text=user_query,
                    output=agent_outputs,
                    metadata=TaskMetadata(
                        started_at=int(time.time()),
                        completed_at=int(time.time())
                    )
                )
            )

        return agent_outputs

    async def run_output_agent(
        self,
        executed_tasks_outputs: Dict[str, Any],
        user_id: str,
        chat_id: str,
        task_key: str,
        socket_manager: ConnectionManager
    ) -> Optional[str]:
        """
        Runs the OutputAgent with the collected outputs from planned agents,
        sends a websocket update with the final result, and marks the task as COMPLETED.

        Args:
            executed_tasks_outputs: A dictionary of outputs from the dynamic agents.

        Returns:
            The final user-facing response string, or None if generation fails.
        """
        from src.api.routes.chats.models import Task, TaskStatus, TaskMetadata

        if not self.output_agent:
            print("Error: Output agent is not initialized.")
            # Send failed task update
            if socket_manager and task_key:
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.FAILED,
                        current_task="Output Agent",
                        title="Output Agent",
                        input_text="",
                        error="Output agent is not initialized.",
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=int(time.time())
                        )
                    )
                )
            return None

        if not executed_tasks_outputs:
            print("No agent outputs to process for the Output Agent.")
            # Send failed task update
            if socket_manager and task_key:
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.FAILED,
                        current_task="Output Agent",
                        title="Output Agent",
                        input_text="",
                        error="No agent outputs to process for the Output Agent.",
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=int(time.time())
                        )
                    )
                )
            return "No information was generated by the agents to form a response."

        output_input_str = json.dumps(executed_tasks_outputs, ensure_ascii=False, indent=2)

        # Apply security filtering to the input before sending to output agent
        output_input_str = self.apply_security_filtering(output_input_str)
        print(f"🔒 Applied security filtering to output agent input")

        try:
            response = await self._run_agent_async(
                self.output_agent, 
                output_input_str, 
                user_id, 
                chat_id
            )

            # DEBUG: Log the raw response to understand what's happening
            print(f"🔍 DEBUG: Output agent raw response type: {type(response)}")
            print(f"🔍 DEBUG: Output agent raw response: {str(response)[:500]}...")

            # Try to extract markdown or string output
            final_content = getattr(response, "content", None)
            print(f"🔍 DEBUG: final_content type: {type(final_content)}")
            print(f"🔍 DEBUG: final_content: {str(final_content)[:500]}...")

            markdown_output = None
            output_dict = {}

            if isinstance(final_content, OutputAgentOutputFormat):
                markdown_output = final_content.markdown_response
                output_dict = final_content.model_dump() if hasattr(final_content, "model_dump") else {}
                print(f"🔍 DEBUG: Using OutputAgentOutputFormat path")
            elif hasattr(response, "to_dict"):
                # Fallback: try to get content from dict
                output_dict = response.to_dict()
                markdown_output = output_dict.get("content")
                print(f"🔍 DEBUG: Using to_dict() path, markdown_output: {str(markdown_output)[:200]}...")
            else:
                markdown_output = str(response)
                output_dict = {"content": markdown_output}
                print(f"🔍 DEBUG: Using str(response) path, markdown_output: {str(markdown_output)[:200]}...")



            # Apply security filtering to the output
            if markdown_output:
                markdown_output = self.apply_security_filtering(markdown_output)
                print(f"🔒 Applied security filtering to output")

            # Send COMPLETED websocket update with the result
            if socket_manager and task_key:
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.COMPLETED,
                        current_task="Output Agent",
                        title="Output Agent",
                        input_text="",
                        output={"markdown_output": markdown_output, **output_dict} if markdown_output else output_dict,
                        error=None,
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=int(time.time())
                        )
                    )
                )

            return markdown_output

        except Exception as e:
            print(f"Error during Output Agent execution: {e}")
            # Send failed task update
            if socket_manager and task_key:
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.FAILED,
                        current_task="Output Agent",
                        title="Output Agent",
                        input_text="",
                        error=str(e),
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=int(time.time())
                        )
                    )
                )
            return None
        
    async def partial_run(self, plan, user_query: str, user_id: str, chat_id: str, task_key: str,  socket_manager: ConnectionManager = None) -> Tuple[Optional[str], Optional[Task]]:
        """
        Executes the planned agents, collects their outputs, and runs the output agent to generate the final response.
        Returns the raw OutputAgent output (not just markdown).

        Args:
            plan: The plan object returned by the planner.
            user_query: The user's input query.
            user_id: The user's ID.
            chat_id: The chat/session ID.
            socket_manager: Optional, for sending websocket updates.
            task_key: The task key for websocket updates.

        Returns:
            The output agent's raw output (could be a model, dict, or string), or None if failed.
        """
        executed_outputs = await self.run_planned_agents(
            plan=plan,
            user_query=user_query,
            user_id=user_id,
            chat_id=chat_id,
            task_key=task_key,
            socket_manager=socket_manager
        )
        if not executed_outputs:
            return None

        # Run the output agent and return its raw output (not just markdown)
        if not self.output_agent:
            print("Error: Output agent is not initialized.")
            return None

        output_input_str = json.dumps(executed_outputs, ensure_ascii=False, indent=2)

        # Apply security filtering to the input before sending to output agent
        output_input_str = self.apply_security_filtering(output_input_str)
        print(f"🔒 Applied security filtering to output agent input in partial_run")

        try:
            # Update status to show we're calling the output agent
            if socket_manager and task_key:
                await socket_manager.send_task_update(
                    task=Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.EXECUTING,
                        current_task="Output Agent",
                        title="Generating Final Response",
                        input_text=user_query,
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=None
                        )
                    )
                )
                # Small delay to ensure clients process this update
                await asyncio.sleep(0.1)

            # Call the output agent WITHOUT task_key and socket_manager
            # The run method doesn't accept these parameters
            response = await self._run_agent_async(
                self.output_agent, 
                output_input_str, 
                user_id, 
                chat_id
            )


            if response.content and isinstance(response.content, OutputAgentOutputFormat):
                formated_response: OutputAgentOutputFormat = response.content
                print(f"🔍 --------123231----- DEBUG: Formated response: ")
                print(f"🔍 DEBUG: Formated response Markdown: {formated_response.markdown_response}")
                print(f"🔍 DEBUG: Formated response Charts: {formated_response.charts}")
                print(f"🔍 DEBUG: Formated response Tables: {formated_response.tables}")

            


            # Try to extract markdown or string output
            final_content = getattr(response, "content", None)
            markdown_output = None
            output_dict = {}

            if isinstance(final_content, OutputAgentOutputFormat):
                markdown_output = final_content.markdown_response
            elif hasattr(response, "to_dict"):
                # Fallback: try to get content from dict
                output_dict = response.to_dict()
                markdown_output = output_dict.get("content")
            else:
                markdown_output = str(response)
                output_dict = {"content": markdown_output}

            # Extract charts and tables only if we have a properly formatted response
            charts = []
            tables = []
            reports = []  # Store report metadata for the Task
            
            try:
                if isinstance(final_content, OutputAgentOutputFormat):
                    formated_response: OutputAgentOutputFormat = final_content
                    
                    # Extract charts from properly formatted response
                    try:
                        if formated_response.charts:
                            for chart in formated_response.charts:
                                try:
                                    # Try to parse the chart schema JSON
                                    parsed_chart = json.loads(chart.chart_schema)
                                    print(f"✅ Successfully parsed chart schema")
                                    
                                    # Validate chart type
                                    is_valid, error_message = self._validate_chart_type(parsed_chart)
                                    if not is_valid:
                                        print(f"⚠️ {error_message}")
                                        # Create a fallback bar chart
                                        # not progress this one
                                        continue
                                        
                                    
                                    # Check if chart data is empty and needs population
                                    if self._is_chart_data_empty(parsed_chart):
                                        print(f"📊 Chart data is empty, attempting to populate with SQL")
                                        
                                        # Get the SQL query for this chart
                                        data_sql = getattr(chart, 'data_sql', '')
                                        if data_sql:
                                            print(f"📊 Found data SQL: {data_sql}")
                                            # Populate chart data using SQL query
                                            populated_chart = await self._populate_chart_data(
                                                parsed_chart, 
                                                data_sql, 
                                                user_id
                                            )
                                            charts.append(populated_chart)
                                        else:
                                            print(f"⚠️ No data_sql found for chart, using empty chart")
                                            charts.append(parsed_chart)
                                    else:
                                        print(f"📊 Chart data already populated, using as-is")
                                        charts.append(parsed_chart)
                                        
                                except json.JSONDecodeError as e:
                                    print(f"❌ Failed to parse chart schema: {e}")
                                    print(f"❌ Chart schema content: {chart.chart_schema[:200]}...")
                                    
                                    # Try to fix common JSON issues
                                    try:
                                        # Attempt to fix the JSON by properly escaping quotes
                                        fixed_schema = chart.chart_schema.replace('"', '\\"').replace('\\"type\\"', '"type"').replace('\\"data\\"', '"data"').replace('\\"options\\"', '"options"')
                                        
                                        # Try parsing the fixed version
                                        parsed_chart = json.loads(f'"{fixed_schema}"')  # Wrap in quotes to make it a JSON string
                                        # Then parse again to get the actual object
                                        parsed_chart = json.loads(parsed_chart)
                                        
                                        # Apply data population for fixed chart too
                                        if self._is_chart_data_empty(parsed_chart):
                                            data_sql = getattr(chart, 'data_sql', '')
                                            if data_sql:
                                                populated_chart = await self._populate_chart_data(
                                                    parsed_chart, 
                                                    data_sql, 
                                                    user_id
                                                )
                                                charts.append(populated_chart)
                                            else:
                                                charts.append(parsed_chart)
                                        else:
                                            charts.append(parsed_chart)
                                            
                                        print(f"✅ Successfully parsed chart schema after fixing")
                                    except Exception as fix_error:
                                        print(f"❌ Could not fix chart schema: {fix_error}")
                                        # Create a fallback chart structure
                                        fallback_chart = {
                                            "type": "bar",
                                            "data": {
                                                "labels": [],
                                                "datasets": [{
                                                    "label": "Data",
                                                    "data": [],
                                                    "backgroundColor": "rgba(75, 192, 192, 0.6)"
                                                }]
                                            },
                                            "options": {
                                                "responsive": True,
                                                "plugins": {
                                                    "title": {
                                                        "display": True,
                                                        "text": "Chart (Error in original data)"
                                                    }
                                                }
                                            }
                                        }
                                        
                                        # Try to populate fallback chart if we have SQL
                                        data_sql = getattr(chart, 'data_sql', '')
                                        if data_sql:
                                            print(f"📊 Attempting to populate fallback chart with SQL")
                                            populated_fallback = await self._populate_chart_data(
                                                fallback_chart, 
                                                data_sql, 
                                                user_id
                                            )
                                            charts.append(populated_fallback)
                                        else:
                                            charts.append(fallback_chart)
                                            
                                        print(f"⚠️  Using fallback chart structure due to parsing error")
                    except Exception as chart_error:
                        print(f"❌ Error processing charts: {chart_error}")
                    
                    # Tables handling - generate Excel files and collect metadata
                    try:
                        if formated_response.tables:
                            for table in formated_response.tables:
                                print(f"🔍 DEBUG: Processing table: {getattr(table, 'table_name', 'unknown')}")
                                try:
                                    # Generate Excel file - function now handles both SQL and direct data
                                    table_path = await generate_excel_from_sql_query(table, user_id=user_id)
                                    
                                    # Convert filename from .csv to .xlsx if needed
                                    file_name = os.path.basename(table_path)
                                    if file_name.endswith('.csv'):
                                        file_name = file_name[:-4] + '.xlsx'
                                    tables.append(file_name)
                                    
                                    # Create report metadata for the Task
                                    report_metadata = {
                                        "file_path": table_path,
                                        "file_name": file_name,  # Use the converted filename
                                        "table_name": getattr(table, 'table_name', 'unknown_table'),
                                        "table_description": getattr(table, 'table_description', 'Generated report'),
                                        "data_sql": getattr(table, 'data_sql', ''),  # Will be empty for direct data
                                        "generated_at": int(time.time()),
                                        "user_id": user_id,
                                        "type": "excel"
                                    }
                                    
                                    # Add the report metadata to our collection
                                    reports.append(report_metadata)
                                    
                                except Exception as e:
                                    print(f"❌ Error processing table {getattr(table, 'table_name', 'unknown')}: {str(e)}")
                                    continue
                    except Exception as tables_error:
                        print(f"❌ Error processing tables: {tables_error}")
                        
                else:
                    print(f"⚠️  WARNING: Output agent returned malformed response, no charts/tables extracted")
                    print(f"⚠️  Response type: {type(final_content)}")
                    if hasattr(final_content, '__str__'):
                        print(f"⚠️  Response preview: {str(final_content)[:200]}...")
                        
            except Exception as processing_error:
                print(f"❌ Critical error during charts/tables processing: {processing_error}")
                import traceback
                print(f"❌ Full traceback: {traceback.format_exc()}")
                # Ensure we have empty arrays so the rest of the process can continue
                charts = []
                tables = []
                reports = []



            # Apply security filtering to the output
            if markdown_output:
                markdown_output = self.apply_security_filtering(markdown_output)
                print(f"🔒 Applied security filtering to output")

            # Send COMPLETED websocket update with the result
            final_task = None
            try:
                if socket_manager and task_key:
                    # Create client-friendly reports (with only filename, not full path)
                    client_reports = []
                    for report in reports:
                        # Ensure filename has .xlsx extension
                        file_name = report["file_name"]
                        if file_name.endswith('.csv'):
                            file_name = file_name[:-4] + '.xlsx'
                        
                        client_report = {
                            "file_name": file_name,  # Use the converted filename
                            "table_name": report["table_name"],
                            "table_description": report["table_description"],
                            "generated_at": report["generated_at"],
                            "type": "excel"
                        }
                        client_reports.append(client_report)
                    
                    # Create the final task update
                    final_task = Task(
                        task_key=task_key,
                        user_id=user_id,
                        chat_id=chat_id,
                        status=TaskStatus.COMPLETED,
                        current_task="Output Agent",
                        title="Analysis Complete",
                        input_text=user_query,
                        output={"markdown_output": markdown_output,"charts": charts, "tables": tables} if markdown_output else output_dict,
                        reports=client_reports,  # Use client-friendly reports (no full paths)
                        error=None,
                        metadata=TaskMetadata(
                            started_at=int(time.time()),
                            completed_at=int(time.time())
                        )
                    )

                    # Send the update and await it to complete fully
                    await socket_manager.send_task_update(final_task)

                    # Add a delay after the final update to ensure connections remain stable
                    await asyncio.sleep(0.5)
                    
                print(f"✅ Successfully completed partial_run for task: {task_key}")
                
            except Exception as task_error:
                print(f"❌ Error creating/sending final task: {task_error}")
                import traceback
                print(f"❌ Task error traceback: {traceback.format_exc()}")

            # Return response, final_task, and full reports metadata for database storage
            return response, final_task, reports  # Include full reports with file paths for database
        except Exception as e:
            print(f"Error during Output Agent execution in partial_run: {e}")
            # Send failed task update
            if socket_manager and task_key:
                # Create the error task update
                error_task = Task(
                    task_key=task_key,
                    user_id=user_id,
                    chat_id=chat_id,
                    status=TaskStatus.FAILED,
                    current_task="Output Agent Error",
                    title="Analysis Failed",
                    input_text=user_query,
                    error=str(e),
                    metadata=TaskMetadata(
                        started_at=int(time.time()),
                        completed_at=int(time.time())
                    )
                )

                # Send the update and await it to complete
                await socket_manager.send_task_update(error_task)

                # Add a delay after the error update
                await asyncio.sleep(0.5)
            return None, None, []  # Return three values for consistency

    def full_run(self, user_query: str, user_id: str, chat_id: str) -> Optional[str]:
        """
        Performs a full end-to-end run: planner, task execution, and output generation.
        """

        plan = self.run_planner(user_query, user_id=user_id, chat_id=chat_id)
        if not plan:
            return "I encountered an issue while planning how to address your query. Please try again."

        executed_outputs = self.run_planned_agents(plan, user_query, user_id, chat_id)
        if not executed_outputs:
             # This case might be handled inside run_output_agent as well
            return "No specific actions were performed by the agents based on the plan."

        final_response = self.run_output_agent(executed_outputs, user_id, chat_id)
        if not final_response:
            return "I gathered information from various agents, but encountered an issue while generating the final response."

        return final_response

    def cleanup(self):
        """Cleanup resources, especially the thread pool executor"""
        if hasattr(self, 'agent_executor'):
            self.agent_executor.shutdown(wait=True)
            print("Agent executor thread pool shut down successfully")

    def _validate_chart_type(self, chart_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate chart type and return whether it's allowed.
        
        Args:
            chart_data: The parsed chart schema dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            chart_type = chart_data.get("type", "").lower()
            
            # List of forbidden chart types
            forbidden_types = ["scatter"]
            
            if chart_type in forbidden_types:
                return False, f"'{chart_type}' grafik tipi kullanımı kısıtlanmıştır. Lütfen alternatif bir grafik tipi kullanın (bar, line, pie)."
            
            # List of allowed chart types
            allowed_types = ["bar", "line", "pie", "doughnut", "radar", "polarArea"]
            
            if chart_type not in allowed_types:
                return False, f"Geçersiz grafik tipi: '{chart_type}'. İzin verilen tipler: {', '.join(allowed_types)}"
            
            return True, ""
            
        except Exception as e:
            return False, f"Grafik tipi doğrulanamadı: {str(e)}"
