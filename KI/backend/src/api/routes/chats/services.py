"""
Service implementation for KAI task planning and execution
"""

import time
import threading
import traceback
import os
import asyncio
from dotenv import load_dotenv
from typing import Dict, List, Optional, Any, Tuple
from fastapi import HTTPException
from sqlalchemy import select
from sqlalchemy.orm import Session
import uuid

from src.agents.agno_agents.agent_factory import AgentFactory
from src.agents.agno_agents.agent_service import AgentService
# Old crew formats - replaced with AgentService
# from src.agents.crews.crew_formats import RouterOutput, GeneralOutput, ChartJsWithSqlOutput
from src.agents.datasets.database import chat_history_db
from src.rag_engine.analitic.db.postgresql.models import User
from src.rag_engine.analitic.db.postgresql.database import get_session
from src.rag_engine.interface import RAGInterface

from .models import Task, TaskStatus, TaskResult, TaskMetadata
from src.rag_engine.analitic.db.postgresql.models import ChatMessageDB
from .websocket import connection_manager
from .chat_service import update_agent_message, get_chat_messages
from .redis_connector import get_redis_client, create_task_key
import json

# Helper functions

# --- Helper functions to find/update task in Redis --- 
def _get_task_by_key(task_key: str) -> Optional[Task]:
    """Finds a task by its unique key in Redis (SYNCHRONOUS)."""
    redis_conn = get_redis_client()
    redis_task_data_key = create_task_key(task_key)
    
    try:
        task_json = redis_conn.get(redis_task_data_key)
        if task_json:
            # Deserialize JSON string to Task object
            task = Task.model_validate_json(task_json)
            return task
        else:
            print(f"[DEBUG] Task key {task_key} not found in Redis.")
            return None
    except Exception as e:
        print(f"[Redis Error] Failed to get task {task_key}: {str(e)}")
        # Handle error appropriately - maybe return None or raise
        return None

async def get_task_by_key_async(task_key: str) -> Optional[Task]:
    """Finds a task by its unique key in Redis (ASYNCHRONOUS - for router use)."""
    # Note: The redis-py client is synchronous, but we make this async
    # to match the FastAPI endpoint context. For true async Redis, use aioredis.
    redis_conn = get_redis_client() 
    redis_task_data_key = create_task_key(task_key)
    
    try:
        task_json = redis_conn.get(redis_task_data_key)
        if task_json:
            task = Task.model_validate_json(task_json)
            return task
        else:
            print(f"[DEBUG][Async] Task key {task_key} not found in Redis.")
            return None
    except Exception as e:
        print(f"[Redis Error][Async] Failed to get task {task_key}: {str(e)}")
        return None

def _save_task_update(task: Task):
    """Saves the updated task object back to Redis (SYNCHRONOUS)."""
    redis_conn = get_redis_client()
    redis_task_data_key = create_task_key(task.task_key)
    try:
        task_json = task.model_dump_json()
        redis_conn.set(redis_task_data_key, task_json)
    except Exception as e:
        print(f"[Redis Error] Failed to save update for task {task.task_key}: {str(e)}")
        # Consider how to handle save failures - retry? Log critical error?

# --- Helper function for chart population ---
def _populate_chart_data(
    chart_template: Dict[str, Any], 
    sql_query: str, 
    user_id: str, 
    rag_interface: RAGInterface,
    thread_name: str = "ChartPopulationThread" # Add thread name for logging
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """
    Executes SQL query and populates a Chart.js template.

    Args:
        chart_template: The Chart.js template dictionary.
        sql_query: The SQL query to execute.
        user_id: The ID of the user.
        rag_interface: An instance of RAGInterface.
        thread_name: The name of the current thread for logging.

    Returns:
        A tuple containing:
        - The populated chart template dictionary (or None if error).
        - An error message string (or None if success).
    """
    try:
        print(f"[Thread {thread_name}] Executing SQL for chart: {sql_query}")
        # Assuming aggregate_csv_data is synchronous for now
        sql_result = rag_interface.agregate_csv_data(user_id, sql_query)
        
        if sql_result and sql_result.get("success"):
            sql_data = sql_result.get("data", {})
            sql_rows = sql_data.get("rows", [])
            sql_cols = sql_data.get("columns", [])
            chart_data_section = chart_template.get("data", {})
            if not chart_data_section: # Ensure data section exists
                 return None, "Chart template is missing the 'data' section."
            template_datasets = chart_data_section.get("datasets", [])
            if not template_datasets: # Ensure datasets exist
                return None, "Chart template is missing 'datasets' in the 'data' section."

            if not sql_rows: 
                print(f"[WARN][Thread {thread_name}] SQL query for chart returned no rows.")
                # Return the original template but signal no data found? Or error? Let's return error for now.
                return None, "SQL query for chart returned no data."
            elif not sql_cols or len(sql_cols) == 0:
                 print(f"[WARN][Thread {thread_name}] SQL query returned no columns.")
                 return None, "SQL query for chart returned no columns."
            # Check if data columns match datasets
            elif len(sql_cols) - 1 != len(template_datasets):
                error_msg = f"SQL data column count ({len(sql_cols) - 1}) doesn't match chart dataset count ({len(template_datasets)})."
                print(f"[WARN][Thread {thread_name}] Mismatch: {error_msg}")
                return None, error_msg
            else:
                # Get the column name for labels (first column)
                label_col_name = sql_cols[0]
                # Populate labels using the first column's name
                chart_data_section['labels'] = [row.get(label_col_name) for row in sql_rows]
                
                # Populate data for each dataset from subsequent SQL columns
                for i, dataset in enumerate(template_datasets):
                    # Get the column name for this dataset (column i+1)
                    data_col_name = sql_cols[i+1]
                    # Populate data using the correct column name
                    dataset['data'] = [row.get(data_col_name) for row in sql_rows] 
                    
                print(f"[Thread {thread_name}] Successfully populated chart labels and data.")
                return chart_template, None # Return populated template, no error
                
        else:
            error_msg = sql_result.get("error", "Unknown SQL execution error")
            print(f"[ERROR][Thread {thread_name}] SQL query for chart failed: {error_msg}")
            return None, f"Failed to execute SQL for chart: {error_msg}"

    except Exception as sql_exec_err:
        print(f"[ERROR][Thread {thread_name}] Exception executing SQL or populating chart: {sql_exec_err}\n{traceback.format_exc()}")
        return None, f"Error processing chart data: {str(sql_exec_err)}"

async def verify_user_exists(user_id: str) -> bool:
    """Checks if the user exists"""
    with get_session() as session:
        result = session.execute(select(User).filter(User.id == user_id))
        user = result.scalars().first()
        return user is not None


def create_task(user_id: str, chat_id: str, input_text: str, title: str, task_key: str) -> Task:
    """Creates a new standardized task with initial values, including the task key."""
    return Task(
        task_key=task_key,
        title=title,
        user_id=user_id,
        chat_id=chat_id,
        input_text=input_text,
        status=TaskStatus.PENDING,
        metadata=TaskMetadata()
    )


def validate_request_fields(user_id: str, chat_id: str, input_text: str):
    """Validate all required request fields."""
    # Validate user_id
    if not user_id:
        raise HTTPException(status_code=400, detail="Kullanıcı ID'si gereklidir")
    if not user_id.strip():
        raise HTTPException(status_code=422, detail="Geçersiz kullanıcı ID formatı")

    # Validate chat_id
    if not chat_id:
        raise HTTPException(status_code=400, detail="Chat ID gereklidir")
    if not chat_id.strip():
        raise HTTPException(status_code=422, detail="Geçersiz chat ID formatı")

    # Validate input_text
    if not input_text:
        raise HTTPException(status_code=400, detail="Mesaj içeriği gereklidir")
    if not input_text.strip():
        raise HTTPException(status_code=422, detail="Mesaj içeriği boş olamaz")


async def verify_user_and_chat(user_id: str, chat_id: str):
    """Verify that both user and chat session exist."""
    # Check if user exists
    user_exists = await verify_user_exists(user_id)
    if not user_exists:
        raise HTTPException(status_code=404, detail=f"Kullanıcı bulunamadı: {user_id}")

    # Check if chat session exists
    try:
        chat_info = chat_history_db.get_chat_info(chat_id)
        if not chat_info:
            raise HTTPException(status_code=404, detail=f"Chat oturumu bulunamadı: {chat_id}")
    except ValueError:
        raise HTTPException(status_code=404, detail=f"Chat oturumu bulunamadı: {chat_id}")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Chat oturumu kontrolü sırasında hata: {str(e)}")



def get_task_status(task_key: str):
    """Get the current status of a task using its unique key from Redis."""
    task = _get_task_by_key(task_key) # Use synchronous version here
    if not task:
        raise HTTPException(status_code=404, detail=f"Görev bulunamadı: {task_key}")
    return task 

async def get_chat_assets(chat_id: str) -> Dict[str, Any]:
    """
    Get all assets (charts, etc.) associated with a chat session from chat messages.
    
    Args:
        chat_id: The chat session ID
        
    Returns:
        Dictionary containing categorized assets
    """
    with get_session() as session:
        # Query all agent messages with assets for this chat
        messages = session.query(ChatMessageDB).filter(
            ChatMessageDB.chat_id == chat_id,
            ChatMessageDB.sender == 'agent',
            ChatMessageDB.assets.is_not(None)  # Only messages with assets
        ).order_by(ChatMessageDB.timestamp).all()
        
        if not messages:
            return {
                "chat_id": chat_id,
                "assets": {"charts": [], "other": []},
                "metadata": {"total_messages": 0}
            }
        
        # Initialize assets dictionary
        assets = {
            "charts": [],
            "reports": [],
            "other": []
        }
        
        # Track metadata
        metadata = {
            "total_messages": len(messages),
            "messages_with_assets": 0,
            "total_charts": 0,
            "total_reports": 0
        }
        
        # Process each message
        for message in messages:
            if message.assets:
                metadata["messages_with_assets"] += 1
                
                # Process charts
                if "charts" in message.assets and message.assets["charts"]:
                    charts = message.assets["charts"]
                    metadata["total_charts"] += len(charts)
                    
                    # Add message context to each chart
                    for chart in charts:
                        chart_with_context = {
                            "chart_data": chart,
                            "message_context": {
                                "message_id": str(message.id),
                                "task_key": message.task_key,
                                "timestamp": message.timestamp.timestamp() if message.timestamp else None,
                                "is_task_output": message.is_task_output
                            }
                        }
                        assets["charts"].append(chart_with_context)
                
                # Process reports
                if "reports" in message.assets and message.assets["reports"]:
                    reports = message.assets["reports"]
                    metadata["total_reports"] += len(reports)
                    
                    # Add message context to each report
                    for report in reports:
                        report_with_context = {
                            "data": report,
                            "message_context": {
                                "message_id": str(message.id),
                                "task_key": message.task_key,
                                "timestamp": message.timestamp.timestamp() if message.timestamp else None,
                                "is_task_output": message.is_task_output
                            }
                        }
                        assets["reports"].append(report_with_context)
                
                # Process other types of assets
                if "other" in message.assets and message.assets["other"]:
                    for other_asset in message.assets["other"]:
                        asset_with_context = {
                            "data": other_asset,
                            "message_context": {
                                "message_id": str(message.id),
                                "task_key": message.task_key,
                                "timestamp": message.timestamp.timestamp() if message.timestamp else None,
                                "is_task_output": message.is_task_output
                            }
                        }
                        assets["other"].append(asset_with_context)
        
        return {
            "chat_id": chat_id,
            "assets": assets,
            "metadata": metadata
        }
