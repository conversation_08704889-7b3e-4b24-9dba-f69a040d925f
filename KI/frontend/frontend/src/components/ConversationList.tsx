import React, { useState, useEffect } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import Divider from '@mui/material/Divider';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import CircularProgress from '@mui/material/CircularProgress';

import { getUserChats, createChat, Chat } from '../app/services/api';

interface ConversationListProps {
  userId: string;
  onSelectChat: (chatId: string) => void;
  currentChatId?: string;
}

const ConversationList: React.FC<ConversationListProps> = ({ userId, onSelectChat, currentChatId }) => {
  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [newChatDialogOpen, setNewChatDialogOpen] = useState<boolean>(false);
  const [newChatName, setNewChatName] = useState<string>('');

  // Load user's chats
  useEffect(() => {
    if (userId) {
      loadChats();
    }
  }, [userId]);

  const loadChats = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 ConversationList: Loading chats for userId:', userId);
      const userChats = await getUserChats(userId);
      console.log('✅ ConversationList: Received chats:', userChats);
      setChats(userChats);
      
      // If there are chats and no current chat is selected, select the first one
      if (userChats.length > 0 && !currentChatId) {
        console.log('📌 ConversationList: Auto-selecting first chat:', userChats[0].chat_id);
        onSelectChat(userChats[0].chat_id);
      }
    } catch (error) {
      console.error('❌ ConversationList: Error loading chats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNewChat = async () => {
    try {
      setIsLoading(true);
      const response = await createChat(userId, newChatName.trim() || undefined);
      
      // Add the new chat to the list and select it
      if (response && response.chat_id) {
        onSelectChat(response.chat_id);
        await loadChats(); // Reload all chats to get the updated list
      }
      
      // Close dialog and reset input
      setNewChatDialogOpen(false);
      setNewChatName('');
    } catch (error) {
      console.error('Error creating new chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem' }}>Sohbetler</Typography>
        <Button
          variant="contained"
          size="small"
          startIcon={<AddCircleOutlineIcon />}
          onClick={() => setNewChatDialogOpen(true)}
          disabled={isLoading || !userId}
        >
          Yeni
        </Button>
      </Box>
      
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress size={24} />
        </Box>
      ) : chats.length === 0 ? (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Henüz bir sohbet oturumunuz bulunmuyor
          </Typography>
        </Box>
      ) : (
        <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>
          {chats.map((chat, index) => (
            <React.Fragment key={chat.chat_id}>
              <ListItem disablePadding>
                <ListItemButton 
                  selected={chat.chat_id === currentChatId}
                  onClick={() => onSelectChat(chat.chat_id)}
                >
                  <ListItemIcon sx={{ minWidth: '40px' }}>
                    <ChatBubbleOutlineIcon color={chat.chat_id === currentChatId ? "primary" : "inherit"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary={chat.chat_name || 'Adsız Sohbet'} 
                    secondary={new Date(chat.created_at).toLocaleDateString()}
                    primaryTypographyProps={{
                      noWrap: true,
                      style: { 
                        fontWeight: chat.chat_id === currentChatId ? 'bold' : 'normal'
                      }
                    }}
                  />
                </ListItemButton>
              </ListItem>
              {index < chats.length - 1 && <Divider component="li" />}
            </React.Fragment>
          ))}
        </List>
      )}

      {/* New Chat Dialog */}
      <Dialog
        open={newChatDialogOpen}
        onClose={() => setNewChatDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Yeni Sohbet Oluştur</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              fullWidth
              id="chatName"
              label="Sohbet Adı (İsteğe Bağlı)"
              name="chatName"
              autoFocus
              value={newChatName}
              onChange={(e) => setNewChatName(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleCreateNewChat();
                }
              }}
              disabled={isLoading}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setNewChatDialogOpen(false)}
            disabled={isLoading}
          >
            İptal
          </Button>
          <Button 
            variant="contained"
            onClick={handleCreateNewChat}
            disabled={isLoading}
          >
            {isLoading ? <CircularProgress size={24} /> : 'Oluştur'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ConversationList; 