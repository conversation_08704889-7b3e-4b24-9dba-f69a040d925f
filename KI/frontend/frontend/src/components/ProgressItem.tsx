import React, { useState } from 'react';
import { ListItem, ListItemIcon, ListItemText, Typography, Chip, Box, IconButton, Collapse, Paper } from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import AutorenewIcon from '@mui/icons-material/Autorenew'; // Or CircularProgress
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import theme from '../app/utils/theme'; // Import theme for direct color access if needed

interface ProgressStatus {
  id: string;
  timestamp: number;
  status: 'processing' | 'success' | 'error' | 'info';
  message: string;
  output?: any; // Add output field
}

interface ProgressItemProps {
  item: ProgressStatus;
}

const getStatusIcon = (status: ProgressStatus['status']) => {
  switch (status) {
    case 'success':
      return <CheckCircleOutlineIcon color="success" fontSize="small" />;
    case 'error':
      return <ErrorOutlineIcon color="error" fontSize="small" />;
    case 'processing':
      // Use Autorenew or a static icon like Hourglass
      return <AutorenewIcon color="primary" fontSize="small" sx={{ animation: 'spin 1.5s linear infinite' }} />;
    case 'info':
    default:
      return <InfoOutlinedIcon color="action" fontSize="small" />;
  }
};

const getStatusChipColor = (status: ProgressStatus['status']): "success" | "error" | "primary" | "info" | "default" => {
    switch(status) {
        case 'success': return 'success';
        case 'error': return 'error';
        case 'processing': return 'primary';
        case 'info': return 'info';
        default: return 'default';
    }
}

const ProgressItem: React.FC<ProgressItemProps> = ({ item }) => {
  const [expanded, setExpanded] = useState(false);
  const hasOutput = item.output && Object.keys(item.output).length > 0;

  return (
    <Box>
      <ListItem 
        alignItems="flex-start" 
        sx={{ 
          py: 1.5, 
          px: 0, // Remove default padding, handle spacing in RightPanel
          borderBottom: `1px solid ${theme.palette.divider}`,
          '&:last-child': { borderBottom: 'none' } // Remove border for last item
        }}
      >
        <ListItemIcon sx={{ minWidth: 35, mt: 0.5 }}>
          {getStatusIcon(item.status)}
        </ListItemIcon>
        <ListItemText
          primary={item.message}
          secondary={`(${new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })})`}
          primaryTypographyProps={{ variant: 'body2', sx: { mb: 0.5 } }}
          secondaryTypographyProps={{ variant: 'caption', color: 'text.secondary' }}
        />
        
        {/* Add expand button for items with output */}
        {hasOutput && (
          <IconButton 
            edge="end" 
            size="small" 
            onClick={() => setExpanded(!expanded)}
            sx={{ ml: 1 }}
          >
            {expanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        )}
      </ListItem>
      
      {/* Output collapse panel */}
      {hasOutput && (
        <Collapse in={expanded}>
          <Paper 
            variant="outlined" 
            sx={{ 
              mx: 2, 
              mb: 2, 
              p: 1.5, 
              bgcolor: 'rgba(0, 0, 0, 0.02)',
              fontSize: '0.85rem'
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>
              Çıktı Detayı:
            </Typography>
            <Box component="pre" sx={{ 
              margin: 0, 
              whiteSpace: 'pre-wrap', 
              wordBreak: 'break-word',
              fontSize: '0.75rem',
              lineHeight: 1.4
            }}>
              {JSON.stringify(item.output, null, 2)}
            </Box>
          </Paper>
        </Collapse>
      )}
      
       {/* Keyframe for spinning icon */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </Box>
  );
};

export default ProgressItem; 