from typing import Dict, Optional, <PERSON>
import os
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat
from agno.tools.reasoning import ReasoningTools

def get_model(model_config: Optional[Dict] = None) -> Union[Claude, OpenAIChat]:
        """
        Get the model to use for the agent.

        Args:
            model_config: The configuration for the model to use.
                          If None, the default model is used.

        Returns:
            An Agno model.
        """
        # Use the provided model config or get from environment
        effective_model_config = model_config if model_config is not None else {}
        
        provider = effective_model_config.get("provider", os.getenv("LLM_PROVIDER", "openai")).lower()
        model_id = effective_model_config.get("model", os.getenv("LLM_MODEL"))
        api_key = effective_model_config.get("api_key", os.getenv("LLM_API_KEY"))
        base_url = effective_model_config.get("base_url", os.getenv("LLM_URL"))

        if not model_id:
            raise ValueError("LLM_MODEL environment variable or model_config['model'] must be set.")

        if provider == "anthropic":
            return Claude(
                id=model_id,
                api_key=api_key,
                base_url=base_url,
            )
        # Default to OpenAI
        return OpenAIChat(
            id=model_id,
            api_key=api_key,
            base_url=base_url,
        )



model = get_model()

from agno.agent import Agent

agent = Agent(tools=[ReasoningTools()], show_tool_calls=True, model=model)
res = agent.run("Whats happening in France?")
print(res)