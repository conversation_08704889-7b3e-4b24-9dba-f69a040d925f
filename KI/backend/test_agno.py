from src.agents.agno_agents.agent_factory import AgentFactory
from src.agents.agno_agents.agent_service import AgentService
from dotenv import load_dotenv

load_dotenv()

def run_test():
    """Runs an end-to-end test for the Agno agent system using AgentService."""
    print("--- Initializing Agent Factory ---")
    try:
        factory = AgentFactory()
    except Exception as e:
        print(f"Failed to initialize AgentFactory: {e}")
        return

    print("--- Initializing Agent Service ---")
    try:
        agent_service = AgentService(factory)
    except Exception as e:
        print(f"Failed to initialize AgentService: {e}")
        return

    # User Query and Context
    user_query = "<PERSON><PERSON> adım samet senin adın ne?"
    test_user_id = "f9d4351d-872a-489d-abd3-eb0b5d9765c4"
    test_chat_id = "test_chat_e2e_002"

    print(f"\n--- Starting Full Run with AgentService ---")
    print(f"User Query: {user_query}")


    final_response = agent_service.full_run(
        user_query=user_query,
        user_id=test_user_id,
        chat_id=test_chat_id
    )

    # Display Final Output
    print("\n--- Final User-Facing Response (from AgentService) ---")
    if final_response:
        print(final_response)
    else:
        print("AgentService did not return a final response.")
    
    print("\n--- Test Execution Finished ---")
if __name__ == "__main__":
    run_test()
