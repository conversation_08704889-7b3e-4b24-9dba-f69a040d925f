# Updated useChat Hook Usage

The `useChat` hook has been updated to work with the new API v1 structure. Here's how to use it:

## Basic Usage

```typescript
import { useChat } from '../app/hooks/useChat';

function ChatComponent() {
  const {
    chatHistory,
    sendMessage,
    clearChat,
    scrollRef,
    scrollToBottom,
    userId,
    currentChatId,
    login,
    createNewChat
  } = useChat();

  // Login a user
  const handleLogin = async () => {
    try {
      await login('username');
      console.log('User logged in:', userId);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  // Create a new chat session
  const handleCreateChat = async () => {
    try {
      await createNewChat('My New Chat');
      console.log('Chat created:', currentChatId);
    } catch (error) {
      console.error('Chat creation failed:', error);
    }
  };

  // Send a message
  const handleSendMessage = async () => {
    try {
      await sendMessage('Hello, analyze this data for me');
    } catch (error) {
      console.error('Message sending failed:', error);
    }
  };

  return (
    <div>
      {!userId && (
        <button onClick={handleLogin}>Login</button>
      )}
      
      {userId && !currentChatId && (
        <button onClick={handleCreateChat}>Create Chat</button>
      )}
      
      {userId && currentChatId && (
        <>
          <div ref={scrollRef}>
            {chatHistory.messages.map(message => (
              <div key={message.id}>
                <strong>{message.role}:</strong> {message.content}
              </div>
            ))}
          </div>
          
          <button onClick={handleSendMessage}>Send Message</button>
          <button onClick={clearChat}>Clear Chat</button>
        </>
      )}
    </div>
  );
}
```

## API Changes

### Old API (Deprecated)
- `startAnalysis(question)` - Used for starting analysis with polling
- `getAnalysisStatus(taskId)` - Used for polling status updates

### New API v1
- `loginUser(username)` - Login or create user
- `createChat(userId, chatName?)` - Create new chat session
- `sendMessageToAgent(userId, chatId, message)` - Send message and get immediate response
- `uploadFile(chatId, userId, file, fileType)` - Upload files to specific chat
- `getChatHistory(chatId, limit?, skip?)` - Get chat message history

## Key Differences

1. **Chat-based**: All interactions now happen within chat sessions
2. **Immediate responses**: No more polling - responses are immediate
3. **User sessions**: Users must login and create chat sessions
4. **File uploads**: Files are uploaded to specific chats
5. **Simplified responses**: Responses have a simpler structure with `status`, `message`, and optional `error`

## Migration Guide

If you're migrating from the old useChat hook:

1. **Login flow**: Add user login before any chat operations
2. **Chat creation**: Create chat sessions before sending messages
3. **Remove polling**: No need for status polling anymore
4. **Update file uploads**: Use new file upload API with chat context
5. **Simplified responses**: Update code to handle the new response format 