# Import environment variables
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create a settings class to mimic the old settings object
class Settings:
    # API Settings
    API_HOST = os.getenv("API_HOST")
    API_PORT = int(os.getenv("API_PORT"))
    API_DEBUG = os.getenv("API_DEBUG").lower() == "true"
    API_RELOAD = os.getenv("API_RELOAD").lower() == "true"

    # Environment
    ENVIRONMENT = os.getenv("ENVIRONMENT")

    # Security
    SECRET_KEY = os.getenv("SECRET_KEY")
    API_TOKEN = os.getenv("API_TOKEN")

    # CORS
    CORS_ORIGINS = os.getenv("CORS_ORIGINS").split(",")

    # Database
    DATABASE_URL = os.getenv("DATABASE_URL")

# Create settings instance
settings = Settings()