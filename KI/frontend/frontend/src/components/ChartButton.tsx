import React from 'react';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import BarChartIcon from '@mui/icons-material/BarChart'; // Or ShowChartIcon

interface ChartButtonProps {
  onClick: () => void; // Function to call when button is clicked
}

const ChartButton: React.FC<ChartButtonProps> = ({ onClick }) => {
  return (
    <Tooltip title="Grafikleri Görüntüle" placement="top">
      <IconButton 
        color="primary" 
        onClick={onClick}
        aria-label="grafikleri görüntüle"
      >
        <BarChartIcon />
      </IconButton>
    </Tooltip>
  );
};

export default ChartButton; 