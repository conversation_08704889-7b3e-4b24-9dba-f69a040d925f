"""
CrewAI memory adapter for PostgreSQL chat history
"""

from typing import List, Dict, Any, Optional, ClassVar
import logging
import uuid
from pydantic import PrivateAttr

from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient
from qdrant_client.http import models as qdrant_models

from crewai.memory.memory import Memory
from crewai.memory.storage.interface import Storage
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
from .database import get_chat_history_db
from crewai.memory.long_term.long_term_memory import LongTermMemory
from crewai.memory.short_term.short_term_memory import ShortTermMemory

logger = logging.getLogger(__name__)




from crewai.memory.short_term.short_term_memory import ShortTermMemory


class QdrantStorage(Storage):
    """
    Implementation of Storage interface using Qdrant for vector storage,
    aligned with DocumentIndexer embedding approach.
    """

    def __init__(self,
                 collection_name: str,
                 embedding_model_name: str = "paraphrase-multilingual-mpnet-base-v2",
                 qdrant_url: Optional[str] = None,
                 qdrant_port: Optional[int] = 6333,
                 qdrant_api_key: Optional[str] = None
                 ):
        """
        Initialize Qdrant storage

        Args:
            collection_name: Name of the Qdrant collection
            embedding_model_name: Name of the SentenceTransformer model to use
            url: Qdrant server URL (optional)
            api_key: Qdrant API key (optional)
        """
        self.collection_name = collection_name
        self.qdrant_url = qdrant_url
        self.qdrant_port = qdrant_port
        self.qdrant_api_key = qdrant_api_key

        # Load embedding model
        try:
            logger.info(f"Loading embedding model for QdrantStorage: {embedding_model_name}")
            self.model = SentenceTransformer(embedding_model_name)
            self.vector_size = self.model.get_sentence_embedding_dimension()
            logger.info(f"Embedding model loaded. Vector size: {self.vector_size}")
        except Exception as e:
            logger.error(f"Failed to load SentenceTransformer model '{embedding_model_name}': {e}")
            raise

        self._initialize_client()

    def _initialize_client(self):
        """Initialize the Qdrant client and ensure collection exists
           IF not exists, create it.
        Args:
            url: Qdrant server URL (optional)
            api_key: Qdrant API key (optional)
        """
        try:
            if not self.qdrant_url:
                self.client = QdrantClient(port=self.qdrant_port)
            else:
                # Ensure the URL has a protocol prefix
                url = self.qdrant_url
                if not url.startswith("http://") and not url.startswith("https://"):
                    url = f"http://{url}"
                self.client = QdrantClient(url=url, port=self.qdrant_port, api_key=self.qdrant_api_key)

            # Check if collection exists, create if it doesn't
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=self.vector_size, # Use determined vector size
                        distance=qdrant_models.Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {self.collection_name}")

        except ImportError:
            logger.error("Qdrant client not installed. Please install with 'pip install qdrant-client'")
            raise
        except Exception as e:
            logger.error(f"Error initializing Qdrant client: {str(e)}")
            raise

    def save(self, value: Any, metadata: Dict[str, Any]) -> None:
        """
        Save a value with metadata to Qdrant.
        Note: Assumes 'value' is the text content.

        Args:
            value: The text content to save and embed.
            metadata: Additional metadata for the entry.
        """
        try:
            if not isinstance(value, str):
                value = str(value) # Ensure value is string for embedding

            # Generate embeddings for the value using the local model
            embedding = self._get_embedding(value)

            # Create a unique ID for the point if not provided in metadata
            point_id = metadata.get("id", str(uuid.uuid4()))

            # Prepare payload, ensuring text is stored under 'context' key
            payload = metadata.copy()
            payload["context"] = value # Store the text under 'context' key

            # Store the point in Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=[
                    qdrant_models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )
        except Exception as e:
            logger.error(f"Error saving to Qdrant: {str(e)}")
            # Decide on error handling: raise, log, or return?
            # For now, logging and continuing.

    def search(self, query: str, limit: int = 5, score_threshold: float = 0.65) -> List[Dict[str, Any]]:
        """
        Search for similar entries in Qdrant using the local embedding model.

        Args:
            query: The search query.
            limit: Maximum number of results to return.
            score_threshold: Minimum similarity score threshold.

        Returns:
            List of dictionaries, where each dictionary is the payload
            of a matching Qdrant point.
        """
        try:
            # Generate embedding for the query using the local model
            query_embedding = self._get_embedding(query)

            # Search in Qdrant
            search_results: List[qdrant_models.ScoredPoint] = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True # Ensure payload is returned
            )

            # Extract payloads from the results
            results_payloads = [point.payload for point in search_results if point.payload is not None]

            return results_payloads
        except Exception as e:
            logger.error(f"Error searching in Qdrant: {str(e)}")
            return []

    def reset(self) -> None:
        """Reset the storage by recreating the collection"""
        try:
            self.client.delete_collection(collection_name=self.collection_name)
            logger.info(f"Deleted Qdrant collection: {self.collection_name}")

            # Recreate the collection
            self._initialize_client()
        except Exception as e:
            logger.error(f"Error resetting Qdrant storage: {str(e)}")

    def _get_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for text using the loaded SentenceTransformer model.

        Args:
            text: Text to generate embedding for.

        Returns:
            List of embedding values.
        """
        try:
            embedding = self.model.encode(text)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Error generating embedding using SentenceTransformer: {str(e)}")
            # Return a zero vector of the correct size as fallback
            return [0.0] * self.vector_size






