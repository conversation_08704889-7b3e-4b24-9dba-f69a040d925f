from fastapi import HTT<PERSON>Exception, status as http_status
from typing import Optional, Any, Dict

from src.api.common.error_codes import <PERSON><PERSON>r<PERSON><PERSON>, ERROR_MESSAGES
from src.api.common.schemas import ErrorDetail, ApiResponse

class StandardAPIException(HTTPException):
    def __init__(
        self,
        status_code: int,
        error_code: ErrorCode,
        message: Optional[str] = None,
        field: Optional[str] = None,
        target: Optional[str] = None,
        details: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        # request_id: Optional[str] = None, # Captured from request context if available
    ):
        self.error_code = error_code
        # Prioritize provided message, then ERROR_MESSAGES, then a generic fallback.
        self.message = message if message is not None else ERROR_MESSAGES.get(error_code, "Bilinmeyen bir hata olu<PERSON>.")
        self.field = field
        self.target = target
        self.details = details
        # self.request_id = request_id # For future use with middleware

        error_detail_obj = ErrorDetail(
            code=self.error_code,
            message=self.message,
            field=self.field,
            target=self.target,
            details=self.details,
        )
        
        # The content of the exception's detail will be the ApiResponse model (error part)
        # This ensures that when FastAPI handles this HTTPException, the response body
        # matches the ApiResponse structure.
        api_response_content = ApiResponse[Any](error=error_detail_obj).model_dump(exclude_none=True)
        
        super().__init__(status_code=status_code, detail=api_response_content, headers=headers) 