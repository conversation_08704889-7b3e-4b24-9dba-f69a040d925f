from agno.tools import Toolkit, tool
from typing import Dict, Any, Optional, List, Type
from pydantic import BaseModel, Field

from src.rag_engine.interface import RAGInterface

class RFMAnalysisInput(BaseModel):
    """Input schema for the RFM analysis tool."""
    customer_ids: List[str] = Field(..., description="Müşteri ID'lerini içeren bir liste.")
    recency_list: List[int] = Field(..., description="Her müşteri için son satın almadan bugüne kadar geçen gün sayısı listesi.")
    frequency_list: List[int] = Field(..., description="Her müşteri için toplam satın alma sayısı listesi.")
    monetary_list: List[float] = Field(..., description="Her müşteri için toplam harcama miktarı listesi.")

class CLTVAnalysisInput(BaseModel):
    """Input schema for the CLTV analysis tool."""
    customer_ids: List[str] = Field(..., description="Müşteri ID'lerini içeren bir liste.")
    average_order_value: List[float] = Field(..., description="Her müşteri için ortalama sipariş değeri listesi.")
    purchase_frequency: List[float] = Field(..., description="Her müşteri için yıllık ortalama satın alma sıklığı listesi.")
    customer_lifespan: List[float] = Field(..., description="Her müşteri için işletmeyle olan tahmini yaşam süresi (yıl cinsinden) listesi.")
    churn_rate: float = Field(..., description="Müşteri kaybı oranı (0 ile 1 arasında bir değer). Tüm müşteriler için sabit varsayılır.")
    discount_rate: float = Field(0.1, description="Gelecekteki nakit akışlarını bugüne indirgemek için kullanılan iskonto oranı (0 ile 1 arasında bir değer). Tüm müşteriler için sabit varsayılır.")

class ChurnAnalysisInput(BaseModel):
    """Input schema for the Churn analysis tool."""
    customer_ids: List[str] = Field(..., description="Analiz edilecek müşteri ID'lerini içeren bir liste.")
    contract_length_months: List[int] = Field(..., description="Her müşteri için sözleşme süresi (ay cinsinden) listesi.")
    total_charges: List[float] = Field(..., description="Her müşteri için toplam harcama miktarı listesi.")
    monthly_charges: List[float] = Field(..., description="Her müşteri için aylık ortalama harcama miktarı listesi.")
    customer_service_calls: List[int] = Field(..., description="Her müşteri için son dönemde yapılan müşteri hizmetleri çağrı sayısı listesi.")
    inactive_months: List[int] = Field(..., description="Her müşteri için son dönemde aktif olmayan ay sayısı listesi.")
    has_tech_support: List[bool] = Field(..., description="Her müşteri için teknik destek alıp almadığına dair boolean liste (True/False).")
    payment_method: List[str] = Field(..., description="Her müşteri için kullanılan ödeme yöntemi listesi.")

class AnalyticsToolkit(Toolkit):
    def __init__(self):
        super().__init__(
            name="AnalyticsToolkit",
            instructions=None,
            add_instructions=False,
        )
        self.rag_interface = RAGInterface()

        self.register(self.rfm_analysis, name="rfm_analysis")
        self.register(self.cltv_analysis, name="cltv_analysis")
        self.register(self.churn_analysis, name="churn_analysis")

    def __name__(self):
        return "AnalyticsToolkit"

    def rfm_analysis(self, customer_ids: List[str], recency_list: List[int], frequency_list: List[int], monetary_list: List[float], user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        RFM analizi yapar.

        Her müşteri için RFM skorlarını hesaplar, ve müşteri id'si ile birlikte döner.

        Yenilik (R Skoru):
        5: En yakın zamanda alışveriş yapan müşterilerdir. Sadık olma ve tekrar satın alma olasılıkları yüksektir.
        4: Yakın zamanda alışveriş yapmış müşterilerdir. İlgi düzeyleri hala yüksektir.
        3: Orta derecede yakın zamanda alışveriş yapmış müşterilerdir. İlgi düzeyleri azalmaya başlamış olabilir.
        2: Uzun zaman önce alışveriş yapmış müşterilerdir. İlgi düzeyleri düşüktür, geri kazanma çabaları gerekebilir.
        1: En uzun zaman önce alışveriş yapmış müşterilerdir. Kaybedilmiş müşteri olarak değerlendirilebilirler.

        Sıklık (F Skoru):
        5: En sık alışveriş yapan müşterilerdir. Yüksek değerli ve sadık müşterilerdir.
        4: Sık alışveriş yapan müşterilerdir. Sadakat potansiyelleri yüksektir.
        3: Orta sıklıkta alışveriş yapan müşterilerdir.
        2: Nadiren alışveriş yapan müşterilerdir. Satın alma alışkanlıkları düşük olabilir.
        1: Çok nadir veya tek seferlik alışveriş yapan müşterilerdir.

        Parasal Değer (M Skoru):
        5: En yüksek toplam harcama yapan müşterilerdir. En değerli müşterilerdir.
        4: Yüksek harcama yapan müşterilerdir.
        3: Orta düzeyde harcama yapan müşterilerdir.
        2: Düşük harcama yapan müşterilerdir.
        1: En düşük toplam harcama yapan müşterilerdir.

        Args:
            customer_ids: Müşteri ID'lerini içeren bir liste.
            recency_list: Her müşteri için son satın almadan bugüne kadar geçen gün sayısı listesi.
            frequency_list: Her müşteri için toplam satın alma sayısı listesi.
            monetary_list: Her müşteri için toplam harcama miktarı listesi.
            user_id: Kullanıcı ID'si (kanca tarafından enjekte edilir).

        Returns:
            RFM analizi sonuçlarını içeren bir sözlük.
            Example: {'customer_1': (5, 3, 4), 'customer_2': (2, 5, 1), ...}
        """
        # Debug: Print list lengths
        print(f"RFM Analysis - List lengths: customer_ids={len(customer_ids)}, recency_list={len(recency_list)}, frequency_list={len(frequency_list)}, monetary_list={len(monetary_list)}")
        
        # Check if lists have equal lengths and provide detailed error message
        if not (len(customer_ids) == len(recency_list) == len(frequency_list) == len(monetary_list)):
            error_msg = f"Girdi listelerinin uzunlukları eşit olmalıdır. customer_ids: {len(customer_ids)}, recency_list: {len(recency_list)}, frequency_list: {len(frequency_list)}, monetary_list: {len(monetary_list)}"
            print(f"ERROR: {error_msg}")
            
            # Try to fix the length mismatch by taking the minimum length
            min_length = min(len(customer_ids), len(recency_list), len(frequency_list), len(monetary_list))
            print(f"Attempting to fix by truncating all lists to minimum length: {min_length}")
            
            customer_ids = customer_ids[:min_length]
            recency_list = recency_list[:min_length]
            frequency_list = frequency_list[:min_length]
            monetary_list = monetary_list[:min_length]
            
            print(f"After truncation - List lengths: customer_ids={len(customer_ids)}, recency_list={len(recency_list)}, frequency_list={len(frequency_list)}, monetary_list={len(monetary_list)}")

        num_customers = len(customer_ids)
        rfm_scores = {}

        # Create ranking indices for each metric
        recency_ranks = sorted(range(num_customers), key=recency_list.__getitem__)
        frequency_ranks = sorted(range(num_customers), key=frequency_list.__getitem__, reverse=True)
        monetary_ranks = sorted(range(num_customers), key=monetary_list.__getitem__, reverse=True)

        def get_score(rank, total):
            """Convert rank to 1-5 score"""
            if total == 0:
                return 1
            segment_size = total // 5
            if rank < segment_size:
                return 5
            elif rank < segment_size * 2:
                return 4
            elif rank < segment_size * 3:
                return 3
            elif rank < segment_size * 4:
                return 2
            else:
                return 1

        # Create score mappings
        recency_score_map = {customer_ids[i]: get_score(rank, num_customers) for rank, i in enumerate(recency_ranks)}
        frequency_score_map = {customer_ids[i]: get_score(rank, num_customers) for rank, i in enumerate(frequency_ranks)}
        monetary_score_map = {customer_ids[i]: get_score(rank, num_customers) for rank, i in enumerate(monetary_ranks)}

        # Combine scores for each customer
        for customer_id in customer_ids:
            r_score = recency_score_map.get(customer_id, 0)
            f_score = frequency_score_map.get(customer_id, 0)
            m_score = monetary_score_map.get(customer_id, 0)
            rfm_scores[customer_id] = (r_score, f_score, m_score)

        return {
            "success": True,
            "data": rfm_scores,
            "message": f"RFM analizi {len(customer_ids)} müşteri için başarıyla tamamlandı."
        }

    def cltv_analysis(self, customer_ids: List[str], average_order_value: List[float], purchase_frequency: List[float],
                     customer_lifespan: List[float], churn_rate: float, discount_rate: float = 0.1,
                     user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Müşteri Yaşam Boyu Değeri (CLTV) analizi yapar.

        Her müşteri için tahmini CLTV değerini hesaplar ve müşteri ID'si ile birlikte döner.

        Hesaplama aşağıdaki temel formül kullanılarak yapılır:
        CLTV = Ortalama Sipariş Değeri * Satın Alma Sıklığı * Müşteri Yaşam Süresi / (1 + Müşteri Kayıp Oranı - İskonto Oranı)

        Args:
            customer_ids: Müşteri ID'lerini içeren bir liste.
            average_order_value: Her müşteri için ortalama sipariş değeri listesi.
            purchase_frequency: Her müşteri için yıllık ortalama satın alma sıklığı listesi.
            customer_lifespan: Her müşteri için işletmeyle olan tahmini yaşam süresi listesi.
            churn_rate: Müşteri kaybı oranı.
            discount_rate: İskonto oranı.
            user_id: Kullanıcı ID'si (kanca tarafından enjekte edilir).

        Returns:
            CLTV analizi sonuçlarını içeren bir sözlük.
            Example: {'customer_1': 1500.75, 'customer_2': 2200.50, ...}
        """
        # Convert numeric strings to appropriate types if needed
        try:
            average_order_value = [float(x) for x in average_order_value]
            purchase_frequency = [float(x) for x in purchase_frequency]
            customer_lifespan = [float(x) for x in customer_lifespan]
        except (ValueError, TypeError) as e:
            raise ValueError(f"Numeric list conversion error: {str(e)}")

        if not (len(customer_ids) == len(average_order_value) == len(purchase_frequency) == len(customer_lifespan)):
            raise ValueError(f"Girdi listelerinin uzunlukları eşit olmalıdır. customer_ids: {len(customer_ids)}, average_order_value: {len(average_order_value)}, purchase_frequency: {len(purchase_frequency)}, customer_lifespan: {len(customer_lifespan)}")

        cltv_values = {}
        for i in range(len(customer_ids)):
            aov = average_order_value[i]
            pf = purchase_frequency[i]
            cl = customer_lifespan[i]

            # Calculate CLTV using the formula
            cltv = (aov * pf * cl) / (1 + churn_rate - discount_rate)
            cltv_values[customer_ids[i]] = round(cltv, 2)

        return {
            "success": True,
            "data": cltv_values,
            "message": f"CLTV analizi {len(customer_ids)} müşteri için başarıyla tamamlandı."
        }

    def churn_analysis(self, customer_ids: List[str], contract_length_months: List[int],
                      total_charges: List[float], monthly_charges: List[float],
                      customer_service_calls: List[int], inactive_months: List[int],
                      has_tech_support: List[bool], payment_method: List[str],
                      user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Müşteri Kaybı (Churn) analizi yapar.

        Belirli müşteri özelliklerini girdi olarak alarak, her müşteri için kayıp riskini değerlendirir
        ve risk seviyesini (Yüksek, Orta, Düşük) içeren bir sonuç döndürür.

        Değerlendirme aşağıdaki gibi basit kurallara dayanmaktadır:
        - Uzun sözleşme süresi ve yüksek toplam harcama: Düşük risk.
        - Kısa sözleşme süresi, düşük toplam harcama ve çok sayıda müşteri hizmetleri çağrısı/uzun inaktif süre: Yüksek risk.
        - Diğer durumlar: Orta risk.

        Args:
            customer_ids: Analiz edilecek müşteri ID'lerini içeren bir liste.
            contract_length_months: Her müşteri için sözleşme süresi listesi.
            total_charges: Her müşteri için toplam harcama miktarı listesi.
            monthly_charges: Her müşteri için aylık ortalama harcama miktarı listesi.
            customer_service_calls: Her müşteri için müşteri hizmetleri çağrı sayısı listesi.
            inactive_months: Her müşteri için aktif olmayan ay sayısı listesi.
            has_tech_support: Her müşteri için teknik destek durumu listesi.
            payment_method: Her müşteri için ödeme yöntemi listesi.
            user_id: Kullanıcı ID'si (kanca tarafından enjekte edilir).

        Returns:
            Churn analizi sonuçlarını içeren bir sözlük.
            Example: {'customer_1': 'Düşük', 'customer_2': 'Yüksek', ...}
        """
        # Convert numeric and boolean strings to appropriate types if needed
        try:
            contract_length_months = [int(float(x)) for x in contract_length_months]
            total_charges = [float(x) for x in total_charges]
            monthly_charges = [float(x) for x in monthly_charges]
            customer_service_calls = [int(float(x)) for x in customer_service_calls]
            inactive_months = [int(float(x)) for x in inactive_months]
            # Convert boolean strings
            has_tech_support = [bool(x) if isinstance(x, bool) else str(x).lower() in ['true', '1', 'yes'] for x in has_tech_support]
        except (ValueError, TypeError) as e:
            raise ValueError(f"Data type conversion error: {str(e)}")

        if not (len(customer_ids) == len(contract_length_months) == len(total_charges) ==
                len(monthly_charges) == len(customer_service_calls) == len(inactive_months) ==
                len(has_tech_support) == len(payment_method)):
            raise ValueError(f"Girdi listelerinin uzunlukları eşit olmalıdır. customer_ids: {len(customer_ids)}, contract_length_months: {len(contract_length_months)}, total_charges: {len(total_charges)}, monthly_charges: {len(monthly_charges)}, customer_service_calls: {len(customer_service_calls)}, inactive_months: {len(inactive_months)}, has_tech_support: {len(has_tech_support)}, payment_method: {len(payment_method)}")

        churn_risk = {}
        for i in range(len(customer_ids)):
            customer_id = customer_ids[i]
            contract_length = contract_length_months[i]
            total_charge = total_charges[i]
            service_calls = customer_service_calls[i]
            inactive = inactive_months[i]
            tech_support = has_tech_support[i]
            payment = payment_method[i].lower()

            risk_level = "Orta"  # Default to medium risk

            # High value, long-term customers = Low risk
            if contract_length > 12 and total_charge > 500:
                risk_level = "Düşük"
            # Short-term, low-value customers with issues = High risk
            elif contract_length < 6 and total_charge < 100 and (service_calls > 3 or inactive > 2):
                risk_level = "Yüksek"
            # Customers with many service calls or long inactive periods = High risk
            elif service_calls > 5 or inactive > 4:
                risk_level = "Yüksek"
            # Very short-term, very low-value customers = High risk
            elif contract_length < 3 and total_charge < 50:
                risk_level = "Yüksek"
            # Stable payment methods can reduce risk (if not already high)
            elif payment in ["kredi kartı", "banka havalesi"]:
                if risk_level != "Yüksek":
                    risk_level = "Orta"

            churn_risk[customer_id] = risk_level

        return {
            "success": True,
            "data": churn_risk,
            "message": f"Churn analizi {len(customer_ids)} müşteri için başarıyla tamamlandı."
        }

