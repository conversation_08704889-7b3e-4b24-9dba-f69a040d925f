"""
Data management endpoints for user-uploaded CSV/Excel tables
"""

from fastapi import APIRouter, HTTPException, Query, Path
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from src.api.common.schemas import <PERSON><PERSON><PERSON><PERSON>po<PERSON>, ErrorDetail
from src.rag_engine.interface import RAGInterface
from src.rag_engine.analitic.processor import PostgreSQLProcessor

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data")

# Initialize RAG interface
rag_interface = RAGInterface()

# Response Models
class TableColumnInfo(BaseModel):
    """Column information model"""
    name: str
    type: str
    nullable: bool

class TableMetadata(BaseModel):
    """Table metadata model"""
    id: int
    name: str
    original_file_name: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    row_count: int
    column_count: int
    columns: List[TableColumnInfo]

class TablePreviewData(BaseModel):
    """Table preview data model"""
    table_id: int
    table_name: str
    columns: List[str]
    sample_rows: List[Dict[str, Any]]
    total_rows: int
    preview_rows_count: int

class UserTablesResponse(BaseModel):
    """Response model for user tables list"""
    user_id: str
    tables_count: int
    tables: List[TableMetadata]

class TableDetailResponse(BaseModel):
    """Response model for detailed table information"""
    metadata: TableMetadata
    preview: TablePreviewData

# Endpoints

@router.get("/users/{user_id}/tables", response_model=ApiResponse[UserTablesResponse])
async def get_user_tables(
    user_id: str = Path(..., description="User ID to get tables for")
):
    """
    Get all tables owned by a user with metadata.
    
    This endpoint returns a comprehensive list of all tables owned by the user,
    including metadata such as row counts, column information, and creation dates.
    
    Args:
        user_id: The ID of the user whose tables to retrieve
        
    Returns:
        List of user tables with metadata
    """
    try:
        if not user_id or not user_id.strip():
            raise HTTPException(
                status_code=400,
                detail="User ID is required and cannot be empty"
            )
        
        # Get tables from the RAG interface
        tables_result = rag_interface.get_user_tables(user_id)
        
        # Handle both error cases and empty results
        if isinstance(tables_result, dict) and ("status" in tables_result or "error" in tables_result):
            error_msg = tables_result.get("message", "Failed to retrieve user tables")
            raise HTTPException(
                status_code=500,
                detail=f"Database error: {error_msg}"
            )
        
        # tables_result should be a list of tables
        if not isinstance(tables_result, list):
            raise HTTPException(
                status_code=500,
                detail="Unexpected response format from database"
            )
        
        # Convert to response format
        tables_data = []
        for table in tables_result:
            table_metadata = TableMetadata(
                id=table["id"],
                name=table["name"],
                original_file_name=table.get("original_file_name"),
                created_at=table.get("created_at"),
                updated_at=table.get("updated_at"),
                row_count=table.get("row_count", 0),
                column_count=len(table.get("columns", [])),
                columns=[
                    TableColumnInfo(
                        name=col["name"],
                        type=col["type"],
                        nullable=col.get("nullable", True)
                    )
                    for col in table.get("columns", [])
                ]
            )
            tables_data.append(table_metadata)
        
        response_data = UserTablesResponse(
            user_id=user_id,
            tables_count=len(tables_data),
            tables=tables_data
        )
        
        return ApiResponse[UserTablesResponse](
            success=True,
            data=response_data,
            message=f"Retrieved {len(tables_data)} tables for user {user_id}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrieving tables for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error retrieving user tables: {str(e)}"
        )


@router.get("/users/{user_id}/tables/{table_id}", response_model=ApiResponse[TableDetailResponse])
async def get_table_details(
    user_id: str = Path(..., description="User ID who owns the table"),
    table_id: int = Path(..., description="Table ID to get details for"),
    sample_rows: int = Query(5, ge=1, le=50, description="Number of sample rows to return (1-50)")
):
    """
    Get detailed information about a specific table including sample data.
    
    This endpoint returns both metadata and a preview of the table data,
    allowing users to understand the structure and content of their uploaded data.
    
    Args:
        user_id: The ID of the user who owns the table
        table_id: The ID of the table to get details for
        sample_rows: Number of sample rows to return (default: 5, max: 50)
        
    Returns:
        Detailed table information with sample data
    """
    try:
        if not user_id or not user_id.strip():
            raise HTTPException(
                status_code=400,
                detail="User ID is required and cannot be empty"
            )
        
        # First, get all user tables to find the specific table
        tables_result = rag_interface.get_user_tables(user_id)
        
        # Handle both error cases and empty results
        if isinstance(tables_result, dict) and ("status" in tables_result or "error" in tables_result):
            error_msg = tables_result.get("message", "Failed to retrieve user tables")
            raise HTTPException(
                status_code=500,
                detail=f"Database error: {error_msg}"
            )
        
        if not isinstance(tables_result, list):
            raise HTTPException(
                status_code=500,
                detail="Unexpected response format from database"
            )
        
        # Find the specific table
        target_table = None
        for table in tables_result:
            if table["id"] == table_id:
                target_table = table
                break
        
        if not target_table:
            raise HTTPException(
                status_code=404,
                detail=f"Table with ID {table_id} not found for user {user_id}"
            )
        
        # Create metadata
        table_metadata = TableMetadata(
            id=target_table["id"],
            name=target_table["name"],
            original_file_name=target_table.get("original_file_name"),
            created_at=target_table.get("created_at"),
            updated_at=target_table.get("updated_at"),
            row_count=target_table.get("row_count", 0),
            column_count=len(target_table.get("columns", [])),
            columns=[
                TableColumnInfo(
                    name=col["name"],
                    type=col["type"],
                    nullable=col.get("nullable", True)
                )
                for col in target_table.get("columns", [])
            ]
        )
        
        # Get sample data using the processor
        processor = rag_interface.get_processor()
        sample_query = f"SELECT * FROM {target_table['name']} LIMIT {sample_rows}"
        
        query_result = processor.execute_user_query(user_id, sample_query, max_rows=sample_rows)
        
        if not query_result.get("success", False):
            logger.warning(f"Failed to get sample data for table {table_id}: {query_result.get('data', {}).get('error', 'Unknown error')}")
            # Still return metadata, but with empty sample data
            preview_data = TablePreviewData(
                table_id=table_id,
                table_name=target_table["name"],
                columns=[col["name"] for col in target_table.get("columns", [])],
                sample_rows=[],
                total_rows=target_table.get("row_count", 0),
                preview_rows_count=0
            )
        else:
            query_data = query_result.get("data", {})
            sample_rows_data = query_data.get("rows", [])
            columns_list = query_data.get("columns", [col["name"] for col in target_table.get("columns", [])])
            
            preview_data = TablePreviewData(
                table_id=table_id,
                table_name=target_table["name"],
                columns=columns_list,
                sample_rows=sample_rows_data,
                total_rows=target_table.get("row_count", 0),
                preview_rows_count=len(sample_rows_data)
            )
        
        response_data = TableDetailResponse(
            metadata=table_metadata,
            preview=preview_data
        )
        
        return ApiResponse[TableDetailResponse](
            success=True,
            data=response_data,
            message=f"Retrieved details for table {target_table['name']}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrieving table details for user {user_id}, table {table_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error retrieving table details: {str(e)}"
        )


@router.get("/users/{user_id}/tables/{table_id}/preview", response_model=ApiResponse[TablePreviewData])
async def get_table_preview(
    user_id: str = Path(..., description="User ID who owns the table"),
    table_id: int = Path(..., description="Table ID to preview"),
    limit: int = Query(5, ge=1, le=100, description="Number of rows to return (1-100)"),
    offset: int = Query(0, ge=0, description="Number of rows to skip")
):
    """
    Get a preview of table data with pagination support.
    
    This endpoint allows users to browse through their table data with
    pagination support for larger datasets.
    
    Args:
        user_id: The ID of the user who owns the table
        table_id: The ID of the table to preview
        limit: Number of rows to return (default: 5, max: 100)
        offset: Number of rows to skip for pagination (default: 0)
        
    Returns:
        Table preview data with requested rows
    """
    try:
        if not user_id or not user_id.strip():
            raise HTTPException(
                status_code=400,
                detail="User ID is required and cannot be empty"
            )
        
        # Get user tables to find the specific table
        tables_result = rag_interface.get_user_tables(user_id)
        
        # Handle both error cases and empty results
        if isinstance(tables_result, dict) and ("status" in tables_result or "error" in tables_result):
            error_msg = tables_result.get("message", "Failed to retrieve user tables")
            raise HTTPException(
                status_code=500,
                detail=f"Database error: {error_msg}"
            )
        
        if not isinstance(tables_result, list):
            raise HTTPException(
                status_code=500,
                detail="Unexpected response format from database"
            )
        
        # Find the specific table
        target_table = None
        for table in tables_result:
            if table["id"] == table_id:
                target_table = table
                break
        
        if not target_table:
            raise HTTPException(
                status_code=404,
                detail=f"Table with ID {table_id} not found for user {user_id}"
            )
        
        # Get data with pagination
        processor = rag_interface.get_processor()
        preview_query = f"SELECT * FROM {target_table['name']} LIMIT {limit} OFFSET {offset}"
        
        query_result = processor.execute_user_query(user_id, preview_query, max_rows=limit)
        
        if not query_result.get("success", False):
            error_msg = query_result.get("data", {}).get("error", "Failed to retrieve table data")
            raise HTTPException(
                status_code=500,
                detail=f"Database query failed: {error_msg}"
            )
        
        query_data = query_result.get("data", {})
        rows_data = query_data.get("rows", [])
        columns_list = query_data.get("columns", [col["name"] for col in target_table.get("columns", [])])
        
        preview_data = TablePreviewData(
            table_id=table_id,
            table_name=target_table["name"],
            columns=columns_list,
            sample_rows=rows_data,
            total_rows=target_table.get("row_count", 0),
            preview_rows_count=len(rows_data)
        )
        
        return ApiResponse[TablePreviewData](
            success=True,
            data=preview_data,
            message=f"Retrieved {len(rows_data)} rows from table {target_table['name']}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting table preview for user {user_id}, table {table_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error getting table preview: {str(e)}"
        )


@router.delete("/users/{user_id}/tables/{table_id}", response_model=ApiResponse[Dict[str, Any]])
async def delete_user_table(
    user_id: str = Path(..., description="User ID who owns the table"),
    table_id: int = Path(..., description="Table ID to delete")
):
    """
    Delete a user's table and all associated data.
    
    This endpoint permanently removes a table and all its data.
    Use with caution as this action cannot be undone.
    
    Args:
        user_id: The ID of the user who owns the table
        table_id: The ID of the table to delete
        
    Returns:
        Confirmation of deletion
    """
    try:
        if not user_id or not user_id.strip():
            raise HTTPException(
                status_code=400,
                detail="User ID is required and cannot be empty"
            )
        
        # Get user tables to verify ownership and get table name
        tables_result = rag_interface.get_user_tables(user_id)
        
        # Handle both error cases and empty results
        if isinstance(tables_result, dict) and ("status" in tables_result or "error" in tables_result):
            error_msg = tables_result.get("message", "Failed to retrieve user tables")
            raise HTTPException(
                status_code=500,
                detail=f"Database error: {error_msg}"
            )
        
        if not isinstance(tables_result, list):
            raise HTTPException(
                status_code=500,
                detail="Unexpected response format from database"
            )
        
        # Find the specific table
        target_table = None
        for table in tables_result:
            if table["id"] == table_id:
                target_table = table
                break
        
        if not target_table:
            raise HTTPException(
                status_code=404,
                detail=f"Table with ID {table_id} not found for user {user_id}"
            )
        
        # Delete the table using the processor
        processor = rag_interface.get_processor()
        
        # This would need to be implemented in the processor
        # For now, we'll return a not implemented error
        raise HTTPException(
            status_code=501,
            detail="Table deletion functionality is not yet implemented"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error deleting table for user {user_id}, table {table_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error deleting table: {str(e)}"
        ) 