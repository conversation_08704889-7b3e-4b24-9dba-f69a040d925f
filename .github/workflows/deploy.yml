name: Production Deployment Pipeline

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy via SSH
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            set -euo pipefail
            export DEPLOY_DIR="/root/kai/KI"
            export LOG_DIR="$DEPLOY_DIR/logs"
            export COMPOSE_HTTP_TIMEOUT=120

            # Create log directory
            mkdir -p "$LOG_DIR"
            touch "$LOG_DIR/frontend.log" "$LOG_DIR/backend.log"
            chmod 666 "$LOG_DIR/frontend.log" "$LOG_DIR/backend.log"

            # Update repository
            cd "$DEPLOY_DIR"
            git reset --hard HEAD
            git fetch origin main
            git checkout main
            git pull origin main

            # Stop existing services
            tmux kill-session -t agno || true
            docker compose down --remove-orphans --timeout 60 || true

            # Build and start Docker services
            docker compose up -d --build --remove-orphans

            # Wait for containers to be running
            echo "Waiting for containers to start..."
            timeout=180
            while [ $timeout -gt 0 ]; do
                if [ $(docker compose ps -q | wc -l) -eq $(docker compose ps -q --filter status=running | wc -l) ]; then
                    break
                fi
                sleep 5
                timeout=$((timeout - 5))
            done

            # Verify all containers are running
            if [ $(docker compose ps -q | wc -l) -ne $(docker compose ps -q --filter status=running | wc -l) ]; then
                echo "Docker services failed to start"
                docker compose logs --tail 50
                exit 1
            fi

            # Install frontend dependencies
            cd "$DEPLOY_DIR/KI/frontend/frontend"
            npm ci --legacy-peer-deps --prefer-offline

            # Create tmux session for services
            tmux new-session -d -s agno -n "deploy_monitor"

            # Start backend - BIND TO 0.0.0.0
            tmux new-window -t agno -n backend
            tmux send-keys -t agno:backend "cd $DEPLOY_DIR/KI/backend" C-m
            tmux send-keys -t agno:backend "uv run main.py --host 0.0.0.0 --port 8000 2>&1 | tee -a $LOG_DIR/backend.log" C-m

            # Start frontend - DEVELOPMENT MODE with external access
            tmux new-window -t agno -n frontend
            tmux send-keys -t agno:frontend "cd $DEPLOY_DIR/KI/frontend/frontend" C-m
            tmux send-keys -t agno:frontend "HOST=0.0.0.0 npm run dev 2>&1 | tee -a $LOG_DIR/frontend.log" C-m

            # Create watchdog
            tmux new-window -t agno -n watchdog
            tmux send-keys -t agno:watchdog "cd $DEPLOY_DIR" C-m
            tmux send-keys -t agno:watchdog "while sleep 30; do" C-m
            tmux send-keys -t agno:watchdog "  if ! tmux has-session -t agno; then exit 1; fi" C-m
            tmux send-keys -t agno:watchdog "  if ! docker compose ps | grep -q 'Up'; then exit 1; fi" C-m
            tmux send-keys -t agno:watchdog "  if ! curl -s http://localhost:8000 >/dev/null; then echo 'Backend down!'; exit 1; fi" C-m
            tmux send-keys -t agno:watchdog "  if ! curl -s http://localhost:3000 >/dev/null; then echo 'Frontend down!'; exit 1; fi" C-m
            tmux send-keys -t agno:watchdog "done" C-m

            # Give services time to start
            echo "Waiting for services to initialize..."
            sleep 30

            # Open firewall ports
            ufw allow 8000/tcp comment 'Backend API' || true
            ufw allow 3000/tcp comment 'Frontend' || true
            ufw reload || true

            echo "Deployment successful. Services running in tmux session 'agno'"
            echo "Frontend accessible at: http://${{ secrets.SERVER_IP }}:3000"
            echo "Backend accessible at: http://${{ secrets.SERVER_IP }}:8000"
            echo "View logs at: $LOG_DIR"
            echo "Frontend log: tail -f $LOG_DIR/frontend.log"
            echo "Backend log: tail -f $LOG_DIR/backend.log"
