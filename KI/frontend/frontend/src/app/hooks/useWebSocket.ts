import { useState, useEffect, useRef, useCallback } from 'react';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: number;
}

interface UseWebSocketReturn {
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  sendMessage: (message: any) => void;
  lastMessage: WebSocketMessage | null;
  connect: (chatId: string) => void;
  disconnect: () => void;
}

const getWebSocketURL = (chatId: string) => {
  if (typeof window === 'undefined') {
    // Return a dummy URL for server-side rendering
    return 'ws://localhost:3000/apiv1/chats/dummy/ws'; 
  }
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.host;
  // Use the same host as the frontend, which will proxy WebSocket connections
  return `${protocol}//${host}/apiv1/chats/${chatId}/ws`;
};

const RECONNECT_INTERVAL = 3000; // 3 seconds
const MAX_RECONNECT_ATTEMPTS = 5;

export const useWebSocket = (): UseWebSocketReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  
  const websocketRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentChatIdRef = useRef<string | null>(null);

  // Clean up function
  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = null;
    }
    setIsConnected(false);
    setConnectionStatus('disconnected');
  }, []);

  // Connect to WebSocket
  const connect = useCallback((chatId: string) => {
    if (!chatId) {
      console.warn('❌ Cannot connect WebSocket: No chat ID provided');
      return;
    }

    // If already connected to the same chat, don't reconnect
    if (websocketRef.current && currentChatIdRef.current === chatId && isConnected) {
      console.log('✅ WebSocket already connected to chat:', chatId);
      return;
    }

    // Clean up existing connection
    cleanup();
    
    currentChatIdRef.current = chatId;
    setConnectionStatus('connecting');
    
    const wsUrl = getWebSocketURL(chatId);
    console.log('🔌 Connecting to WebSocket:', wsUrl);

    try {
      const ws = new WebSocket(wsUrl);
      websocketRef.current = ws;

      ws.onopen = () => {
        console.log('✅ WebSocket connected to chat:', chatId);
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0;
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', message);
          setLastMessage({
            ...message,
            timestamp: Date.now()
          });
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        websocketRef.current = null;

        // Only attempt to reconnect if it wasn't a manual disconnect
        if (event.code !== 1000 && reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS && currentChatIdRef.current) {
          setConnectionStatus('connecting');
          reconnectAttemptsRef.current += 1;
          console.log(`🔄 Attempting to reconnect (${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS})...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            if (currentChatIdRef.current) {
              connect(currentChatIdRef.current);
            }
          }, RECONNECT_INTERVAL);
        } else {
          setConnectionStatus('disconnected');
        }
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('❌ Error creating WebSocket connection:', error);
      setConnectionStatus('error');
    }
  }, [cleanup, isConnected]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    console.log('🔌 Manually disconnecting WebSocket');
    currentChatIdRef.current = null;
    cleanup();
  }, [cleanup]);

  // Send message through WebSocket
  const sendMessage = useCallback((message: any) => {
    if (websocketRef.current && isConnected) {
      try {
        const messageString = typeof message === 'string' ? message : JSON.stringify(message);
        websocketRef.current.send(messageString);
        console.log('📤 WebSocket message sent:', message);
      } catch (error) {
        console.error('❌ Error sending WebSocket message:', error);
      }
    } else {
      console.warn('⚠️ Cannot send message: WebSocket not connected');
    }
  }, [isConnected]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    isConnected,
    connectionStatus,
    sendMessage,
    lastMessage,
    connect,
    disconnect
  };
}; 