from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from contextlib import asynccontextmanager
from dotenv import load_dotenv
import os
import logging
import asyncio

# Load environment variables
load_dotenv()

# Get database connection string from environment variables
# Convert sync URL to async URL
DB_CONNECTION_STRING = os.getenv("DATABASE_URL", "postgresql://admin:adminpassword@localhost:5432/userdb_template")
if DB_CONNECTION_STRING.startswith("postgresql://"):
    DB_CONNECTION_STRING = DB_CONNECTION_STRING.replace("postgresql://", "postgresql+asyncpg://", 1)

logger = logging.getLogger(__name__)

class AsyncDatabase:
    """Async database connection and session management with connection pooling"""

    _instance = None
    _initialized = False
    _initialization_lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AsyncDatabase, cls).__new__(cls)
        return cls._instance

    async def __aenter__(self):
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()

    async def initialize(self, db_uri: str = None):
        """Initialize async database engine with connection pooling"""
        async with self._initialization_lock:
            if not self._initialized:
                connection_string = db_uri or DB_CONNECTION_STRING
                
                # Create async engine with connection pooling
                self.engine = create_async_engine(
                    connection_string,
                    # Connection pool settings for multi-user support
                    pool_size=20,           # Base connections in pool
                    max_overflow=30,        # Additional connections if needed
                    pool_pre_ping=True,     # Validate connections
                    pool_recycle=3600,      # Recycle connections after 1 hour
                    echo=False,             # Set to True for SQL debugging
                    future=True
                )
                
                # Create async session factory
                self.AsyncSessionLocal = async_sessionmaker(
                    self.engine,
                    class_=AsyncSession,
                    autocommit=False,
                    autoflush=False,
                    expire_on_commit=False,
                )
                
                self._initialized = True
                logger.info("Async database initialized with connection pooling")

    async def initialize_tables(self):
        """Initialize database tables if they don't exist"""
        from .models import Base
        
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables initialized successfully (async)")
        except Exception as e:
            logger.error(f"Error initializing database tables: {str(e)}")
            raise

    @asynccontextmanager
    async def get_session(self):
        """Get a new async database session with automatic cleanup"""
        if not self._initialized:
            await self.initialize()
            
        async with self.AsyncSessionLocal() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error(f"Database session error: {str(e)}")
                raise
            finally:
                await session.close()

    async def get_session_no_context(self) -> AsyncSession:
        """Get a new async session without context management (manual cleanup required)"""
        if not self._initialized:
            await self.initialize()
        return self.AsyncSessionLocal()

    async def cleanup(self):
        """Cleanup database connections"""
        if hasattr(self, 'engine') and self.engine:
            await self.engine.dispose()
            logger.info("Async database connections cleaned up")

# Global async database instance
_async_db = AsyncDatabase()

async def get_async_db(db_uri: str = None) -> AsyncDatabase:
    """Get the async database instance with optional custom connection URI"""
    if db_uri and not _async_db._initialized:
        await _async_db.initialize(db_uri)
    elif not _async_db._initialized:
        await _async_db.initialize()
    return _async_db

async def initialize_async_db() -> None:
    """Initialize the async database tables"""
    db = await get_async_db()
    await db.initialize_tables()

@asynccontextmanager
async def get_async_session():
    """Get a new async database session with automatic cleanup"""
    db = await get_async_db()
    async with db.get_session() as session:
        yield session

async def get_async_session_no_context() -> AsyncSession:
    """Get a new async session without context management"""
    db = await get_async_db()
    return await db.get_session_no_context()

class AsyncDatabaseMiddleware:
    """Middleware to manage database connections per request"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # Initialize database for HTTP requests
            await get_async_db()
        
        await self.app(scope, receive, send)

# Helper functions for backward compatibility
def get_session():
    """Backward compatibility - returns sync session"""
    from .database import get_session as sync_get_session
    return sync_get_session()

# Connection management utilities
class ConnectionManager:
    """Manages database connections for different user sessions"""
    
    def __init__(self):
        self.active_sessions = {}
        self.session_lock = asyncio.Lock()
    
    async def get_user_session(self, user_id: str) -> AsyncSession:
        """Get or create a session for a specific user"""
        async with self.session_lock:
            if user_id not in self.active_sessions:
                db = await get_async_db()
                session = await db.get_session_no_context()
                self.active_sessions[user_id] = session
            return self.active_sessions[user_id]
    
    async def close_user_session(self, user_id: str):
        """Close and remove a user's session"""
        async with self.session_lock:
            if user_id in self.active_sessions:
                session = self.active_sessions[user_id]
                await session.close()
                del self.active_sessions[user_id]
    
    async def cleanup_all_sessions(self):
        """Close all active sessions"""
        async with self.session_lock:
            for session in self.active_sessions.values():
                try:
                    await session.close()
                except Exception as e:
                    logger.error(f"Error closing session: {e}")
            self.active_sessions.clear()

# Global connection manager
connection_manager = ConnectionManager() 