"""
Main entry point for the KI API with async architecture support
"""
import uvicorn
import logging
import asyncio
import threading
from fastapi import Request, FastAPI, status
from starlette.middleware.base import BaseHTTPMiddleware
from dotenv import load_dotenv
import os
import sys
from pathlib import Path
from contextlib import asynccontextmanager

# API Router import
from src.api.routes import api_router

# Common API Schemas
from src.api.common.schemas import SimpleApiResponse as ApiResponse, HealthStatusData

# Load environment variables
load_dotenv()

# Add project root directory
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

# Import database initialization
from src.rag_engine.analitic.db.postgresql.database import initialize_db
from src.agents.datasets.database import initialize_chat_history_tables

# Import RAG interface for document indexing
from src.rag_engine.interface import RAGInterface

# Import async database if it exists (from main branch async architecture)
try:
    from src.rag_engine.analitic.db.postgresql.async_database import AsyncDatabaseManager
    ASYNC_DB_AVAILABLE = True
except ImportError:
    ASYNC_DB_AVAILABLE = False
    print("Async database not available, using sync database only")

# Import rate limiter if available (from main branch async architecture)
try:
    from src.api.common.rate_limiter import RateLimitMiddleware
    RATE_LIMITER_AVAILABLE = True
except ImportError:
    RATE_LIMITER_AVAILABLE = False
    print("Rate limiter not available")

# Import CORS
from fastapi.middleware.cors import CORSMiddleware

# Initialize global async database manager
async_db_manager = None

def background_index_documents():
    """
    Background function to index all documents in the data directory.
    Runs in a separate thread to avoid blocking the main application.
    """
    try:
        logging.info("Starting background document indexing...")

        # Initialize RAG interface
        rag_interface = RAGInterface()

        # Define data directory path
        data_dir = Path(__file__).parent / "data"

        if not data_dir.exists():
            logging.warning(f"Data directory not found: {data_dir}")
            return

        # Index all documents in the data directory
        result = rag_interface.bulk_index_directory(
            directory_path=data_dir,
            user_id=None,  # General knowledge base (no specific user)
            recursive=True
        )

        # Log results
        logging.info(f"Background indexing completed:")
        logging.info(f"  - Total files processed: {result['total_files']}")
        logging.info(f"  - Successful: {len(result['successful'])}")
        logging.info(f"  - Failed: {len(result['failed'])}")
        logging.info(f"  - Total chunks indexed: {result['total_chunks']}")

        # Log failed files if any
        if result['failed']:
            logging.warning("Failed to index the following files:")
            for failed in result['failed']:
                logging.warning(f"  - {failed['file']}: {failed['error']}")

        # Log successful files
        if result['successful']:
            logging.info("Successfully indexed files:")
            for success in result['successful']:
                logging.info(f"  - {success['file']}: {success['chunks']} chunks")

    except Exception as e:
        logging.error(f"Error during background document indexing: {str(e)}")
        logging.exception("Full traceback:")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    global async_db_manager
    
    try:
        # Reset AgentFactory singleton cache
        from src.agents.agno_agents.agent_factory import AgentFactory
        AgentFactory._initialized = False
        AgentFactory._instance = None
        logging.info("AgentFactory singleton reset successfully")

        # Initialize sync database
        initialize_db()
        initialize_chat_history_tables()
        logging.info("Sync database initialized successfully")
        
        # Initialize async database if available
        if ASYNC_DB_AVAILABLE:
            async_db_manager = AsyncDatabaseManager()
            await async_db_manager.initialize()
            logging.info("Async database manager initialized successfully")

        # Start background document indexing in a separate thread
        indexing_thread = threading.Thread(
            target=background_index_documents,
            daemon=True,  # Thread will be killed when main process exits
            name="DocumentIndexingThread"
        )
        indexing_thread.start()
        logging.info("Background document indexing thread started")

    except Exception as e:
        logging.error(f"Failed to initialize database: {str(e)}")
        raise e
    
    yield
    
    # Cleanup on shutdown
    if async_db_manager:
        await async_db_manager.close()
        logging.info("Async database manager closed")


# Create the FastAPI application
app = FastAPI(
    title="Kai Analysis API",
    description="Business analysis API for Kai with async architecture",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
cors_origins_env = os.getenv("CORS_ORIGINS")
cors_origins = cors_origins_env.split(",") if cors_origins_env else ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware if available
if RATE_LIMITER_AVAILABLE:
    # Create rate limiter instance
    rate_limiter = RateLimitMiddleware(
        calls_per_minute=100,
        calls_per_hour=5000,
        concurrent_requests_per_user=5
    )
    # Add as function-based middleware using BaseHTTPMiddleware
    from starlette.middleware.base import BaseHTTPMiddleware
    app.add_middleware(BaseHTTPMiddleware, dispatch=rate_limiter)

# Custom middleware to suppress WebSocket error logging
class SuppressWebSocketErrorsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path.endswith("/ws"):
            ws_logger = logging.getLogger("uvicorn.access")
            original_level = ws_logger.level
            ws_logger.setLevel(logging.ERROR)
            response = await call_next(request)
            ws_logger.setLevel(original_level)
            return response
        return await call_next(request)

# Add middleware
app.add_middleware(SuppressWebSocketErrorsMiddleware)

# Include API routes - use the api_router which includes all sub-routes
app.include_router(api_router, prefix="/apiv1")

# Root endpoint
@app.get("/")
async def root():
    return ApiResponse(
        success=True,
        data={"message": "Kai Analysis API is running with async architecture!"},
        message="API is operational"
    )

if __name__ == "__main__":
    """
    Run the FastAPI application with uvicorn
    Access the API docs at http://localhost:8000/docs (Swagger)
    or http://localhost:8000/redoc (ReDoc)
    """
    uvicorn.run(
        "main:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", 8000)),
        reload=True,
        workers=1  # Use 1 worker during development for async compatibility
    )
