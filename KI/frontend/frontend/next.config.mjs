/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    const backendUrl = process.env.BACKEND_API_URL || 'http://127.0.0.1:8000';
    
    return [
      // API routes proxy
      {
        source: '/apiv1/:path*',
        destination: `${backendUrl}/apiv1/:path*`,
      },
      {
        source: '/kai/:path*',
        destination: `${backendUrl}/kai/:path*`,
      },
      // WebSocket proxy - this handles the HTTP upgrade for WebSocket connections
      {
        source: '/ws/:path*',
        destination: `${backendUrl}/ws/:path*`,
      }
    ];
  },
  
  // <PERSON>le redirects to prevent backend URL exposure
  async redirects() {
    return [];
  },
  
  // Enable compression for better performance
  compress: true,
  
  // PoweredBy header removal for security
  poweredByHeader: false,
  
  // Custom headers for CORS if needed
  async headers() {
    return [
      {
        source: '/apiv1/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
