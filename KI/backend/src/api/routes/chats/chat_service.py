"""
Chat history service operations for KAI module.
Handles storing and retrieving chat messages using the PostgreSQL database.
"""

import uuid
from typing import List, Optional, Dict, Any, Tuple
from fastapi import HTTPException

from sqlalchemy import select
from src.rag_engine.analitic.db.postgresql.database import get_session
from src.rag_engine.analitic.db.postgresql.models import ChatMessageDB

def convert_task_list_to_strings(task_list):
    """Convert task list from dict format to string format if needed"""
    if not task_list:
        return task_list
    
    converted_list = []
    for item in task_list:
        if isinstance(item, dict):
            # Dict format: convert dict to string
            agent_name = item.get('agent_name', item.get('name', 'Unknown Agent'))
            task = item.get('task', item.get('task_for_agent', 'Unknown Task'))
            converted_list.append(f"{agent_name}: {task}")
        elif isinstance(item, str):
            # String format: already a string
            converted_list.append(item)
        else:
            # Fallback: convert to string
            converted_list.append(str(item))
    return converted_list

def convert_task_list_to_agents(task_list):
    """Convert task list to detailed agent objects if the data is available"""
    if not task_list:
        return None
    
    agents = []
    for item in task_list:
        if isinstance(item, dict) and all(key in item for key in ['agent_id', 'agent_name', 'task', 'reason']):
            # Full detailed format: use as-is
            agents.append({
                "agent_id": item['agent_id'],
                "agent_name": item['agent_name'],
                "task": item['task'],
                "reason": item['reason']
            })
        elif isinstance(item, dict):
            # Partial dict format: fill in missing data
            agent_name = item.get('agent_name', item.get('name', 'Unknown Agent'))
            task = item.get('task', item.get('task_for_agent', 'Unknown Task'))
            agents.append({
                "agent_id": item.get('agent_id', 'unknown'),
                "agent_name": agent_name,
                "task": task,
                "reason": item.get('reason', 'No reason provided')
            })
        # For string format, we can't reconstruct detailed info, so return None
        elif isinstance(item, str):
            return None
    
    return agents if agents else None

async def store_user_message(
    user_id: str,
    chat_id: str,
    text: str
) -> Dict[str, Any]:
    """
    Store a user's message in the database.
    
    Args:
        user_id: The user's ID
        chat_id: The chat session ID
        text: The message text
        
    Returns:
        Dictionary with message details, including the generated ID
    """
    message_id = uuid.uuid4()
    
    with get_session() as session:
        user_message = ChatMessageDB(
            id=message_id,
            chat_id=chat_id,
            user_id=user_id,
            task_key=None,  # User messages have no associated task
            sender='user',
            text=text,
            sources=None,
            error=False,
            is_task_output=False
        )
        
        session.add(user_message)
        session.commit()
        session.refresh(user_message)
    
    # Return formatted message
    return {
        "id": str(message_id),
        "sender": "user",
        "text": text,
        "timestamp": user_message.timestamp.timestamp() if user_message.timestamp else None,
    }

async def store_initial_agent_message(
    user_id: str,
    chat_id: str,
    task_key: str,
    plan_reason: str,
    plan_task_list: List[Any]  # Can be strings or detailed agent dicts
) -> Dict[str, Any]:
    """
    Store an initial agent message after planning stage.
    
    Args:
        user_id: The user's ID
        chat_id: The chat session ID
        task_key: The unique task key
        plan_reason: Planning stage reasoning
        plan_task_list: List of planned tasks (can be strings or detailed agent dicts)
        
    Returns:
        Dictionary with message details, including the generated ID
    """
    message_id = uuid.uuid4()
    
    with get_session() as session:
        # Create temporary text while processing
        temp_text = "İsteğiniz analiz ediliyor..."
        
        agent_message = ChatMessageDB(
            id=message_id,
            chat_id=chat_id,
            user_id=user_id,
            task_key=task_key,
            sender='agent',
            text=temp_text,
            sources=None,
            error=False,
            plan_reason=plan_reason,
            plan_task_list=plan_task_list,
            task_list=plan_task_list,  # Initially the same as planned
            is_task_output=False  # Not a final output yet
        )
        
        session.add(agent_message)
        session.commit()
        session.refresh(agent_message)
    
    # Convert task lists and get detailed agents if available
    converted_plan_task_list = convert_task_list_to_strings(plan_task_list) if plan_task_list else []
    detailed_agents = convert_task_list_to_agents(plan_task_list) if plan_task_list else None
    
    # Return formatted message
    return {
        "id": str(message_id),
        "sender": "agent",
        "text": temp_text,
        "timestamp": agent_message.timestamp.timestamp() if agent_message.timestamp else None,
        "plan": {
            "reason": plan_reason,
            "task_list": converted_plan_task_list,
            "agents": detailed_agents
        },
        "task_list": converted_plan_task_list,
        "isTaskOutput": False
    }

async def update_agent_message(
    task_key: str,
    text: str,
    sources: Optional[List[Dict[str, Any]]] = None,
    error: bool = False,
    task_list: Optional[List[str]] = None,
    assets: Optional[Dict[str, Any]] = None,
    is_task_output: bool = False,
    reports: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """
    Update an existing agent message with final output or error.
    
    Args:
        task_key: The unique task key to identify the message
        text: The final output text
        sources: List of sources (optional)
        error: Whether this is an error message
        task_list: Final list of executed tasks (may differ from plan)
        
    Returns:
        Dictionary with updated message details
    """
    with get_session() as session:
        # Find the message by task_key
        stmt = select(ChatMessageDB).where(ChatMessageDB.task_key == task_key)
        result = session.execute(stmt)
        message = result.scalars().first()
        
        if not message:
            raise ValueError(f"No agent message found with task_key: {task_key}")
        
        # Update the message
        message.text = text
        message.sources = sources or []
        message.error = error
        if task_list:
            message.task_list = task_list
        message.is_task_output = is_task_output or True
        
        # Handle assets including reports
        message_assets = assets or {}
        if reports:
            # Add reports to the assets
            if "reports" not in message_assets:
                message_assets["reports"] = []
            message_assets["reports"].extend(reports)
        
        message.assets = message_assets
        session.commit()
        session.refresh(message)
    
    # Convert task lists to ensure consistent format
    converted_plan_task_list = convert_task_list_to_strings(message.plan_task_list) if message.plan_task_list else []
    converted_task_list = convert_task_list_to_strings(message.task_list) if message.task_list else []
    
    # Try to get detailed agents if available
    detailed_agents = convert_task_list_to_agents(message.plan_task_list) if message.plan_task_list else None
    
    # Return formatted message
    return {
        "id": str(message.id),
        "sender": "agent",
        "text": message.text,
        "timestamp": message.timestamp.timestamp() if message.timestamp else None,
        "sources": message.sources,
        "error": message.error,
        "plan": {
            "reason": message.plan_reason,
            "task_list": converted_plan_task_list,
            "agents": detailed_agents
        },
        "task_list": converted_task_list,
        "isTaskOutput": message.is_task_output,
        "assets": message.assets
    }

async def get_chat_messages(chat_id: str, limit: Optional[int] = 100, skip: Optional[int] = 0) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get all messages for a chat session.
    
    Args:
        chat_id: The chat session ID
        
    Returns:
        List of message dictionaries in proper format for frontend
    """
    with get_session() as session:
        # Get messages sorted by timestamp
        stmt = select(ChatMessageDB).where(
            ChatMessageDB.chat_id == chat_id
        ).order_by(ChatMessageDB.timestamp)
        if limit:
            stmt = stmt.limit(limit)
        if skip:
            stmt = stmt.offset(skip)
        
        result = session.execute(stmt)
        messages = result.scalars().all()
    
    # Format messages for frontend
    formatted_messages = []
    for msg in messages:
        message = {
            "id": str(msg.id),
            "sender": msg.sender,
            "text": msg.text,
            "timestamp": msg.timestamp.timestamp() if msg.timestamp else None,
        }
        
        # Add agent-specific fields if applicable
        if msg.sender == 'agent':
            if msg.sources:
                message["sources"] = msg.sources
            
            if msg.error:
                message["error"] = msg.error
                
            if msg.plan_reason and msg.plan_task_list:
                # Convert plan task list to strings for backward compatibility
                converted_plan_task_list = convert_task_list_to_strings(msg.plan_task_list)
                # Try to get detailed agents if available
                detailed_agents = convert_task_list_to_agents(msg.plan_task_list)
                
                message["plan"] = {
                    "reason": msg.plan_reason,
                    "task_list": converted_plan_task_list,
                    "agents": detailed_agents
                }
                
            if msg.task_list:
                # Convert task list to strings if needed
                converted_task_list = convert_task_list_to_strings(msg.task_list)
                message["task_list"] = converted_task_list
                
            if msg.is_task_output:
                message["isTaskOutput"] = msg.is_task_output
        
        formatted_messages.append(message)
    
    return formatted_messages, len(formatted_messages)

async def delete_chat_messages(chat_id: str) -> int:
    """
    Delete all messages for a chat session.
    
    Args:
        chat_id: The chat session ID
        
    Returns:
        Number of messages deleted
    """
    with get_session() as session:
        # Delete all messages for the chat
        deleted = session.query(ChatMessageDB).filter(
            ChatMessageDB.chat_id == chat_id
        ).delete()
        
        session.commit()
    
    return deleted 