import { useState, useEffect, useRef, useCallback } from 'react';
import { sendMessageToAgent, createChat, loginUser } from '../services/api';
import { 
  generateId, 
  createThinkingMessage, 
  Message
} from '../utils/helpers';

// localStorage keys
const LOCAL_STORAGE_KEY = 'kai-chat-history';
const USER_STORAGE_KEY = 'kai-user-id';
const CHAT_STORAGE_KEY = 'kai-current-chat-id';

export interface ChatHistory {
  messages: Message[];
  isLoading: boolean;
  status: string;
}

export interface UseChatReturn {
  chatHistory: ChatHistory;
  sendMessage: (message: string) => Promise<void>;
  clearChat: () => void;
  scrollRef: React.RefObject<HTMLDivElement | null>;
  scrollToBottom: (behavior?: ScrollBehavior) => void;
  userId: string | null;
  currentChatId: string | null;
  login: (username: string) => Promise<void>;
  createNewChat: (chatName?: string) => Promise<void>;
}

export const useChat = (): UseChatReturn => {
  // Initialize user ID from localStorage
  const [userId, setUserId] = useState<string | null>(() => {
    try {
      return localStorage.getItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error("Failed to load user ID from localStorage:", error);
      return null;
    }
  });

  // Initialize current chat ID from localStorage
  const [currentChatId, setCurrentChatId] = useState<string | null>(() => {
    try {
      return localStorage.getItem(CHAT_STORAGE_KEY);
    } catch (error) {
      console.error("Failed to load chat ID from localStorage:", error);
      return null;
    }
  });

  // State to store chat history
  const [chatHistory, setChatHistory] = useState<ChatHistory>(() => {
    try {
      const savedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (savedHistory) {
        const parsedHistory: ChatHistory = JSON.parse(savedHistory);
        // Reset loading state on load
        return { ...parsedHistory, isLoading: false };
      }
    } catch (error) {
      console.error("Failed to load chat history from localStorage:", error);
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    }
    // Default initial state
    return { messages: [], isLoading: false, status: 'idle' };
  });
  
  // Reference for automatic scrolling
  const scrollRef = useRef<HTMLDivElement | null>(null);

  // Save chat history to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(chatHistory));
    } catch (error) {
      console.error("Failed to save chat history to localStorage:", error);
    }
  }, [chatHistory]);

  // Save user ID to localStorage whenever it changes
  useEffect(() => {
    try {
      if (userId) {
        localStorage.setItem(USER_STORAGE_KEY, userId);
      } else {
        localStorage.removeItem(USER_STORAGE_KEY);
      }
    } catch (error) {
      console.error("Failed to save user ID to localStorage:", error);
    }
  }, [userId]);

  // Save chat ID to localStorage whenever it changes
  useEffect(() => {
    try {
      if (currentChatId) {
        localStorage.setItem(CHAT_STORAGE_KEY, currentChatId);
      } else {
        localStorage.removeItem(CHAT_STORAGE_KEY);
      }
    } catch (error) {
      console.error("Failed to save chat ID to localStorage:", error);
    }
  }, [currentChatId]);

  // Scroll function
  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView({ behavior, block: 'end' });
    }
  }, []);

  // Login function
  const login = useCallback(async (username: string) => {
    try {
      console.log('📤 Logging in user:', username);
      const response = await loginUser(username);
      setUserId(response.user_id);
      console.log('✅ User logged in successfully:', response.user_id);
    } catch (error) {
      console.error('❌ Login failed:', error);
      throw error;
    }
  }, []);

  // Create new chat function
  const createNewChat = useCallback(async (chatName?: string) => {
    if (!userId) {
      throw new Error('User must be logged in to create a chat');
    }

    try {
      console.log('📤 Creating new chat for user:', userId);
      const response = await createChat(userId, chatName);
      setCurrentChatId(response.chat_id);
      // Clear current chat history when creating new chat
      setChatHistory({ messages: [], isLoading: false, status: 'idle' });
      console.log('✅ New chat created:', response.chat_id);
    } catch (error) {
      console.error('❌ Failed to create chat:', error);
      throw error;
    }
  }, [userId]);

  // Function to add user message and send to analysis
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    if (!userId || !currentChatId) {
      throw new Error('User must be logged in and have an active chat to send messages');
    }
    
    console.log('📩 Sending message:', message);
    
    // Add user message to chat history
    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    
    // Create thinking message
    const thinkingMessage = createThinkingMessage();
    
    // Update chat history with user message and thinking indicator
    setChatHistory(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage, thinkingMessage],
      isLoading: true,
      status: 'processing'
    }));
    
    // Immediately scroll to see the thinking message
    setTimeout(() => scrollToBottom(), 100);
    
    try {
      // Send the message to the API
      console.log('📤 Sending message to agent');
      const response = await sendMessageToAgent(userId, currentChatId, message);
      
      console.log('✅ Response received:', response);
      
      // Remove thinking message and add assistant response
      setChatHistory(prev => {
        const messagesWithoutThinking = prev.messages.filter(msg => msg.role !== 'thinking');
        
        const assistantMessage: Message = {
          id: generateId(),
          role: 'assistant',
          content: response.message,
          timestamp: new Date(),
          status: response.status === 'failed' ? 'error' : 'success'
        };
        
        return {
          ...prev,
          messages: [...messagesWithoutThinking, assistantMessage],
          isLoading: false,
          status: response.status
        };
      });
      
    } catch (error) {
      console.error('❌ Error sending message:', error);
      
      // Update message to show error
      setChatHistory(prev => {
        const messagesWithoutThinking = prev.messages.filter(msg => msg.role !== 'thinking');
        
        const errorMessage: Message = {
          id: generateId(),
          role: 'assistant',
          content: `An error occurred: ${(error as Error).message}. Please try again later.`,
          timestamp: new Date(),
          status: 'error'
        };
        
        return {
          ...prev,
          messages: [...messagesWithoutThinking, errorMessage],
          isLoading: false,
          status: 'error'
        };
      });
    }
  }, [userId, currentChatId, scrollToBottom]);

  // Function to clear chat history
  const clearChat = useCallback(() => {
    console.log('🧹 Clearing chat history');
    
    // Reset chat history
    setChatHistory({
      messages: [],
      isLoading: false,
      status: 'idle'
    });
    
    try {
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    } catch (error) {
      console.error("Failed to remove chat history from localStorage:", error);
    }
  }, []);

  return {
    chatHistory,
    sendMessage,
    clearChat,
    scrollRef,
    scrollToBottom,
    userId,
    currentChatId,
    login,
    createNewChat
  };
}; 