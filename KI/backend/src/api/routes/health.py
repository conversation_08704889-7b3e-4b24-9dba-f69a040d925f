"""
Health check endpoints to verify API status
"""

from fastapi import APIRouter, status
from pydantic import BaseModel

from src.api.common.schemas import ApiResponse, HealthStatusData

router = APIRouter(prefix="/health")


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    version: str


@router.get("/", response_model=ApiResponse[HealthStatusData], status_code=status.HTTP_200_OK)
async def health_check():
    health_data = HealthStatusData(status="healthy", version="1.0.0")
    return ApiResponse[HealthStatusData](data=health_data)