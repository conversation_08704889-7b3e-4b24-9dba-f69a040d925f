"""
WebSocket support for real-time task status notifications
"""

from typing import Dict, List, Any, Optional
import json
from fastapi import WebSocket, WebSocketDisconnect
from starlette.websockets import WebSocketDisconnect as StarletteWebSocketDisconnect
import websockets.exceptions
from .models import Task, TaskStatus
import logging
import time
from .redis_connector import get_redis_client, create_task_key, create_chat_tasks_key
import asyncio
from decimal import Decimal

# Set up basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DecimalEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle Decimal values"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)

# Redis key pattern for pending updates
PENDING_UPDATES_KEY_PREFIX = "ws:pending_updates:"

def get_pending_updates_key(chat_id: str) -> str:
    """Get the Redis key for pending updates for a specific chat_id."""
    return f"{PENDING_UPDATES_KEY_PREFIX}{chat_id}"

class ConnectionManager:
    """
    Manages WebSocket connections and broadcasting messages to clients
    """
    def __init__(self):
        # Store active connections by user ID and chat ID
        self.active_connections: Dict[str, List[WebSocket]] = {}
        
    async def connect(self, websocket: WebSocket, chat_id: str):
        """
        Connect a new WebSocket client for a specific chat
        """
        await websocket.accept()
        logger.info(f"New WebSocket connection accepted for chat_id: {chat_id}")
        
        # Store the connection by chat ID
        if chat_id not in self.active_connections:
            self.active_connections[chat_id] = []
        
        self.active_connections[chat_id].append(websocket)
        logger.info(f"WebSocket connection registered. Total connections for chat_id {chat_id}: {len(self.active_connections[chat_id])}")
        
        # Check if there are any pending updates for this chat_id
        await self.send_pending_updates(chat_id)
    
    def disconnect(self, websocket: WebSocket, chat_id: str):
        """
        Remove a WebSocket client when it disconnects
        """
        if chat_id in self.active_connections:
            # Get connection count before removal
            before_count = len(self.active_connections[chat_id])
            
            # Remove this specific websocket
            self.active_connections[chat_id] = [
                conn for conn in self.active_connections[chat_id] 
                if conn is not websocket
            ]
            
            # Log the removal
            after_count = len(self.active_connections[chat_id])
            logger.info(f"WebSocket disconnected from chat_id {chat_id}. Connections before: {before_count}, after: {after_count}")
            
            # Clean up empty entries
            if not self.active_connections[chat_id]:
                del self.active_connections[chat_id]
                logger.info(f"Removed empty connection list for chat_id {chat_id}")
        else:
            logger.warning(f"Attempted to disconnect WebSocket from non-existent chat_id: {chat_id}")
    
    async def broadcast_to_chat(self, chat_id: str, message: Dict[str, Any]):
        """
        Send a message to all clients subscribed to a specific chat ID
        """
        if chat_id not in self.active_connections:
            logger.warning(f"No active connections found for chat_id: {chat_id}")
            # No need to store here since send_task_update already stores all updates
            return
            
        # Convert message to JSON using custom encoder for Decimal values
        try:
            message_json = json.dumps(message, cls=DecimalEncoder)
        except TypeError as e:
            logger.error(f"Failed to serialize message for chat_id {chat_id}: {e}")
            logger.debug(f"Message content: {message}") # Log the problematic message structure
            return # Don't proceed if serialization fails

        # Send to all connections for this chat
        disconnected = []
        connections_to_notify = self.active_connections.get(chat_id, [])
        logger.info(f"Attempting to broadcast to {len(connections_to_notify)} connection(s) for chat_id: {chat_id}")

        for connection in connections_to_notify:
            try:
                await connection.send_text(message_json)
                logger.debug(f"Successfully sent message to a connection for chat_id: {chat_id}")
            except Exception as e:
                # Check if this is a critical connection error or temporary
                error_str = str(e).lower()
                critical_error = (
                    isinstance(e, (WebSocketDisconnect, StarletteWebSocketDisconnect)) or
                    "connection" in error_str and ("closed" in error_str or "reset" in error_str or "refused" in error_str) or
                    "disconnected" in error_str or
                    "not connected" in error_str
                )

                if critical_error:
                    # For definite connection issues, mark for disconnection
                    logger.error(f"Critical WebSocket error for chat_id {chat_id}: {e}")
                    disconnected.append(connection)
                else:
                    # For other errors, log but don't disconnect immediately
                    logger.warning(f"Non-critical error sending message to chat_id {chat_id}: {e}")
                    # Try one more time with a delay for non-critical errors
                    try:
                        await asyncio.sleep(0.1)  # Short delay
                        await connection.send_text(message_json)
                        logger.debug(f"Successfully sent message on retry for chat_id: {chat_id}")
                    except Exception as retry_e:
                        logger.error(f"Failed to send message on retry for chat_id {chat_id}: {retry_e}")
                        disconnected.append(connection)
        
        # Clean up any failed connections
        if disconnected:
            logger.warning(f"Disconnecting {len(disconnected)} failed connection(s) for chat_id: {chat_id}")
            for failed_connection in disconnected:
                # Pass the correct chat_id when disconnecting
                self.disconnect(failed_connection, chat_id)
    
    async def send_task_update(self, task: Task):
        """
        Send a task status update to all clients subscribed to the task's chat
        AND save the task state to Redis.
        """
        # Save task to Redis first
        try:
            redis_conn = get_redis_client()
            task_data_json = task.model_dump_json()

            # Use the standardized Redis key format for storing the task object
            redis_key_for_task_object = create_task_key(task.task_key)
            redis_conn.set(redis_key_for_task_object, task_data_json)

            # Add the original task_key to the set of tasks for this chat
            redis_key_for_chat_task_set = create_chat_tasks_key(task.chat_id)
            redis_conn.sadd(redis_key_for_chat_task_set, task.task_key)

            logger.info(f"Successfully saved task {task.task_key} to Redis.")
        except Exception as e:
            logger.error(f"Failed to save task {task.task_key} to Redis: {e}", exc_info=True)
            # Depending on requirements, one might choose to not broadcast if saving fails.
            # For now, we'll log the error and proceed with broadcasting.

        # Create status update message
        message = {
            "type": "task_update",
            "task_key": f"{task.task_key}",
            "status": task.status.value,  # Convert enum to string value
            "current_task": task.current_task,
            "progress": {
                "created_at": time.time(), # Current time
                "started_at": task.metadata.started_at,
                "completed_at": task.metadata.completed_at
            },
            "reports": task.reports if task.reports else []  # Include reports in WebSocket message
        }

        # Include error if present
        if task.error:
            message["error"] = task.error

        # Include output if task is completed
        if task.status == TaskStatus.COMPLETED and task.output:
            message["output"] = task.output

            # Özellikle report alanını kontrol et ve ekle
            if task.output and "report" in task.output:
                message["report"] = task.output["report"]

            # Özellikle markdown_output alanını kontrol et ve ekle
            if task.output and "markdown_output" in task.output:
                message["report"] = task.output["markdown_output"]

            # Ayrıca task_results içinde markdown_output var mı kontrol et
            if task.task_results:
                for result in task.task_results:
                    if isinstance(result, dict) and "result" in result:
                        result_data = result["result"]
                        if isinstance(result_data, dict) and "markdown_output" in result_data:
                            if "report" not in message:
                                message["report"] = result_data["markdown_output"]

        # Always store all task updates in Redis for future retrieval
        # This ensures all updates are available even if the client connects later
        try:
            await self.store_pending_update(task.chat_id, message)
            logger.info(f"Stored task update for chat_id: {task.chat_id}, task_key: {task.task_key}, status: {task.status.value}")
        except Exception as e:
            logger.error(f"Failed to store task update in Redis: {e}", exc_info=True)

        # Broadcast to all connections for this chat
        await self.broadcast_to_chat(task.chat_id, message)
    
    async def store_pending_update(self, chat_id: str, message: Dict[str, Any]):
        """
        Store a task update in Redis for a chat_id for future retrieval.
        This stores all task updates, not just when clients are disconnected.

        Args:
            chat_id: The chat ID to store the update for
            message: The message to store
        """
        try:
            redis_conn = get_redis_client()

            # Add timestamp to track when it was stored
            message["_stored_at"] = time.time()

            # Get task key and status from the message
            task_key = message.get("task_key", "unknown")
            status = message.get("status", "unknown")

            # Serialize the message using custom encoder for Decimal values
            message_json = json.dumps(message, cls=DecimalEncoder)

            # Store in Redis lists to maintain sequential update history
            # This allows us to replay the complete sequence of updates

            # 1. List of all updates for this chat (across all tasks)
            chat_updates_key = f"chat_updates:{chat_id}"
            redis_conn.rpush(chat_updates_key, message_json)

            # 2. List of all updates for this specific task
            task_updates_key = f"task_updates:{task_key}"
            redis_conn.rpush(task_updates_key, message_json)

            # 3. Set of all task keys for this chat (for easy retrieval)
            chat_tasks_key = f"chat_tasks:{chat_id}"
            redis_conn.sadd(chat_tasks_key, task_key)

            # 4. Also store the latest state in a hash for quick access to current state
            latest_updates_key = f"latest_updates:{chat_id}"
            redis_conn.hset(latest_updates_key, task_key, message_json)

            # Set expiration for all keys (7 days)
            expiration_time = 604800  # 7 days = 60*60*24*7
            redis_conn.expire(chat_updates_key, expiration_time)
            redis_conn.expire(task_updates_key, expiration_time)
            redis_conn.expire(chat_tasks_key, expiration_time)
            redis_conn.expire(latest_updates_key, expiration_time)

            logger.info(f"Stored update for chat_id: {chat_id}, task_key: {task_key}, status: {status}")
        except Exception as e:
            logger.error(f"Failed to store update in Redis: {e}", exc_info=True)
    
    async def send_pending_updates(self, chat_id: str):
        """
        Send all historical task updates to a client that just connected.
        This replays the complete sequence of all task updates for this chat.
        """
        try:
            redis_conn = get_redis_client()

            # Get the set of all task keys for this chat
            chat_tasks_key = f"chat_tasks:{chat_id}"
            task_keys = redis_conn.smembers(chat_tasks_key)

            if not task_keys:
                logger.debug(f"No tasks found for chat_id: {chat_id}")
                return

            logger.info(f"Found {len(task_keys)} tasks for chat_id: {chat_id}")

            # Process each task
            for task_key in task_keys:
                if isinstance(task_key, bytes):
                    task_key = task_key.decode('utf-8')

                # Get all updates for this task
                task_updates_key = f"task_updates:{task_key}"
                updates = redis_conn.lrange(task_updates_key, 0, -1)  # Get all elements

                if not updates:
                    logger.debug(f"No updates found for task: {task_key}")
                    continue

                logger.info(f"Sending {len(updates)} updates for task: {task_key}")

                # Send updates in chronological order
                for update_json in updates:
                    try:
                        if isinstance(update_json, bytes):
                            update_json = update_json.decode('utf-8')

                        message = json.loads(update_json)

                        # Send the update
                        if message.get("type") == "task_update":
                            await self.broadcast_to_chat(chat_id, message)
                            logger.debug(f"Sent update for task: {task_key}, status: {message.get('status')}")

                            # Add a small delay to prevent overwhelming the client
                            await asyncio.sleep(0.05)
                    except Exception as e:
                        logger.error(f"Failed to process update: {e}", exc_info=True)

            logger.info(f"Successfully sent all task updates for chat_id: {chat_id}")

        except Exception as e:
            logger.error(f"Failed to send task updates: {e}", exc_info=True)

    async def get_task_history(self, task_key: str) -> List[Dict[str, Any]]:
        """
        Retrieve the full history of status updates for a specific task.

        Args:
            task_key: The unique key of the task

        Returns:
            A list of task update messages in chronological order
        """
        try:
            redis_conn = get_redis_client()

            # Get all updates for this task from the Redis list
            task_updates_key = f"task_updates:{task_key}"
            updates = redis_conn.lrange(task_updates_key, 0, -1)  # Get all elements

            if not updates:
                logger.debug(f"No history found for task_key: {task_key}")
                return []

            # Process all updates
            history_items = []
            for update_json in updates:
                try:
                    if isinstance(update_json, bytes):
                        update_json = update_json.decode('utf-8')

                    message = json.loads(update_json)
                    history_items.append(message)
                except Exception as e:
                    logger.error(f"Failed to process history item: {e}", exc_info=True)

            # Updates are already in chronological order due to RPUSH
            return history_items

        except Exception as e:
            logger.error(f"Failed to retrieve task history: {e}", exc_info=True)
            return []

    async def get_latest_task_states(self, chat_id: str) -> Dict[str, Dict[str, Any]]:
        """
        Retrieve the latest state of all tasks for a chat.

        Args:
            chat_id: The chat ID

        Returns:
            A dictionary mapping task_key to the latest task state
        """
        try:
            redis_conn = get_redis_client()

            # Get latest states from the Redis hash
            latest_updates_key = f"latest_updates:{chat_id}"
            latest_states = redis_conn.hgetall(latest_updates_key)

            if not latest_states:
                logger.debug(f"No task states found for chat_id: {chat_id}")
                return {}

            # Process all task states
            result = {}
            for task_key, state_json in latest_states.items():
                try:
                    if isinstance(task_key, bytes):
                        task_key = task_key.decode('utf-8')

                    if isinstance(state_json, bytes):
                        state_json = state_json.decode('utf-8')

                    state = json.loads(state_json)
                    result[task_key] = state
                except Exception as e:
                    logger.error(f"Failed to process task state: {e}", exc_info=True)

            return result

        except Exception as e:
            logger.error(f"Failed to retrieve latest task states: {e}", exc_info=True)
            return {}

# Create a singleton connection manager to be used throughout the app
connection_manager = ConnectionManager() 