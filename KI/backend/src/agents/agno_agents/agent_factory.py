import os
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Callable
import json
import time
import asyncio
import concurrent.futures
from functools import partial
from agno.tools.decorator import tool
import yaml
from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import Open<PERSON><PERSON>hat
from agno.tools import Toolkit
from agno.tools.reasoning import ReasoningTools


from src.agents.agno_agents.memory_manager import MemoryManager
from src.agents.agno_agents.tool_manager import AgnoToolManager
from src.agents.agno_agents.user_context_manager import create_user_context_hook

from pydantic import BaseModel, Field



class PlannedAgent(BaseModel):
    agent_id: str
    agent_name: str
    reason: str
    task_for_agent: str


class PlanerOutputFormat(BaseModel):
    agents: List[PlannedAgent]

class ChartOutput(BaseModel):
    chart_schema: str = Field(description="The schema of chartjs that just data parts empty and it will fill weith data from the sql query")
    data_sql: str = Field(description="The SQL query to get data for the chart")

class TableOutput(BaseModel):
    """Model for SQL-based table output"""
    table_name: str = Field(..., description="Name of the table")
    table_description: Optional[str] = Field(None, description="Description of the table")
    data_sql: str = Field(..., description="SQL query to generate the table data")

class DirectTableOutput(BaseModel):
    """Model for direct data table output used for non creatable by direct sql query tables"""
    table_name: str = Field(..., description="Name of the table")
    table_description: Optional[str] = Field(None, description="Description of the table")
    data: List[Dict[str, Any]] = Field(..., description="Direct table data as list of dictionaries")

class OutputAgentOutputFormat(BaseModel):
    markdown_response: str
    charts: List[ChartOutput]   # Need implementation
    tables: List[TableOutput]


def logger_hook(
    function_name: str, function_call: Callable, arguments: Dict[str, Any]
):
    """Log the duration of the function call"""
    start_time = time.time()

    # Call the function
    result = function_call(**arguments)

    end_time = time.time()
    duration = end_time - start_time

    print("--------------------------------")
    print(f"Function {function_name} arguments: {arguments}")
    print(f"Function {function_name} took {duration:.2f} seconds to execute")
    print(f"Function {function_name} result: {result}")
    print(f"Function {function_name} result type: {type(result)}")
    print("--------------------------------")

    return result


class AgentRun(BaseModel):
    """A class to represent a run of an agent."""
    pass

class RunResponse(BaseModel):
    """A class to represent the response of a run."""
    content: Any

class AgentFactory:
    """
    Agent Factory for Agno Agents.

    This class provides a factory for creating Agno agents from YAML configuration files.
    Now supports async initialization and concurrent agent creation for multi-user scenarios.
    """
    _instance = None
    _initialized = False
    _initialization_lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AgentFactory, cls).__new__(cls)
        return cls._instance

    def __init__(self, model_config: Optional[Dict] = None, config_file: str = "src/agents/agno_agents/config/agents.yaml"):
        """
        Initialize the Agent Factory.

        Args:
            model_config: The configuration for the model to use for all agents.
                          If None, the default model resolution per agent will apply.
        """
        if not AgentFactory._initialized:
            # Thread pool for concurrent agent creation
            self.creation_executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=5,
                thread_name_prefix="agent_creation"
            )
            
            base_config_path = Path(__file__).parent / "config"

            # Load agents configuration
            with open(base_config_path / "agents.yaml", "r", encoding="utf-8") as f:
                self.agents_config_data = yaml.safe_load(f)

            # Load planner configuration
            with open(base_config_path / "planner.yaml", "r", encoding="utf-8") as f:
                self.planner_config_file_data = yaml.safe_load(f)

            # Load output agent configuration
            with open(base_config_path / "output_agent.yaml", "r", encoding="utf-8") as f:
                self.output_agent_config_file_data = yaml.safe_load(f)

            # Initialize the tool manager
            self.tool_manager = AgnoToolManager()
            self._all_tools_map = self.tool_manager.get_all_tool_objects_map()

            # Prepare context of available dynamic agents for the planner
            available_agents_parts = ["Available dynamic agents for task assignment (name - role):"]
            dynamic_agents_config_for_planner = self.agents_config_data.get("agents", {})
            for agent_key, agent_data in dynamic_agents_config_for_planner.items():
                name = agent_data.get("name", agent_key)
                role = agent_data.get("role", "No role defined")
                available_agents_parts.append(f"- agent_key: '{agent_key}' -> {name} - {role} - goal: {agent_data.get('goal', 'No goal defined')}")
            available_agents_info_str = "\\n".join(available_agents_parts)

            # DEBUG: Print available agents list (can be removed in production)
            print("🔍 DEBUG: Available agents list sent to Planner:")
            print(available_agents_info_str)
            print("🔍 DEBUG: Agent keys in agents.yaml:")
            print(list(dynamic_agents_config_for_planner.keys()))

            # Create and store planner agent
            planner_main_key = next(iter(self.planner_config_file_data))
            planner_config = self.planner_config_file_data[planner_main_key]
            self.planner_agent = self._build_agent_instance(
                agent_name_key=planner_main_key,
                agent_config=planner_config,
                model_config=model_config,
                additional_instructions_prefix=available_agents_info_str,
                output_format=PlanerOutputFormat,
                agent_type="planner"
            )

            # Create and store output agent
            output_agent_main_key = next(iter(self.output_agent_config_file_data))
            output_agent_config = self.output_agent_config_file_data[output_agent_main_key]
            self.output_agent = self._build_agent_instance(
                agent_name_key=output_agent_main_key,
                agent_config=output_agent_config,
                model_config=model_config,
                output_format=OutputAgentOutputFormat,
                agent_type="output_agent"
                )

            # Dictionary to store dynamically created agents
            self.dynamic_agents: Dict[str, Agent] = {}
            
            # Agent creation cache to prevent duplicate creation
            self.agent_creation_cache = {}
            self.cache_lock = asyncio.Lock()

            self.create_all_dynamic_agents(model_config=model_config)

            AgentFactory._initialized = True

    async def _build_agent_instance_async(
        self,
        agent_name_key: str,
        agent_config: Dict,
        model_config: Optional[Dict] = None,
        additional_instructions_prefix: Optional[str] = None,
        output_format: Optional[type[BaseModel]] = None,
        agent_type: str = "dynamic",
    ) -> Agent:
        """
        Async version of _build_agent_instance for concurrent agent creation.
        """
        loop = asyncio.get_event_loop()
        
        # Create a partial function for the synchronous agent creation
        agent_builder = partial(
            self._build_agent_instance,
            agent_name_key,
            agent_config,
            model_config,
            additional_instructions_prefix,
            output_format,
            agent_type
        )
        
        # Run agent creation in thread pool to avoid blocking
        try:
            agent = await loop.run_in_executor(
                self.creation_executor,
                agent_builder
            )
            return agent
        except Exception as e:
            print(f"Error in async agent creation for {agent_name_key}: {e}")
            raise

    async def create_agent_async(
        self,
        agent_name: str,
        model_config: Optional[Dict] = None,
    ) -> Optional[Agent]:
        """
        Async version of create_agent for better concurrency.
        Uses caching to prevent duplicate agent creation.
        """
        cache_key = f"{agent_name}_{id(model_config)}"
        
        # Check cache first
        async with self.cache_lock:
            if cache_key in self.agent_creation_cache:
                return self.agent_creation_cache[cache_key]
        
        # Agent not in cache, create it
        agent_config = self.agents_config_data.get("agents", {}).get(agent_name)
        if not agent_config:
            print(f"Agent configuration not found for: {agent_name}")
            return None

        try:
            agent = await self._build_agent_instance_async(
                agent_name_key=agent_name,
                agent_config=agent_config,
                model_config=model_config,
                agent_type="dynamic"
            )
            
            # Cache the created agent
            async with self.cache_lock:
                self.agent_creation_cache[cache_key] = agent
                self.dynamic_agents[agent_name] = agent
            
            return agent
            
        except Exception as e:
            print(f"Failed to create agent {agent_name}: {e}")
            return None

    async def get_agent_async(self, agent_key_or_name: str) -> Optional[Agent]:
        """
        Async version of get_agent that creates agents on-demand if needed.
        """
        # Check if agent already exists
        if agent_key_or_name in self.dynamic_agents:
            return self.dynamic_agents[agent_key_or_name]
        
        # Try to create the agent if it doesn't exist
        return await self.create_agent_async(agent_key_or_name)

    async def create_multiple_agents_concurrent(
        self,
        agent_names: List[str],
        model_config: Optional[Dict] = None
    ) -> Dict[str, Optional[Agent]]:
        """
        Create multiple agents concurrently for better performance.
        """
        tasks = []
        for agent_name in agent_names:
            task = asyncio.create_task(
                self.create_agent_async(agent_name, model_config)
            )
            tasks.append((agent_name, task))
        
        results = {}
        for agent_name, task in tasks:
            try:
                agent = await task
                results[agent_name] = agent
            except Exception as e:
                print(f"Failed to create agent {agent_name}: {e}")
                results[agent_name] = None
        
        return results

    def cleanup(self):
        """Cleanup resources including thread pool executors"""
        if hasattr(self, 'creation_executor'):
            self.creation_executor.shutdown(wait=True)
            print("Agent creation executor shut down successfully")

    def _build_agent_instance(
        self,
        agent_name_key: str,
        agent_config: Dict,
        model_config: Optional[Dict] = None,
        additional_instructions_prefix: Optional[str] = None,
        output_format: Optional[type[BaseModel]] = None,
        agent_type: str = "default",
    ) -> Agent:
        """
        Helper method to build an agent instance from its configuration.
        """
        # Model config'e agent_type ekleme
        effective_model_config = model_config.copy() if model_config else {}
        effective_model_config["agent_type"] = agent_type

        model = self._get_model(effective_model_config)

        tool_names_from_config: List[str] = agent_config.get("tools", [])
        selected_tools: List = [
            self._all_tools_map[name] for name in tool_names_from_config if name in self._all_tools_map
        ]
        if not selected_tools and tool_names_from_config:
            print("Avaliable toolkits names: ", list(self._all_tools_map.keys()))
            print(f"Warning: Agent '{agent_config.get('name', agent_name_key)}' specified tools {tool_names_from_config}, but none were found/matched.")


        memory_manager = MemoryManager() # Each agent gets its own memory manager for now

        # Determine agent settings based on output format
        agent_markdown_output = not bool(output_format)  # Disable markdown if output format is specified


        current_instructions = agent_config.get("instructions", [])
        if not isinstance(current_instructions, list):
            current_instructions = [str(current_instructions)]

        # Prepend additional instructions if provided
        if additional_instructions_prefix:
            current_instructions.insert(0, additional_instructions_prefix)

        # Add explicit JSON formatting instructions if output format is specified
        if output_format:
            json_instructions = [
                "CRITICAL: Your response MUST be a valid JSON object matching this exact schema:",
                json.dumps(output_format.model_json_schema(), indent=2),
                "DO NOT include any text outside the JSON object.",
                "DO NOT wrap the JSON in markdown code blocks.",
                "DO NOT wrap the JSON in XML-like tags.",
                "DO NOT add any explanatory text before or after the JSON.",
                "Ensure all required fields are present and properly formatted.",
                "If a list field would be empty, include it as an empty list [].",
                "Properly escape any special characters in string fields.",
                "Your entire response should be ONLY the JSON object, nothing else."
            ]
            current_instructions.extend(json_instructions)

        # Create secure user context hook for tool execution
        secure_hook = create_user_context_hook()
        
        agent = Agent(
            name=agent_config.get("name", agent_name_key),
            role=agent_config.get("role", ""),
            model=model,
            tools=selected_tools,
            enable_user_memories=True,
            memory=memory_manager.get_memory_instance(),
            instructions=current_instructions,
            markdown=agent_markdown_output,
            show_tool_calls=True,
            debug_mode=True,
            tool_hooks=[secure_hook]
        )

        if output_format:
            print(f"Setting response_model for agent {agent_config.get('name', agent_name_key)} to {output_format}")
            # Configure for structured JSON output
            agent.response_model = output_format
            agent.use_json_mode = True  # Enable JSON mode
            agent.markdown = False  # Disable markdown mode
            agent.structured_outputs = True  # Enable structured outputs
            # Add schema validation
            agent.validate_response = True
            # Force JSON response
            agent.force_json = True
            # Disable any text wrapping
            agent.wrap_response = False
            print(f"Agent {agent_config.get('name', agent_name_key)} configured with JSON mode: {agent.use_json_mode}, markdown: {agent.markdown}")
            print(f"Structured outputs enabled: {getattr(agent, 'structured_outputs', None)}")
            print(f"Response model schema: {output_format.model_json_schema()}")
            print(f"Schema validation enabled: {getattr(agent, 'validate_response', None)}")
            print(f"Force JSON enabled: {getattr(agent, 'force_json', None)}")
            print(f"Response wrapping disabled: {not getattr(agent, 'wrap_response', True)}")

        return agent

    def create_agent(self, agent_config: Dict[str, Any], chat_id: str, is_final_step: bool = False) -> Agent:
        agent_id = agent_config["id"]
        agent_name = agent_config["name"]
        
        agent = Agent(
            id=agent_id,
            name=agent_name,
            role=agent_config.get("role", ""),
            model=self._get_model({"agent_type": "legacy", **agent_config} if agent_config else {"agent_type": "legacy"}),
            tools=agent_config.get("tools", []),
            enable_user_memories=True,
            memory=MemoryManager().get_memory_instance(),
            instructions=agent_config.get("instructions", []),
            markdown=False,
            show_tool_calls=True,
            debug_mode=True,
            tool_hooks=[create_user_context_hook()]
        )

        return agent

    def create_all_dynamic_agents(
        self,
        model_config: Optional[Dict] = None,
        exclude: Optional[List[str]] = None,
    ) -> Dict[str, Agent]:
        """
        Create all dynamic agents from the 'agents.yaml' configuration.
        Planner and Output agents are created during factory initialization.

        Args:
            model_config: The configuration for the model to use.
                          If None, the default model is used.
            exclude: A list of agent names to exclude from creation.

        Returns:
            A dictionary of dynamic agent names to Agno agents.
        """
        # Initialize the agents dictionary
        created_agents: Dict[str, Agent] = {}

        dynamic_agents_config = self.agents_config_data.get("agents", {})
        for agent_name_key, agent_conf_data in dynamic_agents_config.items():
            if exclude and agent_name_key in exclude:
                continue

            agent_instance = self._build_agent_instance(
                agent_name_key=agent_name_key,
                agent_config=agent_conf_data,
                model_config=model_config,
                agent_type="dynamic"
            )
            created_agents[agent_name_key] = agent_instance

        self.dynamic_agents.update(created_agents) # Optionally store them in the factory instance
        print(f"Initialized {len(self.dynamic_agents)} dynamic agents: {list(self.dynamic_agents.keys())}")
        return created_agents

    def get_agent(self, agent_key_or_name: str) -> Optional[Agent]:
        """
        Retrieves a pre-created agent (planner, output, or dynamic).
        Dynamic agents are now created during factory initialization.
        It tries to match by agent_key (for dynamic agents) or by well-known names for planner/output.
        """
        # Check for planner or output agent by their typical names or configured names
        if agent_key_or_name == "planner" or (self.planner_agent and agent_key_or_name == self.planner_agent.name):
            return self.planner_agent
        if agent_key_or_name == "output_agent" or (self.output_agent and agent_key_or_name == self.output_agent.name):
             return self.output_agent

        # Try to get by YAML key from the now-populated dynamic_agents dictionary
        agent = self.dynamic_agents.get(agent_key_or_name)
        if agent:
            return agent

        # Fallback: if not found by key, iterate and check by human-readable name for dynamic agents
        # This might be slow if there are many agents and is less precise.
        # Consider if this fallback is truly needed or if strict key-based lookup is preferred.
        for dyn_agent_key, dyn_agent_instance in self.dynamic_agents.items():
            if dyn_agent_instance.name == agent_key_or_name:
                print(f"Warning: Agent '{agent_key_or_name}' was found by its human-readable name, not its key ('{dyn_agent_key}'). Prefer using agent keys for lookup.")
                return dyn_agent_instance

        return None # If not found by key or specific name

    def get_planner_agent(self) -> Agent:
        """
        Returns the initialized planner agent.
        """
        return self.planner_agent

    def get_output_agent(self) -> Agent:
        """
        Returns the initialized output agent.
        """
        return self.output_agent

    def get_agents_by_names(self, agent_keys_or_names: List[str]) -> List[Agent]:
        """
        Retrieves a list of agents based on a list of agent keys or names.
        It will attempt to find planner, output, or dynamic agents.
        Agents not found will be skipped.
        """
        found_agents: List[Agent] = []
        for key_or_name in agent_keys_or_names:
            agent = self.get_agent(key_or_name)
            if agent:
                found_agents.append(agent)
            else:
                print(f"Warning: Agent with key/name '{key_or_name}' not found and was skipped.")
        return found_agents

    def _get_model(self, model_config: Optional[Dict] = None) -> Union[Claude, OpenAIChat]:
        """
        Get the model to use for the agent.

        Args:
            model_config: The configuration for the model to use.
                          If None, the default model is used.

        Returns:
            An Agno model.
        """
        # Use the provided model config or get from environment
        effective_model_config = model_config if model_config is not None else {}

        provider = effective_model_config.get("provider", os.getenv("LLM_PROVIDER", "openai")).lower()

        # Model ID seçimi - agent tipine göre
        if effective_model_config.get("agent_type") == "output_agent":
            model_id = effective_model_config.get("model", os.getenv("OUTPUT_AGENT_LLM_MODEL", os.getenv("LLM_MODEL")))
            print(f"🤖 Output agent using model: {model_id}")
        else:
            model_id = effective_model_config.get("model", os.getenv("LLM_MODEL"))

        print(f"🤖 Agent using model -------------{ effective_model_config.get("agent_type")}-----------------: {model_id}")

        api_key = effective_model_config.get("api_key", os.getenv("LLM_API_KEY"))
        base_url = effective_model_config.get("base_url", os.getenv("LLM_URL"))

        if not model_id:
            raise ValueError("LLM_MODEL environment variable or model_config['model'] must be set.")

        if provider == "anthropic":
            return Claude(
                id=model_id,
                api_key=api_key,
                base_url=base_url,
            )
        # Default to OpenAI
        return OpenAIChat(
            id=model_id,
            api_key=api_key,
            base_url=base_url,
            max_tokens=80000,  # Increase max tokens
            temperature=0.7,  # Add some creativity 
        )

    def get_agent_by_name(self, agent_name: str) -> Optional[Agent]:
        """
        Get a agent instance by its name.
        """
        return self.get_agent(agent_name)
