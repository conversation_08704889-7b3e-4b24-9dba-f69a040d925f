import pandas as pd
import numpy as np
import io
import json
import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union
from pydantic import BaseModel, Field
import os
import tempfile
from dotenv import load_dotenv
import asyncio

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class PostgreSQLColumn(BaseModel):
    """Pydantic model for PostgreSQL column definition"""
    name: str
    postgresql_type: str
    nullable: bool = True

class ColumnSampleData(BaseModel):
    """Pydantic model for column sample data"""
    column_name: str
    samples: List[Any] = Field(default_factory=list)

class PostgreSQLSchema(BaseModel):
    """Pydantic model for PostgreSQL schema definition"""
    table_name: str
    columns: List[PostgreSQLColumn]
    sample_data: List[ColumnSampleData]
    row_count: int
    create_table_sql: str

class CSVProcessingResult(BaseModel):
    """Pydantic model for CSV processing result"""
    success: bool
    row_count: Optional[int] = None
    column_count: Optional[int] = None
    schema_info: Optional[PostgreSQLSchema] = None  # Renamed from 'schema' to avoid conflict with BaseModel
    error: Optional[str] = None

def normalize_csv_data(csv_content: str, delimiter: str = ',') -> Optional[pd.DataFrame]:
    """
    Normalize CSV data by cleaning and processing it.
    
    Args:
        csv_content: String content of the CSV file
        delimiter: CSV delimiter character (default: ',')
        
    Returns:
        Pandas DataFrame with normalized data or None if processing fails
    """
    try:
        # First attempt: try to read CSV normally
        try:
            df = pd.read_csv(io.StringIO(csv_content), delimiter=delimiter)
        except pd.errors.ParserError as parse_error:
            logger.warning(f"Initial CSV parsing failed: {str(parse_error)}. Attempting error recovery...")
            
            # Second attempt: handle malformed lines by skipping bad lines
            try:
                df = pd.read_csv(
                    io.StringIO(csv_content), 
                    delimiter=delimiter,
                    on_bad_lines='skip',  # Skip bad lines instead of failing
                    warn_bad_lines=True
                )
                logger.warning(f"CSV parsed with some bad lines skipped")
            except:
                # Third attempt: try with error_bad_lines=False (older pandas versions)
                try:
                    df = pd.read_csv(
                        io.StringIO(csv_content), 
                        delimiter=delimiter,
                        error_bad_lines=False,
                        warn_bad_lines=True
                    )
                    logger.warning(f"CSV parsed with error_bad_lines=False")
                except:
                    # Fourth attempt: force consistent field count by padding
                    lines = csv_content.strip().split('\n')
                    if len(lines) < 2:
                        raise ValueError("CSV must have at least a header and one data row")
                    
                    # Get expected field count from header
                    header = lines[0]
                    expected_fields = len(header.split(delimiter))
                    
                    # Fix inconsistent lines
                    fixed_lines = []
                    skipped_lines = 0
                    
                    for i, line in enumerate(lines):
                        fields = line.split(delimiter)
                        if len(fields) == expected_fields:
                            fixed_lines.append(line)
                        elif len(fields) < expected_fields:
                            # Pad with empty fields
                            while len(fields) < expected_fields:
                                fields.append('')
                            fixed_lines.append(delimiter.join(fields))
                        else:
                            # Too many fields - skip the line or truncate
                            if i == 0:  # Don't skip header
                                fixed_lines.append(line)
                            else:
                                # Truncate extra fields
                                truncated_fields = fields[:expected_fields]
                                fixed_lines.append(delimiter.join(truncated_fields))
                                skipped_lines += 1
                    
                    if skipped_lines > 0:
                        logger.warning(f"Fixed {skipped_lines} lines with inconsistent field counts")
                    
                    # Try parsing the fixed content
                    fixed_content = '\n'.join(fixed_lines)
                    df = pd.read_csv(io.StringIO(fixed_content), delimiter=delimiter)
        
        # Clean column names (remove special chars, lowercase, replace spaces with underscores)
        df.columns = [re.sub(r'[^\w\s]', '', col).lower().replace(' ', '_') for col in df.columns]
        
        # Remove duplicate columns if any
        df = df.loc[:, ~df.columns.duplicated()]
        
        # Handle missing values - be smarter about numeric vs text columns
        for col in df.columns:
            # Check if column is likely numeric by sampling non-empty values
            non_empty_values = df[col].dropna()
            if len(non_empty_values) > 0:
                # Try to detect if this should be a numeric column
                sample_values = non_empty_values.head(10).astype(str)
                numeric_count = 0
                for val in sample_values:
                    val_clean = str(val).strip().replace(',', '').replace('.', '', 1).replace('-', '', 1)
                    if val_clean.isdigit():
                        numeric_count += 1
                
                # If most values look numeric, fill with NaN instead of empty string
                if numeric_count >= len(sample_values) * 0.7:  # 70% numeric threshold
                    df[col] = df[col].replace('', np.nan)
                    # Keep NaN for numeric columns - they'll be handled as NULL in database
                else:
                    df[col] = df[col].fillna('')
            else:
                df[col] = df[col].fillna('')
        
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Remove completely empty columns
        df = df.dropna(axis=1, how='all')
        
        logger.info(f"Successfully normalized CSV data with {len(df)} rows and {len(df.columns)} columns")
        return df
    
    except Exception as e:
        logger.error(f"Error normalizing CSV data: {str(e)}")
        return None

def infer_postgresql_column_type(column_values: List[Any]) -> str:
    """
    Infer the PostgreSQL data type for a column based on its values.
    
    Args:
        column_values: List of values in the column
        
    Returns:
        PostgreSQL data type as a string
    """
    # Filter out empty values for type detection
    non_empty_values = [val for val in column_values if val != '' and val is not None]
    
    if not non_empty_values:
        return "TEXT"
    
    # If we have many empty values compared to non-empty, be conservative with INTEGER
    empty_ratio = (len(column_values) - len(non_empty_values)) / len(column_values)
    if empty_ratio > 0.3:  # If more than 30% are empty, avoid INTEGER unless very confident
        conservative_mode = True
    else:
        conservative_mode = False
    
    # Sample size for type detection (use a reasonable number of samples)
    sample_size = min(100, len(non_empty_values))
    detection_sample = non_empty_values[:sample_size]
    
    # Check if all values are numeric
    try:
        # Try to convert all values to float
        all_numeric = all(isinstance(val, (int, float)) or 
                          (isinstance(val, str) and val.replace('.', '', 1).replace('-', '', 1).isdigit()) 
                          for val in detection_sample)
        
        if all_numeric:
            # Check if all values are integers
            all_integers = all(float(val).is_integer() if isinstance(val, (int, float)) 
                              else float(val.replace(',', '')).is_integer() 
                              for val in detection_sample)
            
            if all_integers:
                # In conservative mode, use TEXT for integers to avoid empty string issues
                if conservative_mode:
                    return "TEXT"
                
                # Check if values fit in INTEGER range
                max_val = max(float(val) if isinstance(val, (int, float)) else float(val.replace(',', '')) 
                             for val in detection_sample)
                min_val = min(float(val) if isinstance(val, (int, float)) else float(val.replace(',', ''))
                             for val in detection_sample)
                
                if min_val >= -2147483648 and max_val <= 2147483647:
                    return "INTEGER"
                else:
                    return "BIGINT"
            else:
                # Check if values have fixed precision
                decimal_places = set()
                for val in detection_sample:
                    str_val = str(val).replace(',', '')
                    if '.' in str_val:
                        decimal_places.add(len(str_val.split('.')[1]))
                
                if len(decimal_places) == 1:
                    precision = max(decimal_places)
                    whole_places = max(len(str(int(float(val)))) if isinstance(val, (int, float)) 
                                      else len(str(int(float(val.replace(',', '')))))
                                      for val in detection_sample)
                    
                    return f"NUMERIC({whole_places + precision}, {precision})"
                else:
                    # In conservative mode with empty values, use TEXT for mixed precision
                    if conservative_mode:
                        return "TEXT"
                    return "NUMERIC"
    except (ValueError, TypeError):
        pass
    
    # Check if all values are dates or timestamps
    date_formats = [
        r'^\d{4}-\d{2}-\d{2}$',           # ISO format: 2023-01-01
        r'^\d{2}/\d{2}/\d{4}$',           # US format: 01/01/2023
        r'^\d{2}-\d{2}-\d{4}$',           # Common format: 01-01-2023
        r'^\d{1,2}\s+[A-Za-z]{3}\s+\d{4}$' # Jan 1 2023 or 1 Jan 2023
    ]
    
    timestamp_formats = [
        r'^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}',  # ISO format
        r'^\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}',         # US date with time
        r'^\d{2}-\d{2}-\d{4}\s+\d{2}:\d{2}'          # Common format with time
    ]
    
    is_date = False
    for pattern in date_formats:
        date_pattern = re.compile(pattern)
        if all(isinstance(val, str) and date_pattern.match(val) for val in detection_sample):
            is_date = True
            break
            
    if is_date:
        return "DATE"
    
    is_timestamp = False
    for pattern in timestamp_formats:
        timestamp_pattern = re.compile(pattern)
        if all(isinstance(val, str) and timestamp_pattern.match(val) for val in detection_sample):
            is_timestamp = True
            break
            
    if is_timestamp:
        return "TIMESTAMP"
    
    # Enhanced boolean detection
    bool_values = {'true', 'false', 'yes', 'no', 't', 'f', 'y', 'n', '1', '0', 'true', 'false'}
    if all(isinstance(val, bool) or 
          (isinstance(val, str) and val.lower() in bool_values) or
          (isinstance(val, (int, float)) and val in (0, 1))
          for val in detection_sample):
        return "BOOLEAN"
    
    # Check for JSON content
    def is_json(val):
        if not isinstance(val, str):
            return False
        val = val.strip()
        return (val.startswith('{') and val.endswith('}')) or (val.startswith('[') and val.endswith(']'))
    
    if all(is_json(val) for val in detection_sample):
        return "JSONB"
    
    # Enhanced string length detection for VARCHAR vs TEXT
    if all(isinstance(val, str) for val in detection_sample):
        all_values_length = [len(str(val)) for val in non_empty_values]
        max_length = max(all_values_length)
        avg_length = sum(all_values_length) / len(all_values_length)
        
        # If all strings are short and consistent in length
        if max_length <= 255 and (max_length / avg_length) < 2:
            return f"VARCHAR({max_length})"
        else:
            return "TEXT"
    
    # Default to TEXT for mixed or unknown types
    return "TEXT"

def generate_postgresql_schema(df: pd.DataFrame, table_name: str = "imported_data") -> PostgreSQLSchema:
    """
    Generate a PostgreSQL schema definition based on the DataFrame.
    
    Args:
        df: Pandas DataFrame containing the data
        table_name: Name for the PostgreSQL table
        
    Returns:
        PostgreSQLSchema object with table schema information
    """
    columns: List[PostgreSQLColumn] = []
    sample_data: List[ColumnSampleData] = []
    
    # Process each column to determine its PostgreSQL type
    for col_name in df.columns:
        column_values = df[col_name].tolist()
        pg_type = infer_postgresql_column_type(column_values)
        
        # Get sample data (first 5 non-empty values)
        non_empty_samples = [val for val in column_values if val != '' and val is not None][:5]
        
        columns.append(PostgreSQLColumn(
            name=col_name,
            postgresql_type=pg_type,
            nullable=True  # Assuming all columns are nullable by default
        ))
        
        sample_data.append(ColumnSampleData(
            column_name=col_name,
            samples=non_empty_samples
        ))
    
    # Generate CREATE TABLE SQL
    create_table_sql = generate_create_table_sql(table_name, columns)
    
    # Create the schema definition
    schema = PostgreSQLSchema(
        table_name=table_name,
        columns=columns,
        sample_data=sample_data,
        row_count=len(df),
        create_table_sql=create_table_sql
    )
    
    return schema

def generate_create_table_sql(table_name: str, columns: List[PostgreSQLColumn]) -> str:
    """
    Generate SQL for creating a PostgreSQL table.
    
    Args:
        table_name: Name of the table
        columns: List of column definitions
        
    Returns:
        SQL statement as a string
    """
    column_definitions = []
    
    for col in columns:
        nullable_str = "" if col.nullable else " NOT NULL"
        column_definitions.append(f"    {col.name} {col.postgresql_type}{nullable_str}")
    
    sql = f"CREATE TABLE {table_name} (\n"
    sql += ",\n".join(column_definitions)
    sql += "\n);"
    
    return sql

def process_csv_from_http(csv_content: str, delimiter: str = ',', table_name: str = "imported_data") -> CSVProcessingResult:
    """
    Process CSV data received over HTTP and prepare it for PostgreSQL.
    
    Args:
        csv_content: String content of the CSV file
        delimiter: CSV delimiter character
        table_name: Name for the PostgreSQL table
        
    Returns:
        CSVProcessingResult object with processing results and PostgreSQL schema
    """
    # Normalize the CSV data
    df = normalize_csv_data(csv_content, delimiter)
    
    if df is None:
        return CSVProcessingResult(
            success=False,
            error="Failed to process CSV data"
        )
    
    # Generate PostgreSQL schema
    schema = generate_postgresql_schema(df, table_name)
    
    return CSVProcessingResult(
        success=True,
        row_count=len(df),
        column_count=len(df.columns),
        schema_info=schema  # Using renamed field
    )


def process_csv_from_file(file_path: str, delimiter: str = ',', table_name: str = "imported_data") -> CSVProcessingResult:
    """
    Process CSV data from a file path and prepare it for PostgreSQL.
    
    Args:
        file_path: Path to the CSV file
        delimiter: CSV delimiter character
        table_name: Name for the PostgreSQL table
        
    Returns:
        CSVProcessingResult object with processing results and PostgreSQL schema
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            csv_content = file.read()
        
        return process_csv_from_http(csv_content, delimiter, table_name)
    except Exception as e:
        return CSVProcessingResult(
            success=False,
            error=f"Failed to process CSV file: {str(e)}"
        )

async def get_connector(config: Dict[str, Any] = None) -> 'PostgreSQLProcessor':
    """
    Get a PostgreSQL processor instance with the provided configuration
    
    Args:
        config: Database configuration including connection details
        
    Returns:
        PostgreSQLProcessor instance
    """
    from .processor import PostgreSQLProcessor
    
    # Create and initialize the processor
    processor =  PostgreSQLProcessor(config.get('db_uri') if config else None)
    return processor

async def process_csv_for_user(
    user_id: str,
    file_path: str = None,
    csv_content: str = None,
    table_name: str = None,
    original_file_name: str = None,
    delimiter: str = ',',
    db_config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Process a CSV file and store it in the user's database
    
    Args:
        user_id: ID of the user
        file_path: Path to CSV file (optional if csv_content is provided)
        csv_content: CSV content as string (optional if file_path is provided)
        table_name: Name for the table (optional)
        original_file_name: Original file name (optional)
        delimiter: CSV delimiter character
        db_config: Database configuration
        
    Returns:
        Dictionary with processing results
    """
    from .processor import get_connector
    
    # Initialize the processor
    processor = await get_connector(db_config)
    
    # Process CSV content or file
    if csv_content:
        return await processor.process_csv_and_store(
            user_id, 
            csv_content, 
            table_name, 
            original_file_name, 
            delimiter
        )
    elif file_path:
        with open(file_path, 'r', encoding='utf-8') as f:
            csv_content = f.read()
            
        if not original_file_name:
            import os
            original_file_name = os.path.basename(file_path)
            
        return await processor.process_csv_and_store(
            user_id, 
            csv_content, 
            table_name, 
            original_file_name, 
            delimiter
        )
    else:
        return {
            'success': False,
            'error': 'Either file_path or csv_content must be provided'
        }
