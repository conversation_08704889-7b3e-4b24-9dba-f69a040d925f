from typing import Dict, List, Any, Optional, Union
from src.rag_engine.analitic.db.postgresql.database import initialize_db
from src.rag_engine.analitic.pre_processor import normalize_csv_data, generate_postgresql_schema
from src.rag_engine.analitic.processor import PostgreSQLProcessor
from src.rag_engine.documents_indexer.indexer import index_knowledge_base, search_knowledge_base
import logging
from dotenv import load_dotenv
import os
import asyncio
import threading
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, AsyncEngine
from sqlalchemy.pool import QueuePool
from sqlalchemy.orm import sessionmaker
from contextlib import asynccontextmanager
from pathlib import Path

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class AsyncProcessorManager:
    """Asenkron işlemci yöneticisi - her thread için bağımsız bir processor örneği oluşturur"""

    _instance = None
    _thread_local = threading.local()
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AsyncProcessorManager, cls).__new__(cls)
        return cls._instance

    def get_processor(self):
        """Mevcut thread için bir PostgreSQLProcessor örneği döndürür"""
        # Thread-local storage kullanarak her thread için bağımsız processor örneği oluştur
        if not hasattr(self._thread_local, 'processor'):
            with self._lock:
                self._thread_local.processor = PostgreSQLProcessor()
        return self._thread_local.processor

class ConnectionPool:
    """PostgreSQL bağlantı havuzu yöneticisi"""

    _instance = None
    _engines = {}
    _initialized = False
    _lock = asyncio.Lock()
    _thread_local = threading.local()

    def __new__(cls):
        if cls._instance is None:
            with threading.Lock():
                if cls._instance is None:
                    cls._instance = super(ConnectionPool, cls).__new__(cls)
                    cls._instance._lock = asyncio.Lock()
                    cls._instance._initialized = False
                    cls._instance._engines = {}
                    cls._instance._thread_local = threading.local()
        return cls._instance

    async def initialize(self):
        """Bağlantı havuzunu başlat"""
        if not self._initialized:
            async with self._lock:
                if not self._initialized:
                    # Ana veritabanı URL'si
                    db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://admin:adminpassword@localhost:5432/userdb_template")

                    # PostgreSQL bağlantı limitleri
                    # asyncpg 110'dan fazla bağlantı açılınca performans sorunları yaşayabilir
                    # Her thread için yeni bir bağlantı açılacağından, limitleri makul tutmalıyız
                    self._engines["default"] = create_async_engine(
                        db_url,
                        pool_size=20,        # Havuzdaki sürekli açık bağlantı sayısı
                        max_overflow=30,     # Gerekirse ek açılabilecek bağlantı sayısı
                        pool_timeout=30,     # Bağlantı havuzundan bağlantı beklerken zaman aşımı süresi
                        pool_recycle=1800,   # 30 dakikada bir bağlantıları yenile
                        pool_pre_ping=True,  # Her kullanımdan önce bağlantının açık olduğunu kontrol et
                        echo=False,          # SQL sorgu loglarını kapatır
                        future=True,         # SQLAlchemy 2.0 ile uyumluluk için
                        connect_args={
                            "server_settings": {
                                "application_name": f"KaiFlow-RAG-{os.getpid()}"  # Server üzerinde tanımlama kolaylığı
                            },
                            "command_timeout": 60  # Sorgu zaman aşımı (saniye)
                        }
                    )

                    self._initialized = True
                    logger.info("Veritabanı bağlantı havuzu başlatıldı")

    async def get_session(self, ensure_same_thread=False):
        """Yeni bir veritabanı oturumu al

        Args:
            ensure_same_thread: True ise, oturum sadece açıldığı thread içinde kullanılabilir
        """
        if not self._initialized:
            await self.initialize()

        # Her thread için bağımsız session factory oluştur
        if ensure_same_thread and not hasattr(self._thread_local, 'session_factory'):
            self._thread_local.session_factory = sessionmaker(
                self._engines["default"],
                expire_on_commit=False,
                class_=AsyncSession
            )

        # Thread-safe session factory veya genel session factory kullan
        session_factory = getattr(self._thread_local, 'session_factory', None)
        if session_factory is None:
            session_factory = sessionmaker(
                self._engines["default"],
                expire_on_commit=False,
                class_=AsyncSession
            )

        # Oturum oluştur
        session = session_factory()
        return session

    @asynccontextmanager
    async def session_scope(self, ensure_same_thread=False):
        """Context manager olarak veritabanı oturumu sağla"""
        session = await self.get_session(ensure_same_thread)
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            raise e
        finally:
            await session.close()

class RAGInterface:
    """
    Interface for the RAG (Retrieval-Augmented Generation) engine.
    This class defines the contract for all RAG operations including data retrieval,
    storage, and aggregation functions.
    """

    def __init__(self):
        # Process yöneticisi
        self.processor_manager = AsyncProcessorManager()
        # Bağlantı havuzu
        self.pool = ConnectionPool()

    def get_processor(self):
        """Mevcut thread için processor örneği al"""
        return self.processor_manager.get_processor()

    # Veri işleme metotları
    def store_csv_data(self, user_id: str, csv_content: str, table_name: str = None, **kwargs) -> Dict[str, Any]:
        """
        Store csv data in the RAG storage.

        Args:
            csv_content: csv data to store
            user_id: user id
            **kwargs: additional arguments
                delimiter: delimiter of the csv file

        Returns:
            Dictionary containing the result of the operation
        """
        try:
            # Mevcut thread için ayrı processor kullan
            processor = self.get_processor()
            result = processor.process_csv_and_store(user_id, csv_content, table_name, **kwargs)

            # Ensure we return proper format
            if isinstance(result, dict) and "success" in result:
                return result
            else:
                # Legacy format conversion
                return {
                    "success": True,
                    "result": result
                }

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"CSV veri saklama hatası: {str(e)}")
            logger.error(f"Full traceback: {error_details}")
            return {
                "success": False,
                "error": f"Internal server error processing CSV. Details: {str(e)}"
            }

    def get_user_tables(self, user_id: str, chat_id: str = None) -> Dict[str, Any]:
        """
        Get tables for a user, optionally filtered by chat.

        Args:
            user_id: user id
            chat_id: optional chat id to filter tables

        Returns:
            Dictionary containing the result of the operation
        """
        try:
            # Mevcut thread için ayrı processor kullan
            processor = self.get_processor()
            return  processor.get_user_tables(user_id, chat_id)
        except Exception as e:
            logger.error(f"Kullanıcı tabloları getirme hatası: {str(e)}")
            return {"status": "error", "message": f"Kullanıcı tabloları getirilirken hata oluştu: {str(e)}"}

    def agregate_csv_data(self, user_id: str, query_sql: str) -> Dict[str, Any]:
        """
        Aggregate csv data for analytics purposes.

        Args:
            user_id: user id
            query_sql: sql query to aggregate the data

        Returns:
            Dictionary containing the result of the operation
        """
        try:
            # Mevcut thread için ayrı processor kullan
            processor = self.get_processor()
            return  processor.execute_user_query(user_id, query_sql)
        except Exception as e:
            logger.error(f"SQL sorgu çalıştırma hatası: {str(e)}")
            return {"status": "error", "message": f"SQL sorgusu çalıştırılırken hata oluştu: {str(e)}"}

    ########################################################################
    # Document Operations
    ########################################################################
    def index_knowledge_base(self, pdf_path: Path, user_id: Optional[str] = None, force_reindex: bool = False) -> int:
        """
        Index a document in the knowledge base.

        Args:
            pdf_path: Path to the document file to index
            user_id: Optional user ID for user-specific indexing. If None, indexes to general knowledge base.
            force_reindex: Force reindexing even if file already exists

        Returns:
            Number of indexed chunks
        """
        # Ensure pdf_path is a Path object
        if isinstance(pdf_path, str):
            pdf_path = Path(pdf_path)
        return index_knowledge_base(pdf_path, user_id=user_id, force_reindex=force_reindex)

    def bulk_index_knowledge_base(self, pdf_paths: List[Union[str, Path]], user_id: Optional[str] = None, force_reindex: bool = False) -> Dict[str, Any]:
        """
        Index multiple documents in the knowledge base in bulk.

        Args:
            pdf_paths: List of paths to document files to index
            user_id: Optional user ID for user-specific indexing. If None, indexes to general knowledge base.
            force_reindex: Force reindexing even if files already exist

        Returns:
            Dictionary containing bulk indexing results with success/failure details
        """
        results = {
            "total_files": len(pdf_paths),
            "successful": [],
            "failed": [],
            "total_chunks": 0,
            "status": "success"
        }

        logger.info(f"Starting bulk indexing of {len(pdf_paths)} documents for user: {user_id or 'general'}")

        for pdf_path in pdf_paths:
            try:
                # Ensure pdf_path is a Path object
                if isinstance(pdf_path, str):
                    pdf_path = Path(pdf_path)

                # Check if file exists
                if not pdf_path.exists():
                    results["failed"].append({
                        "file": str(pdf_path),
                        "error": "File not found"
                    })
                    continue

                # Check if file is a supported document type
                if pdf_path.suffix.lower() not in ['.pdf', '.docx', '.doc']:
                    results["failed"].append({
                        "file": str(pdf_path),
                        "error": "Unsupported file format. Only PDF, DOCX, and DOC files are supported."
                    })
                    continue

                # Index the document
                chunks_count = self.index_knowledge_base(pdf_path, user_id=user_id, force_reindex=force_reindex)

                results["successful"].append({
                    "file": str(pdf_path),
                    "chunks": chunks_count
                })
                results["total_chunks"] += chunks_count

                logger.info(f"Successfully indexed {pdf_path.name}: {chunks_count} chunks")

            except Exception as e:
                error_msg = str(e)
                results["failed"].append({
                    "file": str(pdf_path),
                    "error": error_msg
                })
                logger.error(f"Failed to index {pdf_path}: {error_msg}")

        # Update overall status
        if results["failed"] and not results["successful"]:
            results["status"] = "failed"
        elif results["failed"]:
            results["status"] = "partial_success"

        logger.info(f"Bulk indexing completed: {len(results['successful'])} successful, {len(results['failed'])} failed")

        return results

    def bulk_index_directory(self, directory_path: Union[str, Path], user_id: Optional[str] = None,
                           recursive: bool = True) -> Dict[str, Any]:
        """
        Index all PDF files in a directory.

        Args:
            directory_path: Path to directory containing PDF files
            user_id: Optional user ID for user-specific indexing. If None, indexes to general knowledge base.
            recursive: Whether to search subdirectories recursively

        Returns:
            Dictionary containing bulk indexing results
        """
        # Ensure directory_path is a Path object
        if isinstance(directory_path, str):
            directory_path = Path(directory_path)

        if not directory_path.exists():
            return {
                "total_files": 0,
                "successful": [],
                "failed": [{"directory": str(directory_path), "error": "Directory not found"}],
                "total_chunks": 0,
                "status": "failed"
            }

        if not directory_path.is_dir():
            return {
                "total_files": 0,
                "successful": [],
                "failed": [{"directory": str(directory_path), "error": "Path is not a directory"}],
                "total_chunks": 0,
                "status": "failed"
            }

        # Find all supported document files (PDF, DOCX, DOC)
        supported_extensions = ["*.pdf", "*.docx", "*.doc"]
        document_files = []

        for extension in supported_extensions:
            if recursive:
                document_files.extend(list(directory_path.rglob(extension)))
            else:
                document_files.extend(list(directory_path.glob(extension)))

        logger.info(f"Found {len(document_files)} document files in {directory_path}")

        if not document_files:
            return {
                "total_files": 0,
                "successful": [],
                "failed": [],
                "total_chunks": 0,
                "status": "success",
                "message": "No supported document files (PDF, DOCX, DOC) found in directory"
            }

        # Use bulk_index_knowledge_base to process all files
        return self.bulk_index_knowledge_base(document_files, user_id=user_id)

    def search_knowledge_base(self, query: str, user_id: Optional[str] = None, top_k: int = 6) -> Dict[str, Any]:
        """
        Search documents in the knowledge base.
        
        Args:
            query: The search query
            user_id: Optional user ID to search in user-specific knowledge. If None, searches in general knowledge base.
            top_k: Number of results to return
            
        Returns:
            Dictionary containing search results and status
        """
        try:
            logger.info(f"Doküman araması yapılıyor: {query}")

            try:
                search_results = search_knowledge_base(
                    query=query,
                    user_id=user_id,
                    top_k=top_k
                )
            except Exception as search_error:
                logger.error(f"Arama hatası: {str(search_error)}")
                search_results = []

            return {
                "status": "success",
                "message": "Doküman araması başarıyla tamamlandı",
                "results": search_results,
                "query": query
            }
        except Exception as e:
            logger.error(f"Doküman arama hatası: {str(e)}")
            return {"status": "error", "message": f"Doküman araması sırasında hata oluştu: {str(e)}"}

    def rfm_analysis(self, user_id: str, sql_query: str) -> Dict[str, Any]:
        """
        Perform RFM (Recency, Frequency, Monetary) analysis.

        Args:
            user_id: The ID of the user.
            sql_query: The SQL query to execute.

        Returns:
            A dictionary containing the RFM analysis results.
        """
        try:
            # Önce SQL sorgusunu çalıştır
            query_result = self.agregate_csv_data(user_id, sql_query)

            # Sorgu başarısız olduysa, hatayı döndür
            if not query_result.get("success", False):
                return query_result

            # Başarılı sorgu sonucunu RFM analizi formatında döndür
            return {
                "status": "success",
                "message": "RFM analizi başarıyla tamamlandı",
                "data": query_result.get("data", {}),
                "rfm_segments": {
                    "champions": "Yüksek değerli ve sadık müşteriler",
                    "loyal_customers": "Düzenli alışveriş yapan müşteriler",
                    "potential_loyalists": "Yakın zamanda alışveriş yapmış, potansiyel sadık müşteriler",
                    "new_customers": "Yeni müşteriler",
                    "promising": "Yakın zamanda alışveriş yapmış, potansiyel müşteriler",
                    "customers_needing_attention": "Dikkat edilmesi gereken müşteriler",
                    "about_to_sleep": "Alışveriş sıklığı azalan müşteriler",
                    "at_risk": "Kaybedilme riski olan müşteriler",
                    "cant_lose_them": "Değerli ancak kaybedilme riski olan müşteriler",
                    "hibernating": "Uzun süredir alışveriş yapmayan müşteriler",
                    "lost": "Kaybedilmiş müşteriler"
                }
            }
        except Exception as e:
            logger.error(f"RFM analizi hatası: {str(e)}")
            return {"status": "error", "message": f"RFM analizi sırasında hata oluştu: {str(e)}"}

    def cltv_analysis(self, user_id: str, sql_query: str) -> Dict[str, Any]:
        """
        Perform CLTV (Customer Lifetime Value) analysis.

        Args:
            user_id: The ID of the user.
            sql_query: The SQL query to execute.

        Returns:
            A dictionary containing the CLTV analysis results.
        """
        try:
            # Önce SQL sorgusunu çalıştır
            query_result = self.agregate_csv_data(user_id, sql_query)

            # Sorgu başarısız olduysa, hatayı döndür
            if not query_result.get("success", False):
                return query_result

            # Başarılı sorgu sonucunu CLTV analizi formatında döndür
            return {
                "status": "success",
                "message": "CLTV analizi başarıyla tamamlandı",
                "data": query_result.get("data", {}),
                "cltv_segments": {
                    "high_value": "Yüksek değerli müşteriler",
                    "medium_value": "Orta değerli müşteriler",
                    "low_value": "Düşük değerli müşteriler"
                }
            }
        except Exception as e:
            logger.error(f"CLTV analizi hatası: {str(e)}")
            return {"status": "error", "message": f"CLTV analizi sırasında hata oluştu: {str(e)}"}

    def churn_analysis(self, user_id: str, sql_query: str) -> Dict[str, Any]:
        """
        Perform churn analysis.

        Args:
            user_id: The ID of the user.
            sql_query: The SQL query to execute.

        Returns:
            A dictionary containing the churn analysis results.
        """
        try:
            # Önce SQL sorgusunu çalıştır
            query_result = self.agregate_csv_data(user_id, sql_query)

            # Sorgu başarısız olduysa, hatayı döndür
            if not query_result.get("success", False):
                return query_result

            # Başarılı sorgu sonucunu churn analizi formatında döndür
            return {
                "status": "success",
                "message": "Churn analizi başarıyla tamamlandı",
                "data": query_result.get("data", {}),
                "churn_risk": {
                    "high_risk": "Yüksek ayrılma riski olan müşteriler",
                    "medium_risk": "Orta ayrılma riski olan müşteriler",
                    "low_risk": "Düşük ayrılma riski olan müşteriler",
                    "loyal": "Sadık müşteriler"
                }
            }
        except Exception as e:
            logger.error(f"Churn analizi hatası: {str(e)}")
            return {"status": "error", "message": f"Churn analizi sırasında hata oluştu: {str(e)}"}