from agno.tools import Toolkit, tool
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
import json
import time
import hashlib
from datetime import datetime, timedelta

class AnomalyData(BaseModel):
    """Anomaly data structure"""
    row_id: int
    column: str
    value: Any
    method: str
    score: float
    reason: str

class AnomalyStorageInput(BaseModel):
    """Input schema for anomaly storage."""
    user_id: str = Field(..., description="Kullanıcı ID'si")
    chat_id: str = Field(..., description="Chat ID'si")
    table_name: str = Field(..., description="Tablo adı")
    anomalies: List[Dict[str, Any]] = Field(..., description="Anomali listesi")

class AnomalyStorageToolkit(Toolkit):
    def __init__(self):
        super().__init__(
            name="AnomalyStorageToolkit",
            instructions=None,
            add_instructions=False,
        )
        self.register(self.store_anomalies, name="store_anomalies")
        self.register(self.get_anomalies, name="get_anomalies")
        self.register(self.clear_anomalies, name="clear_anomalies")
        self.register(self.check_anomaly_status, name="check_anomaly_status")
        self.register(self.create_backup, name="create_backup")
        self.register(self.remove_anomalies, name="remove_anomalies")
        self.register(self.debug_redis_keys, name="debug_redis_keys")

    def _get_redis_client(self):
        """Get Redis client"""
        try:
            from src.api.routes.chats.redis_connector import get_redis_client
            return get_redis_client()
        except Exception as e:
            print(f"Redis connection error: {e}")
            return None

    def _get_anomaly_key(self, user_id: str, chat_id: str) -> str:
        """Generate Redis key for anomaly data"""
        return f"anomaly_data:{user_id}:{chat_id}"

    def _create_table_hash(self, table_name: str, user_id: str) -> str:
        """Create hash for table to detect changes"""
        hash_input = f"{table_name}_{user_id}_{int(time.time() // 3600)}"  # Hour-based hash
        return hashlib.md5(hash_input.encode()).hexdigest()[:8]

    def store_anomalies(self, user_id: str, chat_id: str, table_name: str, 
                       anomalies: List[Dict[str, Any]], total_rows: int = 0) -> Dict[str, Any]:
        """
        Store anomaly detection results in Redis
        
        Args:
            user_id: Kullanıcı ID'si
            chat_id: Chat ID'si  
            table_name: Tablo adı
            anomalies: Anomali listesi
            total_rows: Toplam satır sayısı
            
        Returns:
            Storage result
        """
        try:
            redis_client = self._get_redis_client()
            if not redis_client:
                return {"success": False, "message": "Redis bağlantısı kurulamadı"}

            # Anomaly data structure
            anomaly_data = {
                "user_id": user_id,
                "chat_id": chat_id,
                "table_name": table_name,
                "table_hash": self._create_table_hash(table_name, user_id),
                "anomalies": anomalies,
                "total_anomalies": len(anomalies),
                "total_rows": total_rows,
                "anomaly_percentage": (len(anomalies) / total_rows * 100) if total_rows > 0 else 0,
                "detection_date": datetime.now().isoformat(),
                "status": "pending",
                "expires_at": (datetime.now() + timedelta(days=7)).isoformat()
            }

            # Store in Redis with 7 days TTL
            redis_key = self._get_anomaly_key(user_id, chat_id)
            redis_client.setex(
                redis_key, 
                7 * 24 * 60 * 60,  # 7 days in seconds
                json.dumps(anomaly_data, default=str)
            )

            print(f"✅ Stored {len(anomalies)} anomalies for user {user_id}, chat {chat_id}")
            
            return {
                "success": True,
                "message": f"{len(anomalies)} adet anomali Redis'e kaydedildi",
                "total_anomalies": len(anomalies),
                "anomaly_percentage": round(anomaly_data["anomaly_percentage"], 2),
                "redis_key": redis_key
            }

        except Exception as e:
            print(f"❌ Error storing anomalies: {e}")
            return {"success": False, "message": f"Anomali kaydetme hatası: {str(e)}"}

    def get_anomalies(self, user_id: str, chat_id: str) -> Dict[str, Any]:
        """
        Retrieve anomaly data from Redis
        
        Args:
            user_id: Kullanıcı ID'si
            chat_id: Chat ID'si
            
        Returns:
            Anomaly data or error
        """
        try:
            redis_client = self._get_redis_client()
            if not redis_client:
                return {"success": False, "message": "Redis bağlantısı kurulamadı"}

            redis_key = self._get_anomaly_key(user_id, chat_id)
            anomaly_data_json = redis_client.get(redis_key)

            if not anomaly_data_json:
                return {
                    "success": False, 
                    "message": "Anomali verisi bulunamadı veya süresi dolmuş",
                    "expired": True
                }

            anomaly_data = json.loads(anomaly_data_json)
            
            # Check if data is expired
            expires_at = datetime.fromisoformat(anomaly_data["expires_at"])
            if datetime.now() > expires_at:
                # Clean up expired data
                redis_client.delete(redis_key)
                return {
                    "success": False,
                    "message": "Anomali verisi süresi dolmuş",
                    "expired": True
                }

            return {
                "success": True,
                "data": anomaly_data,
                "message": f"{anomaly_data['total_anomalies']} adet anomali bulundu"
            }

        except Exception as e:
            print(f"❌ Error retrieving anomalies: {e}")
            return {"success": False, "message": f"Anomali alma hatası: {str(e)}"}

    def clear_anomalies(self, user_id: str, chat_id: str) -> Dict[str, Any]:
        """
        Clear anomaly data from Redis
        
        Args:
            user_id: Kullanıcı ID'si
            chat_id: Chat ID'si
            
        Returns:
            Clear result
        """
        try:
            redis_client = self._get_redis_client()
            if not redis_client:
                return {"success": False, "message": "Redis bağlantısı kurulamadı"}

            redis_key = self._get_anomaly_key(user_id, chat_id)
            deleted = redis_client.delete(redis_key)

            if deleted:
                return {"success": True, "message": "Anomali verisi temizlendi"}
            else:
                return {"success": False, "message": "Silinecek anomali verisi bulunamadı"}

        except Exception as e:
            print(f"❌ Error clearing anomalies: {e}")
            return {"success": False, "message": f"Anomali temizleme hatası: {str(e)}"}

    def check_anomaly_status(self, user_id: str, chat_id: str) -> Dict[str, Any]:
        """
        Check if there are pending anomalies for user
        
        Args:
            user_id: Kullanıcı ID'si
            chat_id: Chat ID'si
            
        Returns:
            Status information
        """
        try:
            result = self.get_anomalies(user_id, chat_id)
            
            if result["success"]:
                data = result["data"]
                return {
                    "success": True,
                    "has_anomalies": True,
                    "total_anomalies": data["total_anomalies"],
                    "anomaly_percentage": data["anomaly_percentage"],
                    "status": data["status"],
                    "detection_date": data["detection_date"]
                }
            else:
                return {
                    "success": True,
                    "has_anomalies": False,
                    "message": result["message"]
                }

        except Exception as e:
            print(f"❌ Error checking anomaly status: {e}")
            return {"success": False, "message": f"Durum kontrol hatası: {str(e)}"}

    def update_anomaly_status(self, user_id: str, chat_id: str, status: str) -> Dict[str, Any]:
        """
        Update anomaly processing status
        
        Args:
            user_id: Kullanıcı ID'si
            chat_id: Chat ID'si
            status: New status (pending, processing, completed, failed)
            
        Returns:
            Update result
        """
        try:
            result = self.get_anomalies(user_id, chat_id)
            if not result["success"]:
                return result

            # Update status
            anomaly_data = result["data"]
            anomaly_data["status"] = status
            anomaly_data["last_updated"] = datetime.now().isoformat()

            # Store back to Redis
            redis_client = self._get_redis_client()
            if not redis_client:
                return {"success": False, "message": "Redis bağlantısı kurulamadı"}

            redis_key = self._get_anomaly_key(user_id, chat_id)
            redis_client.setex(
                redis_key,
                7 * 24 * 60 * 60,  # 7 days
                json.dumps(anomaly_data, default=str)
            )

            return {"success": True, "message": f"Durum '{status}' olarak güncellendi"}

        except Exception as e:
            print(f"❌ Error updating anomaly status: {e}")
            return {"success": False, "message": f"Durum güncelleme hatası: {str(e)}"}

    def get_anomaly_summary(self, user_id: str, chat_id: str) -> Dict[str, Any]:
        """
        Get summary of anomalies for user display
        
        Args:
            user_id: Kullanıcı ID'si
            chat_id: Chat ID'si
            
        Returns:
            Anomaly summary
        """
        try:
            result = self.get_anomalies(user_id, chat_id)
            if not result["success"]:
                return result

            data = result["data"]
            anomalies = data["anomalies"]

            # Group anomalies by type
            anomaly_types = {}
            for anomaly in anomalies:
                method = anomaly.get("method", "Unknown")
                if method not in anomaly_types:
                    anomaly_types[method] = 0
                anomaly_types[method] += 1

            # Group by column
            column_anomalies = {}
            for anomaly in anomalies:
                column = anomaly.get("column", "Unknown")
                if column not in column_anomalies:
                    column_anomalies[column] = 0
                column_anomalies[column] += 1

            return {
                "success": True,
                "total_anomalies": data["total_anomalies"],
                "anomaly_percentage": data["anomaly_percentage"],
                "anomaly_types": anomaly_types,
                "column_anomalies": column_anomalies,
                "sample_anomalies": anomalies[:5],  # First 5 for preview
                "detection_date": data["detection_date"]
            }

        except Exception as e:
            print(f"❌ Error getting anomaly summary: {e}")
            return {"success": False, "message": f"Özet alma hatası: {str(e)}"}

    def create_backup(self, user_id: str, table_name: str) -> Dict[str, Any]:
        """
        Create backup table before anomaly removal

        Args:
            user_id: Kullanıcı ID'si
            table_name: Kaynak tablo adı

        Returns:
            Backup creation result
        """
        try:
            from src.rag_engine.interface import RAGInterface

            rag_interface = RAGInterface()

            # Generate backup table name with timestamp
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_table_name = f"{table_name}_backup_{timestamp}"

            # Create backup table
            backup_query = f"CREATE TABLE {backup_table_name} AS SELECT * FROM {table_name}"

            # Execute backup creation
            processor = rag_interface.get_processor()
            result = processor.execute_user_query(user_id, backup_query)

            if result.get("success"):
                return {
                    "success": True,
                    "backup_table_name": backup_table_name,
                    "message": f"Backup table '{backup_table_name}' created successfully"
                }
            else:
                return {
                    "success": False,
                    "message": f"Backup creation failed: {result.get('data', {}).get('error', 'Unknown error')}"
                }

        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return {"success": False, "message": f"Backup creation error: {str(e)}"}

    def remove_anomalies(self, user_id: str, table_name: str, anomaly_row_ids: List[int]) -> Dict[str, Any]:
        """
        Remove anomaly rows from table

        Args:
            user_id: Kullanıcı ID'si
            table_name: Tablo adı
            anomaly_row_ids: Silinecek satır ID'leri

        Returns:
            Removal result
        """
        try:
            from src.rag_engine.interface import RAGInterface

            if not anomaly_row_ids:
                return {"success": False, "message": "No anomaly row IDs provided"}

            rag_interface = RAGInterface()

            # Create DELETE query
            row_ids_str = ",".join(map(str, anomaly_row_ids))
            delete_query = f"DELETE FROM {table_name} WHERE id IN ({row_ids_str})"

            # Execute deletion
            processor = rag_interface.get_processor()
            result = processor.execute_user_query(user_id, delete_query)

            if result.get("success"):
                return {
                    "success": True,
                    "deleted_count": len(anomaly_row_ids),
                    "message": f"Successfully deleted {len(anomaly_row_ids)} anomaly rows"
                }
            else:
                return {
                    "success": False,
                    "message": f"Anomaly removal failed: {result.get('data', {}).get('error', 'Unknown error')}"
                }

        except Exception as e:
            print(f"❌ Error removing anomalies: {e}")
            return {"success": False, "message": f"Anomaly removal error: {str(e)}"}

    def debug_redis_keys(self, user_id: str) -> Dict[str, Any]:
        """
        Debug function to check Redis keys for user

        Args:
            user_id: Kullanıcı ID'si

        Returns:
            Redis keys information
        """
        try:
            redis_client = self._get_redis_client()
            if not redis_client:
                return {"success": False, "message": "Redis bağlantısı kurulamadı"}

            # Get all anomaly keys for this user
            pattern = f"anomaly_data:{user_id}:*"
            keys = redis_client.keys(pattern)

            key_info = []
            for key in keys:
                try:
                    data = redis_client.get(key)
                    if data:
                        anomaly_data = json.loads(data)
                        key_info.append({
                            "key": key,
                            "chat_id": anomaly_data.get("chat_id"),
                            "total_anomalies": anomaly_data.get("total_anomalies"),
                            "detection_date": anomaly_data.get("detection_date"),
                            "status": anomaly_data.get("status")
                        })
                except Exception as e:
                    key_info.append({
                        "key": key,
                        "error": f"Failed to parse: {str(e)}"
                    })

            return {
                "success": True,
                "user_id": user_id,
                "total_keys": len(keys),
                "keys": key_info,
                "message": f"Found {len(keys)} anomaly keys for user {user_id}"
            }

        except Exception as e:
            print(f"❌ Error debugging Redis keys: {e}")
            return {"success": False, "message": f"Debug error: {str(e)}"}
