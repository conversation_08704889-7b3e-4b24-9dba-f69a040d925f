#!/usr/bin/env python3
"""
Test script for Ollama-based indexer with Turkish documents
"""

import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from rag_engine.documents_indexer.indexer import index_knowledge_base, search_knowledge_base, test_ollama_connection

def main():
    print("🚀 Testing Ollama-based indexer with Turkish documents")
    print("=" * 60)
    
    # Test Ollama connection first
    print("\n1. Testing Ollama connection...")
    if not test_ollama_connection():
        print("❌ Ollama connection failed. Please check your server.")
        return
    
    # Test indexing a Turkish PDF
    print("\n2. Testing PDF indexing...")
    pdf_path = Path("data/BP_markalar_nasil_buyur.pdf")
    
    if not pdf_path.exists():
        print(f"❌ Test PDF not found: {pdf_path}")
        print("Available files in data directory:")
        data_dir = Path("data")
        if data_dir.exists():
            for file in data_dir.glob("*.pdf"):
                print(f"  - {file.name}")
        return
    
    try:
        # Index the document
        print(f"📄 Indexing: {pdf_path.name}")
        chunk_count = index_knowledge_base(pdf_path, force_reindex=True)
        print(f"✅ Successfully indexed {chunk_count} chunks")
        
        # Test search with Turkish queries
        print("\n3. Testing search with Turkish queries...")
        
        test_queries = [
            "marka nasıl büyür",
            "pazarlama stratejisi",
            "müşteri sadakati",
            "rekabet avantajı",
            "Türkçe karakterler: ğüşıöç",
            "çifte risk kanunu"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Searching: '{query}'")
            results = search_knowledge_base(query, top_k=3)
            
            if results:
                for i, result in enumerate(results[:2], 1):
                    score = result.get('score', 0)
                    content = result.get('content', '')[:100]
                    print(f"  {i}. Score: {score:.3f} | {content}...")
            else:
                print("  No results found")
        
        print("\n🎉 All tests completed successfully!")
        print("✅ Ollama embedder is working correctly with Turkish characters")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
