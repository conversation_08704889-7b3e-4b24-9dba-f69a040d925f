"""
Request and response schemas for KAI API
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any


class AnalyzeRequest(BaseModel):
    """Request schema for /analyze endpoint"""
    user_id: str
    input_text: str


class TaskStatusRequest(BaseModel):
    """Request schema for /task-status endpoint"""
    task_key: str


# Component Schemas (used within data models)
class AgentTask(BaseModel):
    """Detailed agent task information"""
    agent_id: str
    agent_name: str
    task: str
    reason: str


class Plan(BaseModel):
    """Plan structure for analyze response"""
    reason: str
    task_list: List[str]  # Keep for backward compatibility
    agents: Optional[List[AgentTask]] = None  # New detailed agent information


class ChatMessage(BaseModel):
    """Chat message schema for frontend"""
    id: str
    sender: str  # 'user' or 'agent'
    text: str
    timestamp: float
    sources: Optional[List[Dict[str, Any]]] = None
    error: Optional[bool] = None # Error specific to this message content if agent message
    plan: Optional[Plan] = None
    task_list: Optional[List[str]] = None
    isTaskOutput: Optional[bool] = None


# Data Schemas for ApiResponse.data field
class AnalyzeData(BaseModel): # Renamed from AnalyzeResponse
    """Data schema for a successful /analyze endpoint response"""
    task_key: str
    plan: Optional[Plan] = None
    # Removed success, message, error


class ChatHistoryData(BaseModel): # Renamed from ChatHistoryResponse
    """Data schema for a successful /chats/{chat_id}/messages endpoint response"""
    messages: List[ChatMessage]
    total_count: int
    # Removed success


class TaskStatusData(BaseModel): # Renamed from TaskStatusResponse
    """Data schema for a successful /task-status endpoint response"""
    id: str
    task_key: str
    status: str
    current_task: Optional[str] = None
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None # This is an error within the task's execution, not an API error


class CreateChatRequest(BaseModel):
    """Request schema for /chats endpoint"""
    user_id: str
    chat_name: Optional[str] = None


class CreateChatResponse(BaseModel): # Unchanged, already suitable as data
    """Data schema for a successful /chats endpoint response"""
    chat_id: str
    user_id: str
    chat_name: str


class UploadResponseData(BaseModel):
    """Data schema for a successful file upload response"""
    message: str
    filename: str
    chat_id: str
    task_key: Optional[str] = None  # Task key for tracking analysis progress


class ChatAssetData(BaseModel):
    """Schema for chat assets response"""
    chat_id: str
    assets: Dict[str, List[Dict[str, Any]]] = Field(
        default_factory=lambda: {"charts": [], "reports": [], "other": []},
        description="Dictionary of asset types and their data. Currently supports 'charts', 'reports', and 'other'"
    )
    metadata: Optional[Dict[str, Any]] = None 