from agno.tools import Toolkit, tool
from typing import Dict, Any, Optional

from src.rag_engine.interface import RAGInterface

class DatabaseToolkit(Toolkit):
    def __init__(self):
        super().__init__(
            name="DatabaseToolkit",
            instructions=None,
            add_instructions=False,
        )

        self.rag_interface = RAGInterface()
        # user_id and chat_id are no longer stored here

        self.register(self.get_user_tables, name="get_user_tables")
        self.register(self.get_latest_chat_table, name="get_latest_chat_table")
        self.register(self.agregate_data, name="agregate_data")
        self.register(self.agregate_limited_data, name="agregate_limited_data")
        # self.register(self.store_csv_data) # Uncomment and implement if needed

    # No set_context method

    def __name__(self):
        return "DatabaseToolkit"


    def get_user_tables(self, user_id: Optional[str] = None, chat_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Kullanıcının veritabanında depolanan mevcut veri tablolarını alır.
        Bu tablolar CSV, Excel vb. çeşitli kaynaklardan yüklenmiş olabilir.

        Args:
            user_id: Kullanıcının ID'si (kanca tarafından enjekte edilir).
            chat_id: Opsiyonel chat ID'si - belirtilirse sadece o chat'e ait tablolar döner.

        Returns:
            Kullanıcı için mevcut tabloları içeren bir sözlük.
        """
        print(f"🔍 DatabaseToolkit: get_user_tables called with user_id={user_id}, chat_id={chat_id}")
        print(f"🔍 DatabaseToolkit: Function arguments received: user_id={repr(user_id)}, chat_id={repr(chat_id)}")

        # SECURITY FIX: Get user_id and chat_id from context if needed
        from src.agents.agno_agents.user_context_manager import UserContextManager
        context_manager = UserContextManager()
        context_user_id = context_manager.get_current_user_id()
        context_chat_id = context_manager.get_current_chat_id()

        # Inject user_id from context if needed
        if context_user_id:
            if not user_id or user_id in ['user', 'placeholder', 'user_id_placeholder', '']:
                print(f"🔧 DatabaseToolkit: Injecting user_id from context: {context_user_id}")
                user_id = context_user_id
            elif user_id != context_user_id:
                print(f"⚠️ DatabaseToolkit: Security override - using context user_id: {context_user_id} instead of: {user_id}")
                user_id = context_user_id

        # Inject chat_id from context if needed
        if context_chat_id:
            if not chat_id or chat_id in ['chat', 'placeholder', 'chat_id_placeholder', '']:
                print(f"🔧 DatabaseToolkit: Injecting chat_id from context: {context_chat_id}")
                chat_id = context_chat_id
            elif chat_id != context_chat_id:
                print(f"⚠️ DatabaseToolkit: Security override - using context chat_id: {context_chat_id} instead of: {chat_id}")
                chat_id = context_chat_id

        if not user_id:
            raise ValueError("get_user_tables için Kullanıcı ID'si sağlanmadı veya enjekte edilmedi.")

        if not chat_id:
            print(f"⚠️ DatabaseToolkit: No chat_id provided for get_user_tables - this may return tables from all chats")

        if chat_id:
            print(f"DatabaseToolkit: get_user_tables aracı user_id={user_id}, chat_id={chat_id} ile çağrıldı")
        else:
            print(f"DatabaseToolkit: get_user_tables aracı user_id={user_id} ile çağrıldı (tüm tablolar)")

        return self.rag_interface.get_user_tables(user_id, chat_id)

    def get_latest_chat_table(self, chat_id: Optional[str] = None, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Belirli bir chat için en son yüklenen CSV tablosunu alır.
        Yeni format desteği: chat_id'ye bakmadan en son yüklenen tabloyu döndürür.

        Args:
            chat_id: Chat ID'si (eski format uyumluluğu için)
            user_id: Kullanıcının ID'si (kanca tarafından enjekte edilir).

        Returns:
            En son yüklenen tablo bilgilerini içeren sözlük veya hata mesajı.
        """
        print(f"🔍 DatabaseToolkit: get_latest_chat_table called with chat_id={chat_id}, user_id={user_id}")

        # SECURITY FIX: Get user_id and chat_id from context if needed
        from src.agents.agno_agents.user_context_manager import UserContextManager
        context_manager = UserContextManager()
        context_user_id = context_manager.get_current_user_id()
        context_chat_id = context_manager.get_current_chat_id()

        # Inject user_id from context if needed
        if context_user_id:
            if not user_id or user_id in ['user', 'placeholder', 'user_id_placeholder', '']:
                print(f"🔧 DatabaseToolkit: get_latest_chat_table - Injecting user_id from context: {context_user_id}")
                user_id = context_user_id
            elif user_id != context_user_id:
                print(f"⚠️ DatabaseToolkit: get_latest_chat_table - Security override - using context user_id: {context_user_id} instead of: {user_id}")
                user_id = context_user_id

        # Inject chat_id from context if needed
        if context_chat_id:
            if not chat_id or chat_id in ['chat', 'placeholder', 'chat_id_placeholder', '']:
                print(f"🔧 DatabaseToolkit: Injecting chat_id from context: {context_chat_id}")
                chat_id = context_chat_id
            elif chat_id != context_chat_id:
                print(f"⚠️ DatabaseToolkit: Security override - using context chat_id: {context_chat_id} instead of: {chat_id}")
                chat_id = context_chat_id

        if not user_id:
            raise ValueError("get_latest_chat_table için Kullanıcı ID'si sağlanmadı veya enjekte edilmedi.")

        if not chat_id:
            # This should not happen if context injection works properly
            print(f"❌ DatabaseToolkit: chat_id is still None after context injection!")
            print(f"❌ DatabaseToolkit: Available context: user_id={context_user_id}, chat_id={context_chat_id}")
            raise ValueError("get_latest_chat_table için Chat ID'si sağlanmadı veya enjekte edilmedi.")

        try:
            # Get tables for this specific chat (or all tables if no chat_id)
            if chat_id:
                print(f"Getting tables for specific chat: {chat_id}")
                tables_result = self.rag_interface.get_user_tables(user_id, chat_id)
            else:
                print(f"No chat_id provided, getting all user tables")
                tables_result = self.rag_interface.get_user_tables(user_id)

            if not tables_result or not isinstance(tables_result, list):
                return {
                    "success": False,
                    "error": "Kullanıcı tabloları alınamadı."
                }

            # Return the latest table for this chat
            print(f"Found {len(tables_result)} tables for user {user_id} in chat {chat_id}")

            if not tables_result:
                return {
                    "success": False,
                    "error": f"Chat {chat_id} için CSV verisi bulunamadı."
                }

            # Return the most recently created table (latest upload)
            latest_table = max(tables_result, key=lambda x: x.get('created_at', ''))

            print(f"DatabaseToolkit: get_latest_chat_table - Found latest table: {latest_table.get('name')}")

            return {
                "success": True,
                "table": latest_table,
                "message": f"En son yüklenen tablo bulundu: {latest_table.get('name')}"
            }

        except Exception as e:
            print(f"❌ DatabaseToolkit: get_latest_chat_table error: {str(e)}")
            return {
                "success": False,
                "error": f"Tablo arama sırasında hata: {str(e)}"
            }


    def agregate_data(self, sql_query: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        SQL sorguları kullanarak kullanıcı verilerini toplar.
        Doğru veritabanını sorguda kullanmak için bu aracı kullanmadan önce kullanıcının tablo bilgilerini edinin.
        Bu araç sadece postgres veritabanı için çalışır. Lütfen posgresql odaklı sql sorguları yaz.

        Args:
            sql_query: Yürütülecek SQL sorgusu.
            user_id: Kullanıcının ID'si (kanca tarafından enjekte edilir).

        Returns:
            SQL sorgusunun sonucunu içeren bir sözlük.
        """
        # SECURITY FIX: Get user_id from context if it's wrong or missing
        from src.agents.agno_agents.user_context_manager import UserContextManager
        context_manager = UserContextManager()
        context_user_id = context_manager.get_current_user_id()
        
        if context_user_id:
            if not user_id or user_id in ['user', 'placeholder', 'user_id_placeholder', '']:
                print(f"🔧 DatabaseToolkit: agregate_data - Injecting user_id from context: {context_user_id}")
                user_id = context_user_id
            elif user_id != context_user_id:
                print(f"⚠️ DatabaseToolkit: agregate_data - Security override - using context user_id: {context_user_id} instead of: {user_id}")
                user_id = context_user_id
        
        if not user_id:
            raise ValueError("agregate_data için Kullanıcı ID'si sağlanmadı veya enjekte edilmedi.")
        print(f"DatabaseToolkit: agregate_data aracı user_id={user_id}, sql_query={sql_query} ile çağrıldı")
        # Assuming the RAGInterface method is indeed agregate_csv_data for historical reasons
        return self.rag_interface.agregate_csv_data(user_id, sql_query)

    def agregate_limited_data(self, sql_query: str, limit: int = 100, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        SQL sorguları kullanarak kullanıcı verilerini sınırlı miktarda toplar.
        Büyük veri setleri için performans optimizasyonu sağlar.

        Args:
            sql_query: Yürütülecek SQL sorgusu.
            limit: Döndürülecek maksimum satır sayısı (varsayılan: 100).
            user_id: Kullanıcının ID'si (kanca tarafından enjekte edilir).

        Returns:
            SQL sorgusunun sınırlı sonucunu içeren bir sözlük.
        """
        # SECURITY FIX: Get user_id from context if it's wrong or missing
        from src.agents.agno_agents.user_context_manager import UserContextManager
        context_manager = UserContextManager()
        context_user_id = context_manager.get_current_user_id()
        
        if context_user_id:
            if not user_id or user_id in ['user', 'placeholder', 'user_id_placeholder', '']:
                print(f"🔧 DatabaseToolkit: agregate_limited_data - Injecting user_id from context: {context_user_id}")
                user_id = context_user_id
            elif user_id != context_user_id:
                print(f"⚠️ DatabaseToolkit: agregate_limited_data - Security override - using context user_id: {context_user_id} instead of: {user_id}")
                user_id = context_user_id
        
        if not user_id:
            raise ValueError("agregate_limited_data için Kullanıcı ID'si sağlanmadı veya enjekte edilmedi.")

        # SQL sorgusuna LIMIT ekle (eğer yoksa)
        query_lower = sql_query.lower().strip()
        if 'limit' not in query_lower:
            # ORDER BY varsa ondan önce, yoksa sonuna LIMIT ekle
            if 'order by' in query_lower:
                # ORDER BY'dan önce LIMIT ekle
                order_by_index = query_lower.rfind('order by')
                limited_query = sql_query[:order_by_index].strip() + f" LIMIT {limit} " + sql_query[order_by_index:]
            else:
                # Sonuna LIMIT ekle
                limited_query = sql_query.rstrip(';') + f" LIMIT {limit}"
        else:
            limited_query = sql_query

        print(f"DatabaseToolkit: agregate_limited_data aracı user_id={user_id}, limit={limit}, sql_query={limited_query} ile çağrıldı")
        return self.rag_interface.agregate_csv_data(user_id, limited_query)



