import asyncio
from src.api.routes.chats.pdf_report_service import PDFReportService

async def main():
    """
    Test script to generate a PDF report for a specific chat ID.
    """
    # The chat ID to generate the report for
    chat_id = "b1d4d4cf-0755-41fb-b9c3-02e87766fe3e"
    
    # Initialize the PDF report service
    pdf_report_service = PDFReportService()
    
    print(f"Generating PDF report for chat ID: {chat_id}...")
    
    try:
        # Generate the PDF report
        await pdf_report_service.generate_pdf_report(chat_id)
        print("PDF report generated successfully.")
        print("You can find the report in the 'reports' directory.")
    except Exception as e:
        print(f"An error occurred during PDF report generation: {e}")

if __name__ == "__main__":
    # This script needs to be run in an environment where the application's
    # dependencies and configurations are available.
    # It assumes that the database and other services are accessible.
    
    # Run the asynchronous main function
    asyncio.run(main()) 