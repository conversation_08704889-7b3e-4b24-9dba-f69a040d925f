"""
Database operations for chat history
"""

from typing import List, Dict, Optional, Any
import logging
import uuid
from datetime import datetime, timezone

from .models import Base, UserChatInfo, UserChatHistory
from src.rag_engine.analitic.db.postgresql.database import get_db

logger = logging.getLogger(__name__)

class ChatHistoryDB:
    """
    Database operations for chat history
    """

    def __init__(self, db_uri: str = None):
        """
        Initialize the chat history database

        Args:
            db_uri: Database connection URI (optional)
        """
        self.db = get_db(db_uri)
        self.engine = self.db.engine
        self.SessionLocal = self.db.SessionLocal

    def initialize_tables(self):
        """
        Initialize database tables if they don't exist
        """
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Chat history tables initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing chat history tables: {str(e)}")
            raise

    def create_chat_session(self, user_id: str, chat_name: Optional[str] = None) -> str:
        """
        Create a new chat session for a user

        Args:
            user_id: User ID
            chat_name: Optional name for the chat

        Returns:
            str: Chat ID
        """
        try:
            chat_id = str(uuid.uuid4())

            with self.SessionLocal() as session:
                chat_info = UserChatInfo(
                    user_id=user_id,
                    chat_id=chat_id,
                    chat_name=chat_name
                )
                session.add(chat_info)
                session.commit()

            return chat_id
        except Exception as e:
            logger.error(f"Error creating chat session: {str(e)}")
            raise

    def add_message(self, chat_id: str, role: str, content: str, metadata: Optional[Dict[str, Any]] = None, user_id: Optional[str] = None) -> int:
        """
        Add a message to a chat session

        Args:
            chat_id: Chat ID
            role: Message role ('user', 'assistant', 'system', etc.)
            content: Message content
            metadata: Optional metadata
            user_id: User ID (optional, used to create a new chat session if chat_id not found)

        Returns:
            int: Message ID
        """
        try:
            with self.SessionLocal() as session:
                # Check if chat exists
                chat_info = session.query(UserChatInfo).filter(UserChatInfo.chat_id == chat_id).first()

                # If chat doesn't exist, raise an error
                if not chat_info:
                    raise ValueError(f"Chat session not found: {chat_id}")

                # Add message
                message = UserChatHistory(
                    chat_id=chat_id,
                    role=role,
                    content=content,
                    message_metadata=metadata
                )
                session.add(message)

                # Update chat timestamp
                chat_info.updated_at = datetime.now(timezone.utc)

                session.commit()
                return message.id
        except Exception as e:
            logger.error(f"Error adding message: {str(e)}")
            raise

    def get_chat_history(self, chat_id: str) -> List[Dict[str, Any]]:
        """
        Get the chat history for a chat session in CrewAI memory format

        Args:
            chat_id: Chat ID

        Returns:
            List[Dict[str, Any]]: Chat history in CrewAI memory format
        """
        try:
            with self.SessionLocal() as session:
                messages = session.query(UserChatHistory).filter(
                    UserChatHistory.chat_id == chat_id
                ).order_by(UserChatHistory.timestamp).all()

                return [message.to_crewai_memory_format() for message in messages]
        except Exception as e:
            logger.error(f"Error getting chat history: {str(e)}")
            raise

    def get_chat_messages(self, chat_id: str) -> List[Dict[str, Any]]:
        """
        Get all messages for a chat session

        Args:
            chat_id: Chat ID

        Returns:
            List[Dict[str, Any]]: List of messages
        """
        try:
            with self.SessionLocal() as session:
                messages = session.query(UserChatHistory).filter(
                    UserChatHistory.chat_id == chat_id
                ).order_by(UserChatHistory.timestamp).all()

                return [{
                    "id": str(message.id),
                    "chat_id": message.chat_id,
                    "sender": message.role,
                    "content": message.content,
                    "timestamp": message.timestamp.isoformat(),
                    "metadata": message.message_metadata
                } for message in messages]
        except Exception as e:
            logger.error(f"Error getting chat messages: {str(e)}")
            raise

    # get_messages_after method removed - message_id usage removed

    def get_chat_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a chat session

        Args:
            chat_id: Chat ID

        Returns:
            Optional[Dict[str, Any]]: Chat session information or None if not found
        """
        try:
            with self.SessionLocal() as session:
                chat = session.query(UserChatInfo).filter(
                    UserChatInfo.chat_id == chat_id
                ).first()

                if not chat:
                    return None

                return {
                    "id": chat.id,
                    "chat_id": chat.chat_id,
                    "user_id": chat.user_id,
                    "chat_name": chat.chat_name,
                    "created_at": chat.created_at.isoformat(),
                    "updated_at": chat.updated_at.isoformat(),
                    "is_active": chat.is_active
                }
        except Exception as e:
            logger.error(f"Error getting chat info: {str(e)}")
            raise

    def get_user_chats(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all chat sessions for a user

        Args:
            user_id: User ID

        Returns:
            List[Dict[str, Any]]: List of chat sessions
        """
        try:
            with self.SessionLocal() as session:
                chats = session.query(UserChatInfo).filter(
                    UserChatInfo.user_id == user_id
                ).order_by(UserChatInfo.updated_at.desc()).all()

                return [{
                    "id": chat.id,
                    "chat_id": chat.chat_id,
                    "chat_name": chat.chat_name,
                    "created_at": chat.created_at.isoformat(),
                    "updated_at": chat.updated_at.isoformat(),
                    "is_active": chat.is_active
                } for chat in chats]
        except Exception as e:
            logger.error(f"Error getting user chats: {str(e)}")
            raise

    def delete_chat(self, chat_id: str) -> bool:
        """
        Delete a chat session and all its messages

        Args:
            chat_id: Chat ID

        Returns:
            bool: True if successful
        """
        try:
            with self.SessionLocal() as session:
                # Check if chat exists
                chat_info = session.query(UserChatInfo).filter(UserChatInfo.chat_id == chat_id).first()
                if not chat_info:
                    raise ValueError(f"Chat session not found: {chat_id}")

                # Delete chat and all messages (cascade)
                session.delete(chat_info)
                session.commit()
                return True
        except Exception as e:
            logger.error(f"Error deleting chat: {str(e)}")
            raise

    def update_chat_name(self, chat_id: str, chat_name: str) -> bool:
        """
        Update the name of a chat session

        Args:
            chat_id: Chat ID
            chat_name: New chat name

        Returns:
            bool: True if successful
        """
        try:
            with self.SessionLocal() as session:
                # Check if chat exists
                chat_info = session.query(UserChatInfo).filter(UserChatInfo.chat_id == chat_id).first()
                if not chat_info:
                    raise ValueError(f"Chat session not found: {chat_id}")

                # Update chat name
                chat_info.chat_name = chat_name
                chat_info.updated_at = datetime.now(timezone.utc)

                session.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating chat name: {str(e)}")
            raise


# Create a singleton instance
chat_history_db = ChatHistoryDB()

def initialize_chat_history_tables():
    """
    Initialize chat history tables
    """
    chat_history_db.initialize_tables()


def get_chat_history_db() -> ChatHistoryDB:
    """
    Get the chat history database instance

    Returns:
        ChatHistoryDB: Chat history database instance
    """
    return chat_history_db
