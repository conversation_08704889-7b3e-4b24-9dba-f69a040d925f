import { createTheme } from '@mui/material/styles';

// QMindLab inspired theme
const theme = createTheme({
  palette: {
    mode: 'light', // Explicitly set light mode
    primary: {
      main: '#6633FF', // QMindLab Purple/Blue
      dark: '#4A23B8', // Darker shade
      light: '#A38DFF', // Lighter shade
      contrastText: '#ffffff', // White text on primary
    },
    secondary: {
      main: '#10b981', // Keep green as secondary for now, can adjust later
      dark: '#047857',
      light: '#34d399',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f8fafc', // Very light grey (similar to QMindLab background)
      paper: '#ffffff', // White for paper elements
    },
    text: {
      primary: '#111827', // Darker grey for main text
      secondary: '#6b7280', // Medium grey for secondary text
    },
    divider: '#e5e7eb', // Light grey for dividers
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif', // Keep Roboto for now, can change later if needed
    h1: { fontSize: '2.5rem', fontWeight: 700 },
    h2: { fontSize: '2rem', fontWeight: 600 },
    h3: { fontSize: '1.5rem', fontWeight: 600 },
    body1: { fontSize: '1rem', lineHeight: 1.5 },
    body2: { fontSize: '0.875rem', lineHeight: 1.5 },
  },
  shape: {
    borderRadius: 16, // Increased border radius significantly
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', // Subtle shadow like QMindLab
          borderRadius: 16, // Use the increased border radius
        },
        containedPrimary: {
          // Ensure contrast text is white
          color: '#ffffff',
        }
      },
    },
    MuiPaper: { // Apply slight shadow to Paper components like QMindLab cards
      styleOverrides: {
        root: {
           boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.07), 0 1px 2px -1px rgb(0 0 0 / 0.07)', // Softer shadow
           borderRadius: 16, // Apply increased radius to Paper by default
        },
      },
    },
    MuiDialog: { // Apply radius to dialogs
      styleOverrides: {
        paper: {
          borderRadius: 16,
        }
      }
    },
    MuiIconButton: { // Style icon buttons like QMindLab's subtle buttons
        styleOverrides: {
            root: {
                // Add a subtle background on hover if needed
                // '&:hover': {
                //     backgroundColor: 'rgba(102, 51, 255, 0.04)'
                // }
                borderRadius: 16, // Make icon buttons rounder too
            }
        }
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 16, // Use the increased border radius
          '&:hover': {
            backgroundColor: 'rgba(102, 51, 255, 0.04)' // Subtle hover effect
          }
        }
      }
    },
    MuiInputBase: { // Apply to input fields globally
      styleOverrides: {
        root: {
          borderRadius: 16, // Use the increased border radius
        }
      }
    },
    MuiOutlinedInput: { // Specifically target outlined variant if needed
        styleOverrides: {
            root: {
                borderRadius: 16,
            }
        }
    }
  },
});

export default theme; 