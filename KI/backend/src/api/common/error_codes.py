from enum import Enum
from typing import Dict

class ErrorCode(str, Enum):
    # General Errors
    INTERNAL_SERVER_ERROR = "internal_server_error"
    SERVICE_UNAVAILABLE = "service_unavailable"
    UNKNOWN_ERROR = "unknown_error"
    OPERATION_TIMED_OUT = "operation_timed_out"

    # Request Validation Errors
    INVALID_REQUEST_BODY = "invalid_request_body"
    VALIDATION_ERROR = "validation_error"
    MISSING_REQUIRED_FIELD = "missing_required_field"
    INVALID_PARAMETER_VALUE = "invalid_parameter_value"
    INVALID_FORMAT = "invalid_format" # E.g., for UUID, date, email
    INVALID_URL_PARAMETER = "invalid_url_parameter"
    INVALID_HEADER_VALUE = "invalid_header_value"
    REQUEST_SIZE_EXCEEDED = "request_size_exceeded"

    # Authentication & Authorization Errors
    AUTHENTICATION_FAILED = "authentication_failed"
    NOT_AUTHENTICATED = "not_authenticated"
    PERMISSION_DENIED = "permission_denied"
    INVALID_API_KEY = "invalid_api_key"
    TOKEN_EXPIRED = "token_expired"
    TOKEN_INVALID = "token_invalid"

    # Resource Errors
    RESOURCE_NOT_FOUND = "resource_not_found"
    RESOURCE_ALREADY_EXISTS = "resource_already_exists"
    RESOURCE_STATE_CONFLICT = "resource_state_conflict" # E.g., trying to modify a completed/locked resource
    RESOURCE_LIMIT_EXCEEDED = "resource_limit_exceeded"

    # File Handling Errors
    FILE_UPLOAD_FAILED = "file_upload_failed"
    FILE_PROCESSING_FAILED = "file_processing_failed"
    UNSUPPORTED_FILE_TYPE = "unsupported_file_type"
    INVALID_FILE_ENCODING = "invalid_file_encoding"
    FILE_NOT_FOUND = "file_not_found"
    FILE_TOO_LARGE = "file_too_large"

    # Database Errors
    DATABASE_OPERATION_FAILED = "database_operation_failed"
    DATABASE_QUERY_FAILED = "database_query_failed"
    DATABASE_CONNECTION_ERROR = "database_connection_error"
    RECORD_NOT_FOUND_IN_DB = "record_not_found_in_db" # More specific than generic resource_not_found

    # Chat/Task Specific Errors
    CHAT_SESSION_NOT_FOUND = "chat_session_not_found"
    TASK_NOT_FOUND = "task_not_found"
    TASK_CREATION_FAILED = "task_creation_failed"
    TASK_UPDATE_FAILED = "task_update_failed"
    ANALYSIS_FAILED = "analysis_failed"
    WEBSOCKET_ERROR = "websocket_error"

    # External Service Errors
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    EXTERNAL_SERVICE_TIMEOUT = "external_service_timeout"


ERROR_MESSAGES: Dict[ErrorCode, str] = {
    # General Errors
    ErrorCode.INTERNAL_SERVER_ERROR: "Sunucuda beklenmedik bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
    ErrorCode.SERVICE_UNAVAILABLE: "Servis geçici olarak kullanılamıyor. Lütfen daha sonra tekrar deneyin.",
    ErrorCode.UNKNOWN_ERROR: "Bilinmeyen bir hata oluştu.",
    ErrorCode.OPERATION_TIMED_OUT: "İşlem zaman aşımına uğradı.",

    # Request Validation Errors
    ErrorCode.INVALID_REQUEST_BODY: "Geçersiz istek gövdesi. Lütfen formatı kontrol edin.",
    ErrorCode.VALIDATION_ERROR: "Doğrulama hatası. Lütfen sağlanan detayları kontrol edin.",
    ErrorCode.MISSING_REQUIRED_FIELD: "Gerekli bir alan eksik.",
    ErrorCode.INVALID_PARAMETER_VALUE: "Sağlanan parametre değeri geçersiz.",
    ErrorCode.INVALID_FORMAT: "Sağlanan değer geçersiz bir formatta.",
    ErrorCode.INVALID_URL_PARAMETER: "URL parametresi geçersiz.",
    ErrorCode.INVALID_HEADER_VALUE: "Header değeri geçersiz.",
    ErrorCode.REQUEST_SIZE_EXCEEDED: "İstek boyutu limiti aştı.",

    # Authentication & Authorization Errors
    ErrorCode.AUTHENTICATION_FAILED: "Kimlik doğrulama başarısız.",
    ErrorCode.NOT_AUTHENTICATED: "Bu işlem için kimliğiniz doğrulanmamış.",
    ErrorCode.PERMISSION_DENIED: "Bu işlemi gerçekleştirmek için yetkiniz yok.",
    ErrorCode.INVALID_API_KEY: "Sağlanan API anahtarı geçersiz.",
    ErrorCode.TOKEN_EXPIRED: "Oturumunuzun süresi doldu. Lütfen tekrar giriş yapın.",
    ErrorCode.TOKEN_INVALID: "Sağlanan token geçersiz.",

    # Resource Errors
    ErrorCode.RESOURCE_NOT_FOUND: "İstenen kaynak bulunamadı.",
    ErrorCode.RESOURCE_ALREADY_EXISTS: "Bu kaynak zaten mevcut.",
    ErrorCode.RESOURCE_STATE_CONFLICT: "Kaynak şu anki durumunda bu işlemi desteklemiyor.",
    ErrorCode.RESOURCE_LIMIT_EXCEEDED: "Kaynak limiti aşıldı.",

    # File Handling Errors
    ErrorCode.FILE_UPLOAD_FAILED: "Dosya yükleme başarısız oldu.",
    ErrorCode.FILE_PROCESSING_FAILED: "Dosya işleme sırasında bir hata oluştu.",
    ErrorCode.UNSUPPORTED_FILE_TYPE: "Desteklenmeyen dosya türü.",
    ErrorCode.INVALID_FILE_ENCODING: "Geçersiz dosya kodlaması.",
    ErrorCode.FILE_NOT_FOUND: "Dosya bulunamadı.",
    ErrorCode.FILE_TOO_LARGE: "Dosya boyutu çok büyük.",

    # Database Errors
    ErrorCode.DATABASE_OPERATION_FAILED: "Veritabanı işlemi başarısız oldu.",
    ErrorCode.DATABASE_QUERY_FAILED: "Veritabanı sorgusu başarısız oldu.",
    ErrorCode.DATABASE_CONNECTION_ERROR: "Veritabanı bağlantı hatası.",
    ErrorCode.RECORD_NOT_FOUND_IN_DB: "Kayıt veritabanında bulunamadı.",

    # Chat/Task Specific Errors
    ErrorCode.CHAT_SESSION_NOT_FOUND: "Chat oturumu bulunamadı.",
    ErrorCode.TASK_NOT_FOUND: "Görev bulunamadı.",
    ErrorCode.TASK_CREATION_FAILED: "Görev oluşturma başarısız oldu.",
    ErrorCode.TASK_UPDATE_FAILED: "Görev güncelleme başarısız oldu.",
    ErrorCode.ANALYSIS_FAILED: "Analiz işlemi başarısız oldu.",
    ErrorCode.WEBSOCKET_ERROR: "WebSocket bağlantısında bir hata oluştu.",

    # External Service Errors
    ErrorCode.EXTERNAL_SERVICE_ERROR: "Harici servisde bir hata oluştu.",
    ErrorCode.EXTERNAL_SERVICE_TIMEOUT: "Harici servisten yanıt alınamadı (zaman aşımı).",
} 