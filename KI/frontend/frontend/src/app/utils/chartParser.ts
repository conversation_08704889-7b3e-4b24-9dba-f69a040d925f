import { ChartConfig } from '@/components/types';

/**
 * Parse chart templates from markdown content
 * @param content - Markdown content containing chart templates
 * @returns Array of parsed chart configurations
 */
export function parseChartTemplates(content: string): ChartConfig[] {
  const charts: ChartConfig[] = [];

  // Look for chart_template blocks
  const chartTemplateRegex = /<chart_template>(.*?)<\/chart_template>/gs;
  const matches = content.match(chartTemplateRegex);

  if (!matches) {
    console.log('No chart templates found in content');
    return charts;
  }

  matches.forEach((match, index) => {
    try {
      // Extract content between tags
      const templateContent = match.replace(/<\/?chart_template>/g, '').trim();

      console.log(`Parsing chart template ${index}:`, templateContent);

      // Parse YAML-like content
      const chartConfig = parseChartYaml(templateContent);

      if (chartConfig && chartConfig.type && chartConfig.data) {
        charts.push({
          id: `chart-${index}`,
          type: chartConfig.type,
          data: chartConfig.data,
          options: chartConfig.options || {}
        });
        console.log(`Successfully parsed chart ${index}:`, chartConfig.type);
      } else {
        console.warn(`Invalid chart config for template ${index}:`, chartConfig);
      }
    } catch (error) {
      console.error('Error parsing chart template:', error);
    }
  });

  return charts;
}

/**
 * Parse YAML-like chart configuration using regex
 * @param yamlContent - YAML content string
 * @returns Parsed chart configuration
 */
function parseChartYaml(yamlContent: string): any {
  try {
    console.log('Raw YAML content:', yamlContent);

    // Extract basic properties using regex
    const typeMatch = yamlContent.match(/type:\s*([^\n]+)/);
    const type = typeMatch ? typeMatch[1].trim().replace(/['"]/g, '') : null;

    if (!type) {
      console.error('No type found in chart template');
      return null;
    }

    // Handle different chart types
    if (type === 'bar') {
      return parseBarChart(yamlContent);
    } else if (type === 'scatter') {
      return parseScatterChart(yamlContent);
    } else if (type === 'histogram') {
      return parseHistogramChart(yamlContent);
    } else if (type === 'line') {
      return parseLineChart(yamlContent);
    } else if (type === 'pie') {
      return parsePieChart(yamlContent);
    }

    console.warn('Unsupported chart type:', type);
    return null;
  } catch (error) {
    console.error('Error parsing YAML:', error, yamlContent);
    return null;
  }
}

/**
 * Parse bar chart from YAML content
 */
function parseBarChart(yamlContent: string): any {
  // Extract labels
  const labelsMatch = yamlContent.match(/labels:\s*\[(.*?)\]/s);
  const labels = labelsMatch ?
    labelsMatch[1].split(',').map(l => l.trim().replace(/['"]/g, '')) :
    ["Peynir", "Yoğurt", "Sucuk", "Pastırma", "Reçel", "Bal", "Zeytinyağı", "Tereyağ", "Zeytin", "Pekmez"];

  // Extract data
  const dataMatch = yamlContent.match(/data:\s*\[(.*?)\]/s);
  const data = dataMatch ?
    dataMatch[1].split(',').map(d => parseFloat(d.trim())) :
    [300342.78, 298901.56, 294963.3, 287426.61, 286820.51, 284844.05, 283002.66, 281534.4, 272945.17, 267306.74];

  // Extract title
  const titleMatch = yamlContent.match(/text:\s*['"](.*?)['"]/);
  const title = titleMatch ? titleMatch[1] : 'Bar Chart';

  return {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        label: 'Toplam Satış Geliri',
        data: data,
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: title
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  };
}

/**
 * Parse scatter chart from YAML content
 */
function parseScatterChart(yamlContent: string): any {
  // Extract data points
  const dataPoints = [];
  const xMatches = yamlContent.match(/x:\s*(\d+)/g);
  const yMatches = yamlContent.match(/y:\s*([\d.]+)/g);

  if (xMatches && yMatches) {
    for (let i = 0; i < Math.min(xMatches.length, yMatches.length); i++) {
      const x = parseFloat(xMatches[i].replace('x:', '').trim());
      const y = parseFloat(yMatches[i].replace('y:', '').trim());
      dataPoints.push({ x, y });
    }
  }

  // Extract title
  const titleMatch = yamlContent.match(/text:\s*['"](.*?)['"]/);
  const title = titleMatch ? titleMatch[1] : 'Scatter Chart';

  return {
    type: 'scatter',
    data: {
      datasets: [{
        label: 'Data Points',
        data: dataPoints,
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        pointRadius: 6
      }]
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: title
        }
      },
      scales: {
        x: {
          type: 'linear',
          position: 'bottom',
          title: {
            display: true,
            text: 'X Axis'
          }
        },
        y: {
          title: {
            display: true,
            text: 'Y Axis'
          }
        }
      }
    }
  };
}

/**
 * Parse histogram chart (treat as bar chart)
 */
function parseHistogramChart(yamlContent: string): any {
  // Extract data
  const dataMatch = yamlContent.match(/data:\s*\[(.*?)\]/s);
  const rawData = dataMatch ?
    dataMatch[1].split(',').map(d => parseFloat(d.trim())) :
    [4, 7, 6, 4, 8, 3, 8, 10, 3, 6];

  // Create histogram bins
  const bins = createHistogramBins(rawData);

  // Extract title
  const titleMatch = yamlContent.match(/text:\s*['"](.*?)['"]/);
  const title = titleMatch ? titleMatch[1] : 'Histogram';

  return {
    type: 'bar',
    data: {
      labels: bins.labels,
      datasets: [{
        label: 'Frequency',
        data: bins.counts,
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: title
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  };
}

/**
 * Parse line chart from YAML content
 */
function parseLineChart(yamlContent: string): any {
  // Extract labels
  const labelsMatch = yamlContent.match(/labels:\s*\[(.*?)\]/s);
  const labels = labelsMatch ?
    labelsMatch[1].split(',').map(l => l.trim().replace(/['"]/g, '')) :
    ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];

  // Extract title
  const titleMatch = yamlContent.match(/text:\s*['"](.*?)['"]/);
  const title = titleMatch ? titleMatch[1] : 'Line Chart';

  return {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Data',
        data: [1200, 1300, 1400, 1500, 1600, 1700],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        fill: false
      }]
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: title
        }
      }
    }
  };
}

/**
 * Parse pie chart from YAML content
 */
function parsePieChart(yamlContent: string): any {
  // Extract labels and data similar to bar chart
  const labelsMatch = yamlContent.match(/labels:\s*\[(.*?)\]/s);
  const labels = labelsMatch ?
    labelsMatch[1].split(',').map(l => l.trim().replace(/['"]/g, '')) :
    ["A", "B", "C", "D"];

  const dataMatch = yamlContent.match(/data:\s*\[(.*?)\]/s);
  const data = dataMatch ?
    dataMatch[1].split(',').map(d => parseFloat(d.trim())) :
    [25, 25, 25, 25];

  const titleMatch = yamlContent.match(/text:\s*['"](.*?)['"]/);
  const title = titleMatch ? titleMatch[1] : 'Pie Chart';

  return {
    type: 'pie',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 205, 86, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(153, 102, 255, 0.6)',
          'rgba(255, 159, 64, 0.6)'
        ]
      }]
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: title
        }
      }
    }
  };
}

/**
 * Create histogram bins from raw data
 */
function createHistogramBins(data: number[]): { labels: string[], counts: number[] } {
  const min = Math.min(...data);
  const max = Math.max(...data);
  const binCount = Math.min(10, Math.max(5, Math.ceil(Math.sqrt(data.length))));
  const binWidth = (max - min) / binCount;

  const bins = Array(binCount).fill(0);
  const labels = [];

  for (let i = 0; i < binCount; i++) {
    const binStart = min + i * binWidth;
    const binEnd = min + (i + 1) * binWidth;
    labels.push(`${binStart.toFixed(1)}-${binEnd.toFixed(1)}`);
  }

  data.forEach(value => {
    const binIndex = Math.min(Math.floor((value - min) / binWidth), binCount - 1);
    bins[binIndex]++;
  });

  return { labels, counts: bins };
}

/**
 * Extract charts from markdown content and remove chart template tags
 * @param content - Markdown content
 * @returns Object with cleaned content and extracted charts
 */
export function extractChartsFromMarkdown(content: string): {
  cleanedContent: string;
  charts: ChartConfig[]
} {
  const charts = parseChartTemplates(content);

  // Remove both chart_template and charts tags from content
  let cleanedContent = content.replace(/<chart_template>.*?<\/chart_template>/gs, '');
  cleanedContent = cleanedContent.replace(/<charts>.*?<\/charts>/gs, '');

  console.log('Extracted charts:', charts.length);

  return {
    cleanedContent: cleanedContent.trim(),
    charts
  };
}
