import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Collapse from '@mui/material/Collapse';
import Timeline from '@mui/lab/Timeline';
import TimelineItem from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineOppositeContent from '@mui/lab/TimelineOppositeContent';
import Paper from '@mui/material/Paper';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import LinearProgress from '@mui/material/LinearProgress';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import BarChartIcon from '@mui/icons-material/BarChart';
import TableChartIcon from '@mui/icons-material/TableChart';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ErrorBoundary } from 'react-error-boundary';
import { TaskStatus, ProgressStatus, TaskHistoryItem, ChartConfig } from './types';
import ChartDisplay from './ChartDisplay';
import TableDisplay from './TableDisplay';
import Tooltip from '@mui/material/Tooltip';

// Helper function to get status color
const getStatusColor = (status: TaskStatus): "success" | "error" | "primary" | "info" | "warning" | "grey" => {
  switch(status) {
    case 'completed': return 'success';
    case 'failed': return 'error';
    case 'executing': return 'primary';
    case 'planning': return 'info';
    case 'routing': return 'info';
    case 'queued_for_execution': return 'warning';
    case 'pending':
    default: return 'grey';
  }
};

// Helper function for relative time
const getRelativeTime = (timestamp?: number): string => {
  if (!timestamp) return '';
  const msPerMinute = 60 * 1000;
  const msPerHour = msPerMinute * 60;
  const msPerDay = msPerHour * 24;
  const elapsed = Date.now() - timestamp;
  if (elapsed < msPerMinute) { return 'Az önce'; }
  else if (elapsed < msPerHour) { return Math.round(elapsed / msPerMinute) + ' dakika önce'; }
  else if (elapsed < msPerDay ) { return Math.round(elapsed / msPerHour ) + ' saat önce'; }
  else { return Math.round(elapsed / msPerDay) + ' gün önce'; }
};

interface TaskDetailItemProps {
  taskId: string;
  taskInfo: TaskHistoryItem;
  toggleTaskExpansion: (taskId: string) => void;
  chatId: string;
  userId: string;
}

const TaskDetailItem: React.FC<TaskDetailItemProps> = ({ taskId, taskInfo, toggleTaskExpansion, chatId, userId }) => {
  const [isChartModalOpen, setIsChartModalOpen] = useState(false);
  const [isTableModalOpen, setIsTableModalOpen] = useState(false);
  const [markdownRenderError, setMarkdownRenderError] = useState(false);

  const handleOpenChartModal = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsChartModalOpen(true);
  };
  const handleCloseChartModal = () => {
    setIsChartModalOpen(false);
  };

  const handleOpenTableModal = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsTableModalOpen(true);
  };
  const handleCloseTableModal = () => {
    setIsTableModalOpen(false);
  };

  const charts = taskInfo.lastUpdate?.charts;
  const hasCharts = Array.isArray(charts) && charts.length > 0;
  
  const tables = taskInfo.lastUpdate?.output?.reports;
  const hasTables = Array.isArray(tables) && tables.length > 0;
  
  // Debug logging
  console.log('🔍 TaskDetailItem Debug:', {
    taskId: taskId.substring(0, 8),
    hasOutput: !!taskInfo.lastUpdate?.output,
    hasReports: !!taskInfo.lastUpdate?.output?.reports,
    reportsLength: taskInfo.lastUpdate?.output?.reports?.length || 0,
    tables: tables,
    hasTables: hasTables,
    fullOutput: taskInfo.lastUpdate?.output
  });


  return (
    <Box sx={{ mb: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 1.5,
          cursor: 'pointer',
          '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' },
          bgcolor: taskInfo.isExpanded ? 'rgba(0,0,0,0.02)' : 'transparent'
        }}
        onClick={() => toggleTaskExpansion(taskId)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {hasCharts && (
            <Tooltip title="Grafikleri Görüntüle">
              <IconButton
                size="small"
                onClick={handleOpenChartModal}
                sx={{ mr: 1 }}
                color="primary"
              >
                <BarChartIcon />
              </IconButton>
            </Tooltip>
          )}
          {hasTables && (
            <Tooltip title="Tabloları Görüntüle">
              <IconButton
                size="small"
                onClick={handleOpenTableModal}
                sx={{ mr: 1 }}
                color="secondary"
              >
                <TableChartIcon />
              </IconButton>
            </Tooltip>
          )}
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
              Görev #{taskId.substring(0, 8)}...
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {getRelativeTime(taskInfo.lastUpdate.timestamp)} • {
                ({
                  'pending': 'Beklemede',
                  'planning': 'Planlanıyor',
                  'routing': 'Yönlendiriliyor',
                  'queued_for_execution': 'Kuyrukta',
                  'executing': 'Yürütülüyor',
                  'completed': 'Tamamlandı',
                  'failed': 'Başarısız'
                } as Record<TaskStatus, string>)[taskInfo.lastUpdate.taskStatus] || 'Bilinmeyen Durum'
              }
            </Typography>
          </Box>
        </Box>
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation(); 
            toggleTaskExpansion(taskId);
          }}
        >
          {taskInfo.isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
        </IconButton>
      </Box>

      <Collapse in={taskInfo.isExpanded} timeout="auto" unmountOnExit>
        <Timeline
          position="right"
          sx={{
            p: 0,
            m: 0,
            [`& .MuiTimelineItem-root:before`]: {
              flex: 0,
              padding: 0
            }
          }}
        >
          {taskInfo.updates.map((update: ProgressStatus, index: number) => (
            <TimelineItem key={update.id}>
              <TimelineOppositeContent color="text.secondary" sx={{ flex: 0.2, minWidth: '70px', pt: '18px' }}>
                <Typography variant="caption">
                  {new Date(update.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot color={getStatusColor(update.taskStatus)} />
                {index < taskInfo.updates.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Typography variant="body2" component="span">
                  {update.message}
                </Typography>

                {update.startedAt && update.completedAt && (
                  <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'text.secondary' }}>
                    Yürütme süresi: {Math.round((update.completedAt - update.startedAt) / 1000)} saniye
                  </Typography>
                )}

                {update.taskStatus === 'executing' && typeof update.percentComplete === 'number' && (
                  <Box sx={{ mt: 1, mb: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={update.percentComplete}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" sx={{ display: 'block', mt: 0.5, textAlign: 'center' }}>
                      %{Math.round(update.percentComplete)}
                    </Typography>
                  </Box>
                )}

                {update.subtasks && update.subtasks.length > 0 && (
                  <Paper
                    variant="outlined"
                    sx={{ mt: 1, p: 1, bgcolor: 'background.paper' }}
                  >
                    <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                      Alt Görevler:
                    </Typography>
                    <List dense disablePadding>
                      {update.subtasks.map((subtask: string) => (
                        <ListItem key={subtask} dense sx={{ px: 1, py: 0.25 }}>
                          <ListItemIcon sx={{ minWidth: 28 }}>
                            {update.subtaskStatus?.[subtask] === 'completed' && <CheckCircleOutlineIcon color="success" fontSize="small" />}
                            {update.subtaskStatus?.[subtask] === 'executing' && <AutorenewIcon color="primary" fontSize="small" sx={{ animation: 'spin 1.5s linear infinite' }} />}
                            {update.subtaskStatus?.[subtask] === 'failed' && <ErrorOutlineIcon color="error" fontSize="small" />}
                            {(!update.subtaskStatus?.[subtask] || update.subtaskStatus?.[subtask] === 'pending') && <InfoOutlinedIcon color="action" fontSize="small" />}
                          </ListItemIcon>
                          <ListItemText
                            primary={subtask}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                )}

                {update.error && (
                  <Paper variant="outlined" sx={{ mt: 1, p: 1.5, bgcolor: 'error.light', color: 'error.contrastText' }}>
                    <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                      Hata:
                    </Typography>
                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                       {update.error}
                     </Typography>
                  </Paper>
                )}
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </Collapse>

      <Dialog open={isChartModalOpen} onClose={handleCloseChartModal} maxWidth="lg" fullWidth scroll="paper">
        <DialogTitle>Görev Grafikleri (#{taskId.substring(0, 8)}...)</DialogTitle>
        <DialogContent>
          {hasCharts && charts ? (
            charts.map((chartConfig: ChartConfig) => (
              <Box key={chartConfig.id} sx={{ mb: 3 }}>
                  <ChartDisplay chartType={chartConfig.type} data={chartConfig.data} options={chartConfig.options} />
              </Box>
            ))
          ) : (
            <Typography sx={{ textAlign: 'center', p: 3 }}>Bu görev için görüntülenek grafik bulunamadı.</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseChartModal}>Kapat</Button>
        </DialogActions>
      </Dialog>

      {hasTables && (
        <TableDisplay
          open={isTableModalOpen}
          onClose={handleCloseTableModal}
          tables={tables || []}
          chatId={chatId}
          userId={userId}
        />
      )}

    </Box>
  );
};

export default TaskDetailItem; 