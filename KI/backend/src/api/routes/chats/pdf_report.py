from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, BackgroundTasks, Path as FastAPIPath, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field
from pathlib import Path
import os
import uuid

from src.api.routes.chats.pdf_report_service import PDFReportService
from src.agents.datasets.models import UserChatInfo as ChatSession
from src.rag_engine.analitic.db.postgresql.database import get_session
from sqlalchemy.orm import Session


router = APIRouter(
    tags=["Chats"],
)
pdf_report_service = PDFReportService()
REPORTS_DIR = Path.cwd() / "reports" / "pdf"

# Ensure the reports directory exists
os.makedirs(REPORTS_DIR, exist_ok=True)

class ReportRequest(BaseModel):
    user_id: uuid.UUID = Field(..., description="ID of the user requesting the report.")


def get_chat_and_verify_user(chat_id: uuid.UUID, user_id: uuid.UUID, db: Session):
    """Dependency function to get a chat session and verify the user owns it."""
    chat_session = db.query(ChatSession).filter(ChatSession.chat_id == str(chat_id), ChatSession.user_id == str(user_id)).first()
    if not chat_session:
        raise HTTPException(status_code=404, detail=f"Chat '{chat_id}' not found or you don't have access.")
    return chat_session


@router.post(
    "/{chat_id}/reports",
    status_code=202,
    summary="Generate PDF Report for Chat",
    description="Triggers the asynchronous generation of a PDF report for the specified chat session. The report will be available for download later.",
)
async def generate_chat_report(
    background_tasks: BackgroundTasks,
    request_body: ReportRequest,
    chat_id: uuid.UUID = FastAPIPath(..., description="ID of the chat to generate a report for."),
    db: Session = Depends(get_session),
):
    """
    Starts the PDF report generation for a chat session in the background.
    - **chat_id**: The UUID of the chat session.
    - **user_id**: The UUID of the user who owns the chat.
    """
    get_chat_and_verify_user(chat_id=chat_id, user_id=request_body.user_id, db=db)

    # The service expects the chat_id as a string
    background_tasks.add_task(pdf_report_service.generate_pdf_report, str(chat_id))
    
    return {
        "message": "PDF report generation has been started.",
        "chat_id": str(chat_id),
    }


@router.get(
    "/{chat_id}/reports/download",
    summary="Download PDF Report",
    description="Downloads a previously generated PDF report for a chat session.",
    response_class=FileResponse,
)
async def download_pdf_report(
    chat_id: uuid.UUID = FastAPIPath(..., description="ID of the chat session."),
    user_id: uuid.UUID = Query(..., description="ID of the user for authorization."),
    db: Session = Depends(get_session),
):
    """
    Downloads the PDF report file for a given chat ID.
    The file must have been generated previously.
    - **chat_id**: The UUID of the chat session.
    - **user_id**: The UUID of the user for authorization.
    """
    get_chat_and_verify_user(chat_id=chat_id, user_id=user_id, db=db)
        
    report_filename = f"report_{str(chat_id)}.pdf"
    report_path = REPORTS_DIR / report_filename

    if not report_path.exists():
        raise HTTPException(
            status_code=404,
            detail=f"Report for chat '{chat_id}' not found. Please generate it first.",
        )
    
    return FileResponse(
        path=report_path,
        filename=report_filename,
        media_type="application/pdf",
    )
