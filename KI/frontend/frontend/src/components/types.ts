import { ChartConfiguration } from 'chart.js';

// Interface for individual chart configuration (can be expanded)
export interface ChartConfig {
  id: string;
  type: 'bar' | 'line' | 'pie' | 'scatter'; // Add other types as needed
  data: any; // Consider using Chart.js specific types for better safety
  options?: any; // Consider using Chart.js specific types
}

// Interface for table information from reports
export interface TableInfo {
  file_name: string;
  table_name: string;
  table_description: string;
  generated_at: number;
  type: 'csv' | 'excel' | string; // Support known types + future types
}

// Interface for task output structure
export interface TaskOutput {
  markdown_output?: string;
  charts?: ChartConfig[];
  tables?: string[]; // Array of file paths
  reports?: TableInfo[]; // Array of report metadata
  report?: string; // Markdown report content
  content?: string; // General content field
  plan?: {
    reason: string;
    task_list: string[];
    agents?: Array<{
      agent_id: string;
      agent_name: string;
      task: string;
      reason: string;
    }>;
  } | null;
}

// Statuses used in the UI
export type StatusType = 'info' | 'processing' | 'success' | 'error';

// Raw task statuses from the backend
export type TaskStatus =
  | 'pending'
  | 'planning'
  | 'routing'
  | 'queued_for_execution'
  | 'executing'
  | 'completed'
  | 'failed';

// Represents a single status update processed for the UI
export interface ProgressStatus {
  id: string; // Unique ID for React key, e.g., task_key-status-timestamp
  taskId: string;
  timestamp: number;
  status: StatusType; // UI status (info, processing, success, error)
  message: string; // User-friendly message
  taskStatus: TaskStatus; // Original backend status
  currentTask: string | null | undefined; // Name of the current sub-task being executed
  output: TaskOutput | null | undefined; // Output data from the task (e.g., final answer)
  error: string | null | undefined; // Error message if failed
  subtasks: string[] | null | undefined; // List of subtasks if available
  subtaskStatus: { [key: string]: string } | null | undefined; // Status of subtasks
  percentComplete: number | null | undefined; // Overall progress percentage
  createdAt: number | undefined; // Task creation timestamp
  startedAt: number | undefined; // Task execution start timestamp
  completedAt: number | undefined; // Task completion/failure timestamp
  charts: ChartConfig[] | null | undefined; // Chart data
}

// Represents the history and state for a single task ID
export interface TaskHistoryItem {
  updates: ProgressStatus[]; // All status updates received for this task
  lastUpdate: ProgressStatus; // The most recent status update
  isExpanded: boolean; // Whether the task details are expanded in the UI
  createdAt: number; // Timestamp of the first update received for this task
  charts?: ChartConfig[] | null; // << Add charts here (optional, could derive from lastUpdate)
}

// Structure to hold the history of all tasks in a chat
export interface TaskHistory {
  [taskId: string]: TaskHistoryItem;
}

// Structure for WebSocket task update messages
export interface TaskUpdate {
  type: 'task_update';
  task_key: string;
  status: TaskStatus;
  current_task?: string | null;
  progress?: {
    created_at?: number;
    started_at?: number;
    completed_at?: number;
    percent_complete?: number;
  } | null;
  output?: any | null; // Includes answer, sources, charts, etc.
  reports?: TableInfo[] | null; // Reports array from top-level message
  error?: string | null;
  subtasks?: string[] | null;
  subtask_status?: { [key: string]: string } | null;
}

// Move ChatMessage interface here

// Define the structure for a plan
export interface Plan {
  reason: string;
  task_list: string[];
}

// Define the structure for a chat message (used in CenterPanel and API)
export interface ChatMessage {
  id: string;
  sender: 'user' | 'agent';
  text: string;
  timestamp: number; // Assuming the backend stores timestamp (e.g., as float/int seconds since epoch)
  sources?: any[];
  error?: boolean;
  plan?: Plan | null;
  task_list?: string[];
  isTaskOutput?: boolean;
  charts?: ChartConfig[];
  taskKey?: string; // Task identifier for linking messages with task updates
}