:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 248, 250, 252;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-rgb: 15, 23, 42;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

html, body, #root {
  height: 100%;
}

/* Animation for thinking dots */
@keyframes wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-5px);
  }
}

/* Basic Layout Styles */
.app-container {
  display: flex;
  flex-grow: 1; /* Allow this container to grow and fill remaining vertical space */
  width: 100%; /* Take full width */
  overflow: hidden; /* Prevent body scroll */
}

.left-panel {
  width: 280px; /* Adjust as needed */
  flex-shrink: 0; /* Prevent shrinking */
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease; /* Smooth transition for width changes */
}

.left-panel.collapsed {
  width: 60px; /* Width when collapsed */
  overflow: hidden;
}

.center-panel {
  flex-grow: 1; /* Take remaining space */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent content overflow */
  transition: flex-grow 0.3s ease; /* Smooth transition for expansion */
}

/* Make center panel expand more when side panels are collapsed */
.app-container.left-collapsed .center-panel {
  flex-grow: 1.2; /* Give more space to center when left panel is collapsed */
}

.app-container.right-collapsed .center-panel {
  flex-grow: 1.2; /* Give more space to center when right panel is collapsed */
}

.app-container.left-collapsed.right-collapsed .center-panel {
  flex-grow: 1.4; /* Give even more space when both panels are collapsed */
}

.right-panel {
  /* Removed fixed width, now controlled by ResizableBox */
  /* width: 350px; */
  height: 100%; /* Ensure RightPanel fills the ResizableBox height */
  width: 100%; /* Ensure RightPanel fills the ResizableBox width */
  flex-shrink: 0;
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  transition: width 0.3s ease; /* Smooth transition for collapse/expand */
  background-color: #fff; /* Ensure background for handle visibility */
  display: flex; /* Already flex, ensure it stays */
  flex-direction: column; /* Already column, ensure it stays */
  overflow: hidden; /* Prevent internal content overflow */
}

/* Styles for the collapsed state */
.right-panel.collapsed {
  /* Collapsed state width still applies if needed, but resizing is now primary */
  width: 60px !important; /* Use important if ResizableBox inline style conflicts */
  min-width: 60px !important; /* Ensure it can collapse fully */
  transition: width 0.3s ease, min-width 0.3s ease;
  overflow: hidden;
}

/* Ensure the ResizableBox itself doesn't interfere with collapsed state */
.app-container.right-collapsed .right-panel-resizable-container {
  width: 60px !important;
  min-width: 60px !important;
}

/* Custom scrollbar styles (optional) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Styling for react-resizable handle */
.custom-resize-handle {
  position: absolute;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  background: #eee; /* Light background for visibility */
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  top: 0;
  z-index: 10; /* Ensure handle is clickable */
  box-sizing: border-box;
}

.custom-resize-handle-w {
  left: -5px; /* Position handle slightly outside the box for easier grabbing */
}

.custom-resize-handle:hover {
   background: #ddd; /* Darken on hover */
}

/* Ensure react-resizable box itself doesn't add extra borders/padding */
.react-resizable {
  position: relative;
  /* Add flex-shrink: 0 to prevent the resizable box itself from shrinking */
  flex-shrink: 0;
}
.react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  background-image: none; /* Hide default handle image if using custom */
  padding: 0; /* Reset padding */
}

a {
  color: inherit;
  text-decoration: none;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Ensure root layout fills viewport */
#__next {
  height: 100%;
}
