# Stage 1: Builder (still useful for node_modules caching)
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
# Copy package.json and package-lock.json (or yarn.lock)
COPY package.json package-lock.json ./
# NPM ci is generally recommended for CI/production builds
# It installs dependencies based on package-lock.json and is faster.
RUN npm ci --legacy-peer-deps

# Copy the rest of the application source code
# For a dev server, this will be the state of the code when the image is built.
# For live changes, you'd typically use a volume mount when running the container.
COPY . .

# No build step for dev
# RUN npm run build

# Stage 2: Runner / Development Server
FROM node:20-alpine
# Using builder's node_modules and source code for consistency, 
# though for dev, direct source mount is more common.

WORKDIR /app

COPY --from=builder /app /app

# Set environment to development
ENV NODE_ENV=development

# Expose the port Next.js dev server runs on (usually 3000)
EXPOSE 3000

# Next.js collects anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry.
# ENV NEXT_TELEMETRY_DISABLED 1

# Run the development server
# Your package.json has: "dev": "next dev --turbopack"
CMD ["npm", "run", "dev"]
