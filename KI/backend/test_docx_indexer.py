#!/usr/bin/env python3
"""
Test script for DOCX indexing with advanced Turkish character support
"""

import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from rag_engine.documents_indexer.indexer import index_knowledge_base, search_knowledge_base, test_ollama_connection

def main():
    print("🚀 Testing DOCX indexing with advanced Turkish character support")
    print("=" * 70)
    
    # Test Ollama connection first
    print("\n1. Testing Ollama connection...")
    if not test_ollama_connection():
        print("❌ Ollama connection failed. Please check your server.")
        return
    
    # Test indexing a Turkish DOCX
    print("\n2. Testing DOCX indexing...")
    docx_files = list(Path("data").glob("*.docx"))
    
    if not docx_files:
        print("❌ No DOCX files found in data directory")
        return
    
    # Test with the first DOCX file
    docx_path = docx_files[0]
    print(f"📄 Testing with: {docx_path.name}")
    
    try:
        # Index the document
        print(f"📄 Indexing: {docx_path.name}")
        chunk_count = index_knowledge_base(docx_path, force_reindex=True)
        print(f"✅ Successfully indexed {chunk_count} chunks from DOCX")
        
        # Test search with Turkish queries
        print("\n3. Testing search with Turkish queries...")
        
        test_queries = [
            "pazarlama",
            "müşteri",
            "araştırma",
            "şirket",
            "değer yaratma",
            "yönetim"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Searching: '{query}'")
            results = search_knowledge_base(query, top_k=3)
            
            if results and results[0].get('score', 0) > 0:
                for i, result in enumerate(results[:2], 1):
                    score = result.get('score', 0)
                    content = result.get('content', '')[:100]
                    extraction_method = result.get('extraction_method', 'unknown')
                    print(f"  {i}. Score: {score:.3f} | Method: {extraction_method} | {content}...")
            else:
                print("  No relevant results found")
        
        print("\n🎉 DOCX indexing test completed successfully!")
        print("✅ Advanced Turkish character extraction is working!")
        
    except Exception as e:
        print(f"❌ Error during DOCX testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
