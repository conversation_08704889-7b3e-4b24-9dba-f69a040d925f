from agno.tools import Toolkit, tool
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from scipy import stats
import logging

logger = logging.getLogger(__name__)

class AnomalyDetectionTool(Toolkit):
    """
    Fast, tool-based anomaly detection using machine learning and statistical methods.
    Replaces agent-based approach for better performance and cost efficiency.
    """
    
    def __init__(self):
        super().__init__(
            name="AnomalyDetectionTool",
            instructions=None,
            add_instructions=False,
        )
        self.register(self.detect_anomalies_fast, name="detect_anomalies_fast")
        self.register(self.analyze_data_profile, name="analyze_data_profile")

    def detect_anomalies_fast(
        self, 
        data: List[Dict[str, Any]], 
        contamination: float = 0.1,
        methods: List[str] = None
    ) -> Dict[str, Any]:
        """
        Fast anomaly detection using multiple algorithms
        
        Args:
            data: List of dictionaries representing table rows
            contamination: Expected proportion of outliers (0.1 = 10%)
            methods: List of methods to use ['isolation_forest', 'z_score', 'iqr']
            
        Returns:
            Dictionary with anomaly detection results
        """
        try:
            if not data or len(data) == 0:
                return {
                    "success": False,
                    "error": "No data provided for anomaly detection"
                }
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            if methods is None:
                methods = ['isolation_forest', 'z_score']
            
            # Analyze data profile
            profile = self._analyze_data_profile(df)
            
            # Detect anomalies using selected methods
            anomalies = []
            method_results = {}
            
            for method in methods:
                if method == 'isolation_forest':
                    method_anomalies = self._detect_isolation_forest(df, profile, contamination)
                elif method == 'z_score':
                    method_anomalies = self._detect_z_score(df, profile)
                elif method == 'iqr':
                    method_anomalies = self._detect_iqr(df, profile)
                else:
                    continue
                
                method_results[method] = method_anomalies
                anomalies.extend(method_anomalies)
            
            # Remove duplicates and sort by score
            unique_anomalies = self._deduplicate_anomalies(anomalies)
            unique_anomalies.sort(key=lambda x: x.get('score', 0), reverse=True)
            
            # Calculate statistics
            total_rows = len(df)
            anomaly_count = len(unique_anomalies)
            anomaly_percentage = (anomaly_count / total_rows * 100) if total_rows > 0 else 0
            
            return {
                "success": True,
                "anomalies_found": anomaly_count > 0,
                "total_rows": total_rows,
                "anomaly_count": anomaly_count,
                "anomaly_percentage": round(anomaly_percentage, 2),
                "anomalies": unique_anomalies[:50],  # Limit to top 50
                "data_profile": profile,
                "methods_used": methods,
                "method_results": {k: len(v) for k, v in method_results.items()},
                "summary": f"{anomaly_count} anomali tespit edildi (%{anomaly_percentage:.1f})",
                "recommendation": "Anomalileri silmek ister misiniz?" if anomaly_count > 0 else "Anomali bulunamadı."
            }
            
        except Exception as e:
            logger.error(f"Error in anomaly detection: {str(e)}")
            return {
                "success": False,
                "error": f"Anomali tespiti sırasında hata: {str(e)}"
            }

    def analyze_data_profile(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze data profile to determine best anomaly detection strategy
        
        Args:
            data: List of dictionaries representing table rows
            
        Returns:
            Data profile information
        """
        try:
            if not data:
                return {"success": False, "error": "No data provided"}
            
            df = pd.DataFrame(data)
            profile = self._analyze_data_profile(df)
            
            return {
                "success": True,
                "profile": profile
            }
            
        except Exception as e:
            logger.error(f"Error in data profile analysis: {str(e)}")
            return {
                "success": False,
                "error": f"Veri profili analizi hatası: {str(e)}"
            }

    def _analyze_data_profile(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze DataFrame to understand data characteristics"""
        profile = {
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "numeric_columns": [],
            "categorical_columns": [],
            "datetime_columns": [],
            "missing_data": {},
            "data_types": {}
        }
        
        for col in df.columns:
            # Determine column type
            if pd.api.types.is_numeric_dtype(df[col]):
                profile["numeric_columns"].append(col)
                profile["data_types"][col] = "numeric"
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                profile["datetime_columns"].append(col)
                profile["data_types"][col] = "datetime"
            else:
                profile["categorical_columns"].append(col)
                profile["data_types"][col] = "categorical"
            
            # Missing data analysis
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                profile["missing_data"][col] = {
                    "count": int(missing_count),
                    "percentage": round(missing_count / len(df) * 100, 2)
                }
        
        return profile

    def _detect_isolation_forest(self, df: pd.DataFrame, profile: Dict, contamination: float) -> List[Dict]:
        """Detect anomalies using Isolation Forest"""
        anomalies = []
        
        numeric_cols = profile["numeric_columns"]
        if len(numeric_cols) == 0:
            return anomalies
        
        try:
            # Prepare numeric data
            numeric_data = df[numeric_cols].copy()
            
            # Handle missing values
            numeric_data = numeric_data.fillna(numeric_data.median())
            
            # Scale the data
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(numeric_data)
            
            # Apply Isolation Forest
            iso_forest = IsolationForest(
                contamination=contamination,
                random_state=42,
                n_estimators=100
            )
            
            outlier_labels = iso_forest.fit_predict(scaled_data)
            anomaly_scores = iso_forest.score_samples(scaled_data)
            
            # Extract anomalies
            for idx, (label, score) in enumerate(zip(outlier_labels, anomaly_scores)):
                if label == -1:  # Anomaly detected
                    anomalies.append({
                        "row_id": int(idx),
                        "method": "isolation_forest",
                        "score": float(abs(score)),  # Convert to positive score
                        "columns_analyzed": numeric_cols,
                        "values": {col: df.iloc[idx][col] for col in numeric_cols},
                        "reason": f"Isolation Forest anomaly (score: {score:.3f})"
                    })
                    
        except Exception as e:
            logger.warning(f"Isolation Forest detection failed: {str(e)}")
        
        return anomalies

    def _detect_z_score(self, df: pd.DataFrame, profile: Dict, threshold: float = 3.0) -> List[Dict]:
        """Detect anomalies using Z-score method"""
        anomalies = []
        
        numeric_cols = profile["numeric_columns"]
        if len(numeric_cols) == 0:
            return anomalies
        
        try:
            for col in numeric_cols:
                # Calculate Z-scores
                col_data = df[col].dropna()
                if len(col_data) < 3:  # Need at least 3 points
                    continue
                
                z_scores = np.abs(stats.zscore(col_data))
                
                # Find anomalies
                anomaly_indices = col_data.index[z_scores > threshold]
                
                for idx in anomaly_indices:
                    anomalies.append({
                        "row_id": int(idx),
                        "method": "z_score",
                        "score": float(z_scores[col_data.index.get_loc(idx)]),
                        "columns_analyzed": [col],
                        "values": {col: df.iloc[idx][col]},
                        "reason": f"Z-score anomaly in {col} (z-score: {z_scores[col_data.index.get_loc(idx)]:.2f})"
                    })
                    
        except Exception as e:
            logger.warning(f"Z-score detection failed: {str(e)}")
        
        return anomalies

    def _detect_iqr(self, df: pd.DataFrame, profile: Dict) -> List[Dict]:
        """Detect anomalies using Interquartile Range (IQR) method"""
        anomalies = []
        
        numeric_cols = profile["numeric_columns"]
        if len(numeric_cols) == 0:
            return anomalies
        
        try:
            for col in numeric_cols:
                col_data = df[col].dropna()
                if len(col_data) < 4:  # Need at least 4 points for quartiles
                    continue
                
                Q1 = col_data.quantile(0.25)
                Q3 = col_data.quantile(0.75)
                IQR = Q3 - Q1
                
                # Define outlier bounds
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # Find anomalies
                outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                
                for idx in outliers.index:
                    value = df.iloc[idx][col]
                    distance = min(abs(value - lower_bound), abs(value - upper_bound))
                    
                    anomalies.append({
                        "row_id": int(idx),
                        "method": "iqr",
                        "score": float(distance / IQR) if IQR > 0 else 1.0,
                        "columns_analyzed": [col],
                        "values": {col: value},
                        "reason": f"IQR anomaly in {col} (value: {value}, bounds: [{lower_bound:.2f}, {upper_bound:.2f}])"
                    })
                    
        except Exception as e:
            logger.warning(f"IQR detection failed: {str(e)}")
        
        return anomalies

    def _deduplicate_anomalies(self, anomalies: List[Dict]) -> List[Dict]:
        """Remove duplicate anomalies (same row detected by multiple methods)"""
        seen_rows = {}
        unique_anomalies = []
        
        for anomaly in anomalies:
            row_id = anomaly["row_id"]
            
            if row_id not in seen_rows:
                seen_rows[row_id] = anomaly
                unique_anomalies.append(anomaly)
            else:
                # Merge methods and keep highest score
                existing = seen_rows[row_id]
                if anomaly["score"] > existing["score"]:
                    existing["score"] = anomaly["score"]
                    existing["reason"] = f"{existing['reason']} + {anomaly['reason']}"
                
                # Merge methods
                methods = existing.get("methods", [existing["method"]])
                if anomaly["method"] not in methods:
                    methods.append(anomaly["method"])
                existing["methods"] = methods
        
        return unique_anomalies
