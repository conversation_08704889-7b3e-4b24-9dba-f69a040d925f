# free_indexer.py  – single-file demo, Python ≥3.9
# run with:  python free_indexer.py
import os, io, re, uuid, json, hashlib, logging, base64, tempfile, requests
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Sequence, Optional
from io import BytesIO
from datetime import datetime
import mammoth
from docx import Document
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


# ─── constants (from environment variables) ───────────────────────────────────
COLLECTION_BASE = "knowledge_base"  # Changed to match <PERSON>lama_indexer.py
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://***************:11434")
OLLAMA_EMBEDDING_MODEL = os.getenv("OLLAMA_EMBEDDING_MODEL", "nomic-embed-text")
CHUNK_CHARS     = int(os.getenv("MAX_CHUNK_SIZE", "800"))
OVERLAP         = int(os.getenv("OVERLAP_SIZE", "120"))

QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

# ─── logging ──────────────────────────────────────────────────────────────────
logging.basicConfig(level=logging.INFO, format="%(asctime)s | %(levelname)s | %(message)s")
L = logging.getLogger("indexer")

# ─── deps ─────────────────────────────────────────────────────────────────────
from pdfminer.high_level import extract_text                 # text
import fitz                                                   # images
import pdfplumber                                             # tables
from langchain.text_splitter import RecursiveCharacterTextSplitter
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams
from qdrant_client.http.models import PointStruct
import cv2
import numpy as np
from PIL import Image
import pytesseract


# ─── data model ───────────────────────────────────────────────────────────────
@dataclass(slots=True)
class Element:
    uid: str
    page: int
    type: str                # text / table / image
    content: str             # raw text OR markdown snippet OR "embedded image"
    meta: Dict[str, Any]
    user_id: str = "general" # User ID (general by default)
    file_hash: str = ""      # SHA-256 hash of the source file for duplicate detection

    def payload(self):
        d = asdict(self)
        m = d.pop("meta")
        d.update(m)
        return d

# ─── text normalization ──────────────────────────────────────────────────────
def normalize_turkish_text(text: str) -> str:
    """
    Normalize Turkish text to fix common encoding issues and improve embedding quality.

    Args:
        text: Raw text that may contain encoding issues

    Returns:
        Normalized text with proper Turkish characters
    """
    if not text:
        return text

    # Common Turkish character fixes for encoding issues
    replacements = {
        # Fix common encoding issues
        'Ã¼': 'ü', 'Ã¶': 'ö', 'Ã§': 'ç', 'Ä±': 'ı', 'Ä°': 'İ', 'Åž': 'ş', 'Ä': 'ğ',
        'Ãœ': 'Ü', 'Ã–': 'Ö', 'Ã‡': 'Ç', 'Åž': 'Ş', 'ÄŸ': 'ğ', 'Äž': 'Ğ',
        # Fix other common issues
        'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€"': '-', 'â€"': '—',
        # Remove or replace problematic characters
        '\ufeff': '',  # BOM
        '\u200b': '',  # Zero-width space
        '\u00a0': ' ', # Non-breaking space
    }

    # Apply replacements
    for old, new in replacements.items():
        text = text.replace(old, new)

    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    # Ensure proper UTF-8 encoding
    try:
        # Try to encode/decode to catch any remaining issues
        text = text.encode('utf-8').decode('utf-8')
    except UnicodeError:
        # If there are still issues, use error handling
        text = text.encode('utf-8', errors='replace').decode('utf-8')

    return text

# ─── advanced text extraction ─────────────────────────────────────────────────
def extract_text_with_ocr(page, page_num: int) -> str:
    """
    Extract text using OCR for better Turkish character recognition.
    This is used as a fallback when regular text extraction fails.
    """
    try:
        # Get page as image
        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
        img_data = pix.tobytes("png")

        # Convert to PIL Image
        img = Image.open(io.BytesIO(img_data))

        # Convert to numpy array for OpenCV processing
        img_array = np.array(img)

        # Image preprocessing for better OCR
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

        # Apply threshold to get better contrast
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Noise removal
        kernel = np.ones((1, 1), np.uint8)
        thresh = cv2.dilate(thresh, kernel, iterations=1)
        thresh = cv2.erode(thresh, kernel, iterations=1)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # Convert back to PIL Image
        processed_img = Image.fromarray(thresh)

        # OCR with Turkish language support
        ocr_text = pytesseract.image_to_string(
            processed_img,
            lang='tur+eng',  # Turkish + English
            config='--oem 3 --psm 6'  # OCR Engine Mode 3, Page Segmentation Mode 6
        )

        return ocr_text.strip()

    except Exception as e:
        L.warning(f"OCR failed for page {page_num}: {e}")
        return ""

def extract_text_pymupdf_advanced(page, page_num: int) -> str:
    """
    Advanced text extraction using PyMuPDF with multiple methods.
    """
    texts = []

    # Method 1: Standard text extraction
    try:
        text1 = page.get_text()
        if text1.strip():
            texts.append(("standard", text1))
    except Exception as e:
        L.warning(f"Standard text extraction failed for page {page_num}: {e}")

    # Method 2: Text extraction with layout preservation
    try:
        text2 = page.get_text("text", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
        if text2.strip():
            texts.append(("layout", text2))
    except Exception as e:
        L.warning(f"Layout text extraction failed for page {page_num}: {e}")

    # Method 3: Dictionary-based extraction (more detailed)
    try:
        text_dict = page.get_text("dict")
        dict_text = ""
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line.get("spans", []):
                        dict_text += span.get("text", "") + " "
                    dict_text += "\n"
        if dict_text.strip():
            texts.append(("dict", dict_text))
    except Exception as e:
        L.warning(f"Dictionary text extraction failed for page {page_num}: {e}")

    # Choose the best extraction result
    if texts:
        # Prefer the longest text that contains Turkish characters
        best_text = ""
        best_score = 0

        for method, text in texts:
            # Score based on length and Turkish character presence
            turkish_chars = sum(1 for c in text if c in 'çğıöşüÇĞIİÖŞÜ')
            score = len(text) + (turkish_chars * 10)  # Bonus for Turkish chars

            if score > best_score:
                best_score = score
                best_text = text
                L.debug(f"Page {page_num}: Best method is {method} (score: {score})")

        return best_text

    return ""

def extract_text_pdfplumber_advanced(page) -> str:
    """
    Advanced text extraction using pdfplumber with better Turkish support.
    """
    try:
        # Try multiple extraction strategies
        strategies = [
            # Strategy 1: Default with better tolerance
            {
                'x_tolerance': 2,
                'y_tolerance': 2,
                'layout': True,
                'x_density': 7.25,
                'y_density': 13
            },
            # Strategy 2: More aggressive tolerance
            {
                'x_tolerance': 3,
                'y_tolerance': 3,
                'layout': False
            },
            # Strategy 3: Character-level extraction
            {
                'x_tolerance': 1,
                'y_tolerance': 1,
                'layout': True,
                'use_text_flow': True
            }
        ]

        best_text = ""
        best_score = 0

        for i, strategy in enumerate(strategies):
            try:
                text = page.extract_text(**strategy) or ""
                if text.strip():
                    # Score based on length and Turkish character presence
                    turkish_chars = sum(1 for c in text if c in 'çğıöşüÇĞIİÖŞÜ')
                    score = len(text) + (turkish_chars * 10)

                    if score > best_score:
                        best_score = score
                        best_text = text
                        L.debug(f"PDFPlumber strategy {i+1} scored {score}")

            except Exception as e:
                L.debug(f"PDFPlumber strategy {i+1} failed: {e}")
                continue

        return best_text

    except Exception as e:
        L.warning(f"Advanced pdfplumber extraction failed: {e}")
        return ""

# ─── ollama embedder ──────────────────────────────────────────────────────────
class OllamaEmbedder:
    """
    Ollama API client for generating embeddings using remote Ollama server.
    """

    def __init__(self, base_url: str, model: str):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.session = requests.Session()
        self._embedding_dim = None  # Will be determined from first embedding

    def encode(self, texts: List[str], normalize_embeddings: bool = True, show_progress_bar: bool = False) -> List[List[float]]:
        """
        Generate embeddings for a list of texts using Ollama API.

        Args:
            texts: List of texts to embed
            normalize_embeddings: Whether to normalize embeddings (ignored for compatibility)
            show_progress_bar: Whether to show progress bar (ignored for compatibility)

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        embeddings = []

        for i, text in enumerate(texts):
            if show_progress_bar and i % 10 == 0:
                L.info(f"Embedding progress: {i}/{len(texts)}")

            try:
                # Make request to Ollama embeddings API
                response = self.session.post(
                    f"{self.base_url}/api/embeddings",
                    json={
                        "model": self.model,
                        "prompt": text
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    embedding = result.get("embedding", [])

                    if embedding:
                        # Set embedding dimension from first successful embedding
                        if self._embedding_dim is None:
                            self._embedding_dim = len(embedding)
                            L.info(f"Detected embedding dimension: {self._embedding_dim}")
                        embeddings.append(embedding)
                    else:
                        L.error(f"No embedding returned for text: {text[:50]}...")
                        # Use zero vector as fallback with detected or default dimension
                        dim = self._embedding_dim or 768
                        embeddings.append([0.0] * dim)
                else:
                    L.error(f"Ollama API error {response.status_code}: {response.text}")
                    # Use zero vector as fallback with detected or default dimension
                    dim = self._embedding_dim or 768
                    embeddings.append([0.0] * dim)

            except requests.exceptions.RequestException as e:
                L.error(f"Request failed for text embedding: {e}")
                # Use zero vector as fallback with detected or default dimension
                dim = self._embedding_dim or 768
                embeddings.append([0.0] * dim)
            except Exception as e:
                L.error(f"Unexpected error during embedding: {e}")
                # Use zero vector as fallback with detected or default dimension
                dim = self._embedding_dim or 768
                embeddings.append([0.0] * dim)

        return embeddings

# ─── duplicate detection ──────────────────────────────────────────────────────
def get_file_hash(file_path: Path) -> str:
    """
    Calculate SHA-256 hash of file content for duplicate detection.

    Args:
        file_path: Path to the file

    Returns:
        SHA-256 hash as hexadecimal string
    """
    hash_sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_sha256.update(chunk)
    return hash_sha256.hexdigest()

def is_file_already_indexed(file_path: Path, user_id: Optional[str] = None) -> bool:
    """
    Check if file is already indexed using hash and metadata.

    Args:
        file_path: Path to the file to check
        user_id: Optional user ID for user-specific knowledge

    Returns:
        True if file is already indexed, False otherwise
    """
    try:
        file_hash = get_file_hash(file_path)
        return is_file_already_indexed_by_hash(file_hash, file_path.name, user_id)
    except Exception as e:
        L.warning(f"Error checking for duplicate file {file_path}: {str(e)}")
        return False

def is_file_already_indexed_by_hash(file_hash: str, filename: str, user_id: Optional[str] = None) -> bool:
    """
    Check if file is already indexed using provided hash and filename.

    Args:
        file_hash: SHA-256 hash of the file content
        filename: Original filename for identification
        user_id: Optional user ID for user-specific knowledge

    Returns:
        True if file is already indexed, False otherwise
    """
    try:
        coll = get_collection_name(user_id)
        L.info(f"Checking collection: {coll}")

        # Check if collection exists
        collections = [c.name for c in q.get_collections().collections]
        if coll not in collections:
            L.info(f"Collection {coll} does not exist")
            return False

        # Search for existing file with same hash using search instead of scroll
        from qdrant_client.http.models import Filter, FieldCondition, MatchValue

        search_filter = Filter(
            must=[
                FieldCondition(key="file_hash", match=MatchValue(value=file_hash)),
                FieldCondition(key="source", match=MatchValue(value=filename))
            ]
        )

        L.info(f"Searching with filter: hash={file_hash[:16]}..., source={filename}")

        # Use search with a dummy vector to check for existence
        dummy_vector = [0.0] * 384  # Assuming 384-dimensional embeddings
        results = q.search(
            collection_name=coll,
            query_vector=dummy_vector,
            query_filter=search_filter,
            limit=1
        )

        L.info(f"Search results count: {len(results)}")
        if len(results) > 0:
            L.info(f"Found existing record: hash={results[0].payload.get('file_hash', 'N/A')[:16]}..., source={results[0].payload.get('source', 'N/A')}")
        else:
            # Let's also try searching just by hash to see if there's a filename mismatch
            hash_only_filter = Filter(
                must=[
                    FieldCondition(key="file_hash", match=MatchValue(value=file_hash))
                ]
            )
            hash_results = q.search(
                collection_name=coll,
                query_vector=dummy_vector,
                query_filter=hash_only_filter,
                limit=5
            )
            L.info(f"Hash-only search results count: {len(hash_results)}")
            for i, result in enumerate(hash_results):
                L.info(f"  Result {i}: hash={result.payload.get('file_hash', 'N/A')[:16]}..., source={result.payload.get('source', 'N/A')}")

        return len(results) > 0
    except Exception as e:
        L.warning(f"Error checking for duplicate file hash {file_hash[:16]}...: {str(e)}")
        return False

# ─── parsers ──────────────────────────────────────────────────────────────────
def parse_pdf(path: Path, file_hash: str = "", original_filename: str = "") -> List[Element]:

    def _clean(row):
        # tüm hücreleri stringle ve None'ları boş string yap
        return ["" if cell is None else str(cell) for cell in row]

    # Use original filename if provided, otherwise use PDF filename
    source_name = original_filename if original_filename else path.name

    L.info("Parsing %s", path.name)
    elements: List[Element] = []
    # 1) pull images first (fast, independent of layout)
    with fitz.open(path) as doc:
        for pno in range(doc.page_count):
            for i, (xref, *_rest) in enumerate(doc.get_page_images(pno)):
                pix = fitz.Pixmap(doc, xref)
                if pix.n > 4:
                    pix = fitz.Pixmap(fitz.csRGB, pix)
                b64 = base64.b64encode(pix.tobytes("png")).decode()
                elements.append(Element(
                    uid=str(uuid.uuid4()),
                    page=pno+1,
                    type="image",
                    content="embedded_image",
                    meta={"source": source_name, "image_b64": b64, "index": i},
                    file_hash=file_hash
                ))
    # 2) text & tables with advanced extraction
    with pdfplumber.open(path) as pdf:
        with fitz.open(path) as fitz_doc:
            for pno, page in enumerate(pdf.pages, start=1):
                fitz_page = fitz_doc[pno-1]  # fitz uses 0-based indexing

                # Try multiple extraction methods for better Turkish character support
                text_candidates = []

                # Method 1: Advanced pdfplumber extraction
                try:
                    pdfplumber_text = extract_text_pdfplumber_advanced(page)
                    if pdfplumber_text.strip():
                        text_candidates.append(("pdfplumber_advanced", pdfplumber_text))
                except Exception as e:
                    L.debug(f"Advanced pdfplumber failed for page {pno}: {e}")

                # Method 2: Advanced PyMuPDF extraction
                try:
                    pymupdf_text = extract_text_pymupdf_advanced(fitz_page, pno)
                    if pymupdf_text.strip():
                        text_candidates.append(("pymupdf_advanced", pymupdf_text))
                except Exception as e:
                    L.debug(f"Advanced PyMuPDF failed for page {pno}: {e}")

                # Method 3: PDFMiner fallback
                try:
                    pdfminer_text = extract_text(path, page_numbers=[pno-1])
                    if pdfminer_text and pdfminer_text.strip():
                        text_candidates.append(("pdfminer", pdfminer_text))
                except Exception as e:
                    L.debug(f"PDFMiner failed for page {pno}: {e}")

                # Method 4: OCR as last resort (only if other methods fail badly)
                if not text_candidates or all(len(text) < 50 for _, text in text_candidates):
                    try:
                        L.info(f"Using OCR for page {pno} due to poor text extraction")
                        ocr_text = extract_text_with_ocr(fitz_page, pno)
                        if ocr_text.strip():
                            text_candidates.append(("ocr", ocr_text))
                    except Exception as e:
                        L.debug(f"OCR failed for page {pno}: {e}")

                # Choose the best text extraction result
                best_text = ""
                if text_candidates:
                    # Score each candidate
                    best_score = 0
                    best_method = ""

                    for method, text in text_candidates:
                        # Scoring criteria:
                        # 1. Length (longer is usually better)
                        # 2. Turkish character presence (bonus)
                        # 3. Reasonable character distribution

                        turkish_chars = sum(1 for c in text if c in 'çğıöşüÇĞIİÖŞÜ')
                        total_chars = len(text)

                        # Check for reasonable character distribution (not too many special chars)
                        special_chars = sum(1 for c in text if not c.isalnum() and c not in ' \n\t.,!?;:-()[]{}"\'/\\')
                        special_ratio = special_chars / max(total_chars, 1)

                        # Calculate score
                        length_score = total_chars
                        turkish_bonus = turkish_chars * 15  # Higher bonus for Turkish chars
                        special_penalty = special_ratio * 100  # Penalty for too many special chars

                        score = length_score + turkish_bonus - special_penalty

                        L.debug(f"Page {pno} - {method}: score={score:.1f} (len={total_chars}, tr={turkish_chars}, special={special_ratio:.2f})")

                        if score > best_score:
                            best_score = score
                            best_text = text
                            best_method = method

                    L.info(f"Page {pno}: Using {best_method} extraction (score: {best_score:.1f})")

                # Normalize and clean Turkish text
                if best_text.strip():
                    # Normalize Turkish characters and fix common encoding issues
                    normalized_text = normalize_turkish_text(best_text)
                    elements.append(Element(
                        uid=str(uuid.uuid4()), page=pno, type="text",
                        content=normalized_text, meta={"source": source_name, "extraction_method": best_method},
                        file_hash=file_hash
                    ))
            # tables → markdown
            for tidx, table in enumerate(page.extract_tables()):
                header = _clean(table[0])
                md  = "| " + " | ".join(header) + " |\n"
                md += "|" + "|".join(["---"] * len(header)) + "|\n"

                for row in table[1:]:
                    cells = _clean(row)
                    # eksik hücre varsa satırı başlık uzunluğuna pad'le
                    cells += [""] * (len(header) - len(cells))
                    md += "| " + " | ".join(cells[:len(header)]) + " |\n"

                elements.append(Element(
                    uid=str(uuid.uuid4()), page=pno, type="table",
                    content=md, meta={"source": source_name, "tindex": tidx},
                    file_hash=file_hash
                ))
    return elements

# ─── chunk + embed ────────────────────────────────────────────────────────────
splitter = RecursiveCharacterTextSplitter(
    chunk_size=CHUNK_CHARS, chunk_overlap=OVERLAP,
    separators=["\n\n", "\n", ".", " ", ""]  # coarse→fine
)
# Use Ollama embedder instead of SentenceTransformer
embedder = OllamaEmbedder(OLLAMA_BASE_URL, OLLAMA_EMBEDDING_MODEL)

def expand_chunks(elems: List[Element]) -> tuple[List[Element], List[List[float]]]:
    new, corpus = [], []
    for e in elems:
        if e.type == "text" and len(e.content) > CHUNK_CHARS:
            parts = splitter.split_text(e.content)
        else:
            parts = [e.content]
        for part in parts:
            # Normalize Turkish text before embedding
            normalized_part = normalize_turkish_text(part) if e.type == "text" else part

            new.append(Element(
                uid=str(uuid.uuid4()),
                page=e.page,
                type=e.type,
                content=normalized_part,
                meta=e.meta,
                user_id=e.user_id,
                file_hash=e.file_hash
            ))
            corpus.append(normalized_part)

    L.info("Embedding %d chunks with Ollama model %s at %s", len(corpus), OLLAMA_EMBEDDING_MODEL, OLLAMA_BASE_URL)

    # Ensure all text is properly encoded before embedding
    clean_corpus = []
    for text in corpus:
        try:
            # Ensure proper UTF-8 encoding
            clean_text = text.encode('utf-8').decode('utf-8')
            clean_corpus.append(clean_text)
        except UnicodeError:
            # Handle any remaining encoding issues
            clean_text = text.encode('utf-8', errors='replace').decode('utf-8')
            clean_corpus.append(clean_text)
            L.warning(f"Fixed encoding issue in text chunk: {text[:50]}...")

    vecs = embedder.encode(clean_corpus, normalize_embeddings=True, show_progress_bar=False)
    return new, vecs

# ─── qdrant ───────────────────────────────────────────────────────────────────
q = QdrantClient(host=QDRANT_HOST, prefer_grpc=True, port=QDRANT_PORT, timeout=150)

def get_collection_name(user_id: Optional[str] = None) -> str:
    """Get collection name based on whether this is general or user-specific knowledge"""
    if user_id:
        return f"{COLLECTION_BASE}_{user_id}"
    return COLLECTION_BASE

def ensure_collection(name: str, dim: int):
    if name not in [c.name for c in q.get_collections().collections]:
        L.info(f"Creating new collection: {name}")
        q.create_collection(name, vectors_config=VectorParams(size=dim, distance=Distance.COSINE))
        # Create payload indexes for better filtering
        q.create_payload_index(name, field_name="user_id", field_schema="keyword")
        q.create_payload_index(name, field_name="type", field_schema="keyword")
        q.create_payload_index(name, field_name="source", field_schema="text")
        q.create_payload_index(name, field_name="file_hash", field_schema="keyword")

def upsert(elements: List[Element], vectors, user_id: Optional[str] = None, batch: int = 128):
    """
    Qdrant'a verileri parça parça (batch) aktarır.
    batch: tek seferde gönderilecek nokta sayısı
    """
    if not elements:
        return
        
    # If user_id is provided, set it for all elements
    if user_id:
        for e in elements:
            e.user_id = user_id
    
    # Get appropriate collection name
    coll = get_collection_name(user_id)
    ensure_collection(coll, len(vectors[0]))

    total = len(elements)
    for i in range(0, total, batch):
        slice_e = elements[i:i + batch]
        slice_v = vectors[i:i + batch]

        points = [
            PointStruct(
                id=str(uuid.uuid4()),
                vector=v if isinstance(v, list) else v.tolist(),  # Handle both list and numpy array
                payload=e.payload()
            )
            for e, v in zip(slice_e, slice_v)
        ]
        q.upsert(coll, points=points, wait=True)

        L.info("🔼  Upserted %d / %d", i + len(points), total)


def extract_text_from_docx_advanced(docx_bytes: bytes) -> str:
    """
    Advanced text extraction from DOCX with proper Turkish character support.

    Args:
        docx_bytes: DOCX file content as bytes

    Returns:
        Extracted text with proper Turkish character handling
    """
    try:
        # Create a BytesIO object for the DOCX content
        docx_stream = BytesIO(docx_bytes)

        # Method 1: Use python-docx for structured extraction
        doc = Document(docx_stream)
        docx_text = ""

        # Extract paragraphs with proper encoding
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                # Ensure proper UTF-8 handling
                para_text = paragraph.text
                # Normalize Turkish characters
                para_text = normalize_turkish_text(para_text)
                docx_text += para_text + "\n"

        # Extract tables
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    if cell_text:
                        cell_text = normalize_turkish_text(cell_text)
                        row_text.append(cell_text)
                if row_text:
                    docx_text += " | ".join(row_text) + "\n"

        # Method 2: Use mammoth as fallback for better formatting
        if not docx_text.strip() or len(docx_text) < 100:
            try:
                docx_stream.seek(0)  # Reset stream position
                result = mammoth.extract_raw_text(docx_stream)
                mammoth_text = result.value
                if mammoth_text and len(mammoth_text) > len(docx_text):
                    docx_text = normalize_turkish_text(mammoth_text)
                    L.info("Used mammoth for DOCX text extraction")
            except Exception as e:
                L.warning(f"Mammoth extraction failed: {e}")

        docx_stream.close()
        return docx_text.strip()

    except Exception as e:
        L.error(f"Advanced DOCX text extraction failed: {e}")
        return ""

def convert_docx_to_pdf(docx_bytes: bytes) -> bytes:
    """
    Convert DOCX file to PDF with proper Turkish character support.

    Args:
        docx_bytes: DOCX file content as bytes

    Returns:
        PDF file content as bytes
    """
    # First extract text using advanced method
    text_content = extract_text_from_docx_advanced(docx_bytes)

    if not text_content.strip():
        L.warning("No text content extracted from DOCX")
        # Create empty PDF
        pdf_stream = BytesIO()
        c = canvas.Canvas(pdf_stream, pagesize=letter)
        c.drawString(50, 750, "No text content found in document")
        c.save()
        return pdf_stream.getvalue()

    # Create a BytesIO object for the PDF
    pdf_stream = BytesIO()

    # Create PDF canvas
    c = canvas.Canvas(pdf_stream, pagesize=letter)

    # Use a font that has better Turkish character support
    try:
        # Try Times-Roman which has better Unicode support than Helvetica
        c.setFont("Times-Roman", 12)
    except:
        try:
            # Fallback to Helvetica
            c.setFont("Helvetica", 12)
        except:
            # Last resort - use default font
            c.setFont("Helvetica", 12)
    
    # Write extracted text to PDF
    y = 750  # Starting y position

    # Split text into paragraphs
    paragraphs = text_content.split('\n')

    for paragraph in paragraphs:
        if not paragraph.strip():
            y -= 10  # Small space for empty lines
            continue
        
        # Split text into lines that fit the page width
        words = paragraph.split()
        lines = []
        current_line = []
        
        for word in words:
            current_line.append(word)
            # Use stringWidth with the current font to measure text width
            if c.stringWidth(' '.join(current_line), c._fontname, c._fontsize) > 500:
                if len(current_line) > 1:
                    lines.append(' '.join(current_line[:-1]))
                    current_line = [word]
                else:
                    lines.append(' '.join(current_line))
                    current_line = []
        
        if current_line:
            lines.append(' '.join(current_line))
        
        # Write lines to PDF
        for line in lines:
            # Normalize Turkish text before writing
            line = normalize_turkish_text(line)

            # Ensure proper encoding for Turkish characters
            try:
                # First try with direct Unicode
                c.drawString(50, y, line)
            except UnicodeEncodeError:
                try:
                    # If that fails, try with UTF-8 encoding
                    encoded_line = line.encode('utf-8').decode('utf-8')
                    c.drawString(50, y, encoded_line)
                except:
                    # If all else fails, replace problematic characters but preserve Turkish chars
                    safe_line = line.encode('utf-8', 'replace').decode('utf-8')
                    c.drawString(50, y, safe_line)
            
            y -= 20
            if y < 50:  # New page if we're near the bottom
                c.showPage()
                y = 750
                c.setFont("Helvetica-Bold", 12)
    
    # Save the PDF
    c.save()
    
    # Get the PDF bytes
    pdf_bytes = pdf_stream.getvalue()
    pdf_stream.close()

    
    return pdf_bytes

def save_pdf_to_path(pdf_bytes: bytes, target_path: Path) -> str:
    """
    Save PDF bytes to the specified path.

    Args:
        pdf_bytes: PDF file content as bytes
        target_path: Target path where to save the PDF

    Returns:
        Path to the saved file as string
    """
    # Ensure parent directory exists
    target_path.parent.mkdir(parents=True, exist_ok=True)

    # Save PDF file
    with open(target_path, "wb") as f:
        f.write(pdf_bytes)

    return str(target_path)

# ─── INTERFACE FUNCTIONS ───────────────────────────────────────────────────────
def index_knowledge_base(file_path: Path, user_id: Optional[str] = None, force_reindex: bool = False) -> int:
    """
    Index a document in the knowledge base (general or user-specific).
    Supports PDF, DOCX, and DOC files. DOCX/DOC files are converted to PDF first.
    Includes duplicate detection to prevent reindexing the same file.

    Args:
        file_path: Path to the document file (PDF, DOCX, or DOC)
        user_id: Optional user ID for user-specific knowledge
        force_reindex: Force reindexing even if file already exists

    Returns:
        Number of indexed chunks (0 if already indexed and not forced)
    """
    # Ensure file_path is a Path object
    if isinstance(file_path, str):
        file_path = Path(file_path)

    # Log based on whether this is user-specific or general
    if user_id:
        L.info(f"Indexing document for user {user_id}: {file_path}")
    else:
        L.info(f"Indexing document for general knowledge base: {file_path}")

    # Check file extension and handle accordingly
    file_extension = file_path.suffix.lower()
    pdf_path = file_path

    # Calculate file hash BEFORE conversion for DOCX/DOC files (use original file)
    # For PDF files, use the PDF itself
    if file_extension in ['.docx', '.doc']:
        # For DOCX/DOC files, use original file hash for duplicate detection
        file_hash = get_file_hash(file_path)
        L.debug(f"File hash for original {file_path.name}: {file_hash[:16]}...")

        # Check for duplicates BEFORE conversion
        L.info(f"Checking for duplicates: hash={file_hash[:16]}..., filename={file_path.name}, user_id={user_id}")
        is_duplicate = is_file_already_indexed_by_hash(file_hash, file_path.name, user_id)
        L.info(f"Duplicate check result: {is_duplicate}")

        if not force_reindex and is_duplicate:
            L.info(f"File already indexed, skipping: {file_path.name}")
            return 0

        # Convert to PDF
        L.info(f"Converting {file_extension} to PDF: {file_path.name}")
        try:
            # Read the DOCX/DOC file
            with open(file_path, 'rb') as f:
                docx_bytes = f.read()

            # Convert to PDF
            pdf_bytes = convert_docx_to_pdf(docx_bytes)

            # Create PDF path in the same directory with .pdf extension
            pdf_path = file_path.parent / (file_path.stem + "_converted.pdf")

            # Save PDF to disk
            save_pdf_to_path(pdf_bytes, pdf_path)
            L.info(f"Successfully converted to PDF: {pdf_path}")

        except Exception as e:
            L.error(f"Failed to convert {file_path} to PDF: {str(e)}")
            return 0

    elif file_extension == '.pdf':
        # For PDF files, use PDF hash for duplicate detection
        file_hash = get_file_hash(pdf_path)
        L.debug(f"File hash for {pdf_path.name}: {file_hash[:16]}...")

        # Check for duplicates
        L.info(f"Checking for duplicates: hash={file_hash[:16]}..., filename={file_path.name}, user_id={user_id}")
        is_duplicate = is_file_already_indexed_by_hash(file_hash, file_path.name, user_id)
        L.info(f"Duplicate check result: {is_duplicate}")

        if not force_reindex and is_duplicate:
            L.info(f"File already indexed, skipping: {file_path.name}")
            return 0
    else:
        # Unsupported file format
        L.warning(f"Unsupported file format: {file_extension}. Only PDF, DOCX, and DOC files are supported.")
        return 0

    # Parse the PDF document with file hash and original filename
    elements = parse_pdf(pdf_path, file_hash, file_path.name)
    if not elements:
        L.warning(f"No extractable content in {pdf_path}")
        return 0

    # Get chunks and embeddings
    chunks, vecs = expand_chunks(elements)

    # Upsert to appropriate collection
    upsert(chunks, vecs, user_id)

    # Clean up converted PDF if it was created
    if file_extension in ['.docx', '.doc'] and pdf_path != file_path:
        try:
            pdf_path.unlink()  # Delete the temporary PDF file
            L.info(f"Cleaned up temporary PDF: {pdf_path}")
        except Exception as e:
            L.warning(f"Could not clean up temporary PDF {pdf_path}: {str(e)}")

    return len(chunks)

def search_knowledge_base(query: str, user_id: Optional[str] = None,
                          block_type: Optional[str] = None, top_k: int = 5, score_threshold: float = 0.60) -> List[Dict]:
    """
    Search the knowledge base for content matching the query

    Args:
        query: The search query
        user_id: Optional user ID to search in user-specific knowledge
        block_type: Optional content type filter (e.g., "text", "table", "image")
        top_k: Number of results to return

    Returns:
        List of matching document chunks with metadata
    """
    # Normalize Turkish characters in query before embedding
    normalized_query = normalize_turkish_text(query)

    # Encode the normalized query using Ollama
    vec = embedder.encode([normalized_query])[0]
    
    # Determine collection to search
    coll = get_collection_name(user_id)
    
    # Build filter conditions if needed
    filter_conditions = []
    
    # Add block type filter if specified
    if block_type:
        filter_conditions.append({"key": "type", "match": {"value": block_type}})
    
    # Create filter dict if we have conditions
    search_filter = {"must": filter_conditions} if filter_conditions else None
    
    # Log search details
    if user_id:
        L.info(f"Searching in user ({user_id}) knowledge base: '{query}'")
    else:
        L.info(f"Searching in general knowledge base: '{query}'")
    
    # Execute search
    try:
        hits = q.search(
            collection_name=coll,
            query_vector=vec if isinstance(vec, list) else vec.tolist(),
            query_filter=search_filter,
            limit=top_k,
            score_threshold=score_threshold
        )
        
        # Convert to list of dicts for easier consumption
        results = []
        for h in hits:
            payload = h.payload
            snippet = (payload.get("content") or "")[:120].replace("\n", " ")
            L.info(f"→ p{payload.get('page', 0):03d} {payload.get('type', ''):8s} | {h.score:.3f} | {snippet}…")
            
            # Add score to payload and add to results
            payload['score'] = float(h.score)
            results.append(payload)

        if len(results) == 0:
            results.append({
                "content": "Bu konuda bilgi bulunamadı.",
                "score": 0.0
            })
            
        return results
    except Exception as e:
        L.error(f"Search error: {str(e)}")
        return []

# ─── TEST FUNCTIONS ────────────────────────────────────────────────────────────
def test_ollama_connection():
    """Test connection to Ollama server and embedding functionality."""
    try:
        L.info(f"Testing Ollama connection to {OLLAMA_BASE_URL}")

        # Test with a simple Turkish text
        test_embedder = OllamaEmbedder(OLLAMA_BASE_URL, OLLAMA_EMBEDDING_MODEL)
        test_texts = ["Merhaba dünya", "Türkçe karakterler: ğüşıöç"]

        embeddings = test_embedder.encode(test_texts)

        if embeddings and len(embeddings) == 2:
            L.info(f"✅ Ollama connection successful!")
            L.info(f"✅ Embedding dimension: {len(embeddings[0])}")
            L.info(f"✅ Turkish characters handled correctly")
            return True
        else:
            L.error("❌ Failed to get embeddings from Ollama")
            return False

    except Exception as e:
        L.error(f"❌ Ollama connection test failed: {e}")
        return False

if __name__ == "__main__":
    # Test Ollama connection
    if test_ollama_connection():
        print("🎉 Ollama embedder is ready to use!")
    else:
        print("❌ Please check your Ollama server and configuration.")