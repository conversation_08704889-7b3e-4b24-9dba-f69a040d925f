import React from 'react';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Tooltip from '@mui/material/Tooltip';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import CircularProgress from '@mui/material/CircularProgress';
import ErrorIcon from '@mui/icons-material/Error';

interface WebSocketStatusProps {
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  size?: 'small' | 'medium';
}

const WebSocketStatus: React.FC<WebSocketStatusProps> = ({ 
  isConnected, 
  connectionStatus, 
  size = 'small' 
}) => {
  const getStatusInfo = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          label: 'Bağlı',
          color: 'success' as const,
          icon: <WifiIcon fontSize={size} />,
          tooltip: 'WebSocket bağlantısı aktif'
        };
      case 'connecting':
        return {
          label: 'Bağlanıyor',
          color: 'warning' as const,
          icon: <CircularProgress size={size === 'small' ? 12 : 16} />,
          tooltip: 'WebSocket bağlantısı kuruluyor...'
        };
      case 'error':
        return {
          label: 'Hata',
          color: 'error' as const,
          icon: <ErrorIcon fontSize={size} />,
          tooltip: 'WebSocket bağlantı hatası'
        };
      case 'disconnected':
      default:
        return {
          label: 'Bağlantısız',
          color: 'default' as const,
          icon: <WifiOffIcon fontSize={size} />,
          tooltip: 'WebSocket bağlantısı yok'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <Tooltip title={statusInfo.tooltip} arrow>
      <Box sx={{ display: 'inline-flex', alignItems: 'center' }}>
        <Chip
          icon={statusInfo.icon}
          label={statusInfo.label}
          color={statusInfo.color}
          size={size}
          variant="outlined"
          sx={{
            height: size === 'small' ? 24 : 32,
            fontSize: size === 'small' ? '0.75rem' : '0.875rem'
          }}
        />
      </Box>
    </Tooltip>
  );
};

export default WebSocketStatus; 