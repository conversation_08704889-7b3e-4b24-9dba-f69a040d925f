import React, { useState, useEffect } from 'react';
import IconButton from '@mui/material/IconButton';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Avatar from '@mui/material/Avatar';
import PersonIcon from '@mui/icons-material/Person';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import Divider from '@mui/material/Divider';
import ConversationList from './ConversationList';

interface LeftPanelProps {
  userId: string;
  username: string;
  currentChatId: string;
  onSelectChat: (chatId: string) => void;
  onLogout: () => void;
  onCollapse?: (isCollapsed: boolean) => void;
}

const LeftPanel: React.FC<LeftPanelProps> = ({ 
  userId, 
  username, 
  currentChatId, 
  onSelectChat,
  onLogout,
  onCollapse
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);
  const profileMenuOpen = Boolean(profileMenuAnchor);

  const toggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    if (onCollapse) {
      onCollapse(newCollapsedState);
    }
  };

  const handleCloseProfileMenu = () => {
    setProfileMenuAnchor(null);
  };

  return (
    <div className={isCollapsed ? 'left-panel collapsed' : 'left-panel'}>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Header with user profile and collapse button */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          p: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
        }}>
          {!isCollapsed && userId && (
            <Box 
              sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: 'pointer' 
              }}
              onClick={(e) => setProfileMenuAnchor(e.currentTarget)}
            >
              <Avatar 
                sx={{ width: 32, height: 32, mr: 1, bgcolor: 'primary.main' }}
              >
                <PersonIcon fontSize="small" />
              </Avatar>
              <Typography 
                variant="subtitle2" 
                noWrap 
                sx={{ 
                  maxWidth: 140, 
                  fontWeight: 'medium'
                }}
              >
                {username || 'Kullanıcı'}
              </Typography>
            </Box>
          )}
          <IconButton onClick={toggleCollapse} size="small">
            {isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton>
        </Box>

        {/* Only render user-related components if userId exists */}
        {userId && (
          <>
            {/* User profile menu */}
            <Menu
              id="profile-menu"
              anchorEl={profileMenuAnchor}
              open={profileMenuOpen}
              onClose={handleCloseProfileMenu}
              MenuListProps={{
                'aria-labelledby': 'profile-button',
              }}
            >
              <MenuItem disabled>
                <ListItemIcon>
                  <AccountCircleIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary={username} />
              </MenuItem>
              <Divider />
              <MenuItem onClick={() => {
                handleCloseProfileMenu();
                onLogout();
              }}>
                <ListItemIcon>
                  <ExitToAppIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Çıkış Yap" />
              </MenuItem>
            </Menu>

            {/* Conversations list - only show when user is logged in and panel not collapsed */}
            {!isCollapsed && (
              <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
                <ConversationList 
                  userId={userId}
                  onSelectChat={onSelectChat}
                  currentChatId={currentChatId}
                />
              </Box>
            )}
          </>
        )}
      </Box>
    </div>
  );
};

export default LeftPanel; 