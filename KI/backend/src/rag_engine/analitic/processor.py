import pandas as pd
from sqlalchemy import create_engine, MetaData, text, select, inspect, and_, func
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, ProgrammingError
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
import re
from datetime import datetime
import uuid
import sqlparse
from dotenv import load_dotenv
import os
import hashlib
import json
import threading

def _convert_mysql_quotes(sql: str) -> str:
    """Convert MySQL-style backtick quotes to PostgreSQL-style doulbe quotes"""
    sql = re.sub(r'`([a-zA-Z0-9_]+)`\.`([a-zA-Z0-9_ ]+)`', r'\1."\2"', sql)
    sql = re.sub(r'`([^`]+?)`', r'"\1"', sql)
    return sql

def _fix_mysql_alias_quotes(sql: str) -> str:
    #  AS 'Ali̇as'  →  AS "Alias"   (veya AS Alias)
    pattern = r"\s+AS\s+'([^']+)'"
    return re.sub(pattern, lambda m: f' AS "{m.group(1)}"', sql, flags=re.IGNORECASE)

from .pre_processor import (
    process_csv_from_file,
    process_csv_from_http,
    normalize_csv_data,
    CSVProcessingResult
)
from .db.postgresql.models import (
    Base, User, UserTable, TableColumn, QueryLog, create_dynamic_table
)
from .db.postgresql.database import get_db, get_session

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class PostgreSQLProcessor:
    """Process data and manage PostgreSQL tables for users"""

    # Class-level lock for table creation to prevent race conditions
    _table_creation_lock = threading.Lock()

    def __init__(self):
        """
        Initialize the processor with database connection

        Args:
            db_uri: Database connection URI for SQLAlchemy (optional, defaults to env variable)
        """
        pass

    def _get_or_create_user(self, session: Session, user_id: str) -> User:
        """Get or create a user by ID"""
        result = session.execute(select(User).filter(User.id == user_id))
        user = result.scalars().first()

        if not user:
            user = User(id=user_id, username=f"user_{user_id}")
            session.add(user)
            # Do not commit here, let the caller manage the transaction
        return user

    def find_matching_table(self, user_id: str, columns: List[Dict[str, Any]]) -> Optional[UserTable]:
        """
        DEPRECATED: Find existing user table with matching column structure

        This function is disabled as part of the new policy:
        Each CSV upload should create a separate table.

        Args:
            user_id: ID of the user
            columns: List of column definitions

        Returns:
            Always returns None (disabled functionality)
        """
        # DISABLED: Always return None to force new table creation
        logger.info("find_matching_table called but disabled - always creating new tables")
        return None

        # ORIGINAL CODE (commented out):
        # with get_session() as session:
        #     # Get all tables for this user
        #     result = session.execute(select(UserTable).filter(UserTable.user_id == user_id))
        #     user_tables = result.scalars().all()
        #
        #     # Create a set of column names and types for comparison
        #     target_columns = {(col['name'], col['postgresql_type']) for col in columns}
        #
        #     for table in user_tables:
        #         # Get all columns for this table
        #         col_result = session.execute(
        #             select(TableColumn).filter(TableColumn.table_id == table.id)
        #         )
        #         db_columns = col_result.scalars().all()
        #
        #         # Create a comparable set of columns
        #         table_columns = {(col.column_name, col.column_type) for col in db_columns}
        #
        #         # Check if column sets match
        #         if target_columns == table_columns:
        #             return table
        #
        #     return None

    def create_user_table(
        self,
        user_id: str,
        table_name: str,
        columns: List[Dict[str, Any]],
        original_file_name: Optional[str] = None,
        chat_id: Optional[str] = None,
        file_content: Optional[str] = None,
        delimiter: str = ','
    ) -> UserTable:
        """
        Create a new table for a user

        Args:
            user_id: ID of the user
            table_name: Base name for the new table
            columns: List of column definitions
            original_file_name: Name of the original file (optional)
            chat_id: Chat identifier for unique naming (optional)
            file_content: CSV content for hash calculation (optional)
            delimiter: CSV delimiter character

        Returns:
            UserTable object representing the created table
        """
        # Use class-level lock to prevent concurrent table creation
        with self._table_creation_lock:
            with get_session() as session:
                with session.begin():
                    try:
                        # Get or create user
                        user = self._get_or_create_user(session, user_id)

                        # Create a unique table name using new naming system
                        safe_table_name = self._get_safe_table_name(
                            user_id=user_id,
                            chat_id=chat_id,
                            file_content=file_content,
                            base_name=table_name
                        )

                        # Calculate additional metadata
                        file_size = len(file_content.encode()) if file_content else None
                        file_hash_value = hashlib.sha256(file_content.encode()).hexdigest() if file_content else None

                        # Get upload order for this chat
                        upload_order = 1  # Default for first upload
                        if chat_id:
                            # Count existing tables for this chat
                            existing_chat_tables = session.execute(
                                select(UserTable).filter(
                                    UserTable.user_id == user.id,
                                    UserTable.chat_id == chat_id
                                )
                            ).scalars().all()
                            upload_order = len(existing_chat_tables) + 1

                        # Create a record in user_tables with enhanced metadata
                        # TEMPORARY: Only add new fields if they exist in the database schema
                        user_table_data = {
                            'user_id': user.id,
                            'table_name': safe_table_name,  # Use the final table name (may have extra suffix)
                            'original_file_name': original_file_name
                        }

                        # Try to add new fields, but don't fail if they don't exist
                        try:
                            user_table_data.update({
                                'chat_id': chat_id,
                                'file_hash': file_hash_value,
                                'file_size_bytes': file_size,
                                'upload_order': upload_order,
                                'delimiter': delimiter,
                                'encoding': 'utf-8'
                            })
                            user_table = UserTable(**user_table_data)
                        except Exception as schema_error:
                            logger.warning(f"New schema fields not available, using legacy format: {schema_error}")
                            # Fallback to basic fields only
                            user_table = UserTable(
                                user_id=user.id,
                                table_name=safe_table_name,
                                original_file_name=original_file_name
                            )
                        session.add(user_table)
                        session.flush()  # Get the ID without committing

                        # Create records in table_columns
                        for i, col in enumerate(columns):
                            column = TableColumn(
                                table_id=user_table.id,
                                column_name=col['name'],
                                column_type=col['postgresql_type'],
                                nullable=col.get('nullable', True),
                                ordinal_position=i
                            )
                            session.add(column)

                        # Add row_hash column to the columns list
                        columns_with_hash = list(columns) # Create a copy to avoid modifying the input list
                        columns_with_hash.append({
                            "name": "row_hash",
                            "postgresql_type": "VARCHAR(64)",
                            "nullable": False
                        })

                        # Enhanced table existence check with multiple attempts
                        max_attempts = 5  # Increase attempts
                        table_created = False

                        for attempt in range(max_attempts):
                            # Enhanced table existence check with multiple methods
                            # Method 1: information_schema
                            check_table_sql = f"""
                                SELECT EXISTS (
                                    SELECT FROM information_schema.tables
                                    WHERE table_name = '{safe_table_name}'
                                    AND table_schema = 'public'
                                );
                            """
                            table_exists_result = session.execute(text(check_table_sql))
                            table_exists_info = table_exists_result.scalar()

                            # Method 2: pg_class (more direct)
                            check_pg_class_sql = f"""
                                SELECT EXISTS (
                                    SELECT FROM pg_class c
                                    JOIN pg_namespace n ON n.oid = c.relnamespace
                                    WHERE c.relname = '{safe_table_name}'
                                    AND n.nspname = 'public'
                                    AND c.relkind = 'r'
                                );
                            """
                            pg_class_result = session.execute(text(check_pg_class_sql))
                            table_exists_pg = pg_class_result.scalar()

                            # Use OR logic - if either says it exists, consider it exists
                            table_exists = table_exists_info or table_exists_pg

                            logger.info(f"Table existence check for {safe_table_name}: info_schema={table_exists_info}, pg_class={table_exists_pg}, final={table_exists}")

                            if not table_exists:
                                logger.info(f"Table {safe_table_name} does not exist, proceeding with creation")
                                table_created = True
                                break

                            logger.warning(f"Table {safe_table_name} already exists (attempt {attempt + 1}/{max_attempts})")

                            # Always generate new name for next attempt (no dropping)
                            extra_random = str(uuid.uuid4().hex)[:8]
                            old_name = safe_table_name
                            safe_table_name = f"{safe_table_name}_{extra_random}"

                            # Update the user_table record with new name
                            user_table.table_name = safe_table_name

                            logger.info(f"Generated new table name: {old_name} -> {safe_table_name}")

                        if not table_created:
                            raise Exception(f"Could not generate unique table name after {max_attempts} attempts")

                        # Create the actual table in PostgreSQL (only if it doesn't exist)
                        if table_created:
                            # Generate a short unique constraint name to avoid PostgreSQL limits
                            constraint_suffix = str(uuid.uuid4().hex)[:8]
                            constraint_name = f"uk_rh_{constraint_suffix}"

                            create_table_sql = f"""
                                CREATE TABLE {safe_table_name} (
                                    id SERIAL PRIMARY KEY,
                                    {', '.join(
                                        f"{col['name']} {col['postgresql_type']}" +
                                        (" NOT NULL" if not col.get('nullable', True) else "")
                                        for col in columns_with_hash
                                    )},
                                    created_at TIMESTAMP DEFAULT NOW(),
                                    updated_at TIMESTAMP DEFAULT NOW(),
                                    CONSTRAINT {constraint_name} UNIQUE (row_hash)
                                )
                            """

                            try:
                                session.execute(text(create_table_sql))
                                logger.info(f"Successfully created table: {safe_table_name}")
                            except Exception as create_error:
                                error_str = str(create_error)
                                logger.error(f"Failed to create table {safe_table_name}: {error_str}")

                                # Check if it's a "already exists" error
                                if "already exists" in error_str.lower() or "duplicate" in error_str.lower():
                                    logger.warning(f"Table {safe_table_name} seems to exist despite our checks - forcing new name")
                                    # Force a completely new name
                                    final_random = str(uuid.uuid4().hex)[:12]  # Longer random
                                    timestamp_new = datetime.now().strftime("%Y%m%d%H%M%S%f")
                                    safe_table_name = f"tbl_{final_random}_{timestamp_new}"[:63]  # Ensure under limit
                                    user_table.table_name = safe_table_name
                                    constraint_name = f"uk_{final_random[:8]}"

                                    create_table_sql = f"""
                                        CREATE TABLE {safe_table_name} (
                                            id SERIAL PRIMARY KEY,
                                            {', '.join(
                                                f"{col['name']} {col['postgresql_type']}" +
                                                (" NOT NULL" if not col.get('nullable', True) else "")
                                                for col in columns_with_hash
                                            )},
                                            created_at TIMESTAMP DEFAULT NOW(),
                                            updated_at TIMESTAMP DEFAULT NOW(),
                                            CONSTRAINT {constraint_name} UNIQUE (row_hash)
                                        )
                                    """
                                    session.execute(text(create_table_sql))
                                    logger.info(f"Successfully created table with fallback name: {safe_table_name}")
                                else:
                                    # Re-raise if it's not a naming conflict
                                    raise create_error
                        else:
                            logger.error(f"Table creation skipped - could not find unique name")
                        logger.info(f"Successfully created table: {safe_table_name}")

                        # Refresh the user_table instance to load generated values like ID
                        session.refresh(user_table)
                        return user_table
                    except Exception as e:
                        logger.error(f"Error creating table: {str(e)}")
                        raise # Re-raise after logging

    def _get_safe_table_name(self, user_id: str, chat_id: str = None, file_content: str = None, base_name: str = None) -> str:
        """
        Generate a unique table name for each CSV upload.

        New format: user_{user_id}_chat_{chat_id}_file_{file_hash}_{timestamp}

        Args:
            user_id: User identifier
            chat_id: Chat identifier (optional)
            file_content: CSV file content for hash calculation (optional)
            base_name: Base name from filename (optional, for fallback)

        Returns:
            Unique table name respecting PostgreSQL's 63 character limit
        """
        # 1. Clean and prepare user_id
        safe_user_id = re.sub(r'[^a-zA-Z0-9]', '_', user_id).lower()

        # 2. Clean and prepare chat_id
        safe_chat_id = "unknown"
        if chat_id:
            safe_chat_id = re.sub(r'[^a-zA-Z0-9]', '_', str(chat_id)).lower()

        # 3. Calculate file hash (8 characters)
        file_hash = "default"
        if file_content:
            file_hash = hashlib.sha256(file_content.encode()).hexdigest()[:8]

        # 4. Generate timestamp with microseconds + random + process ID for uniqueness
        now = datetime.now()
        timestamp = now.strftime("%Y%m%d%H%M%S%f")  # Include microseconds
        process_id = os.getpid()  # Add process ID
        thread_id = threading.get_ident()  # Add thread ID
        random_suffix = str(uuid.uuid4().hex)[:6]  # Add 6 random chars
        timestamp = f"{timestamp}_{process_id}_{thread_id}_{random_suffix}"

        # 5. Build table name components
        user_part = f"user_{safe_user_id}"
        chat_part = f"chat_{safe_chat_id}"
        file_part = f"file_{file_hash}"

        # 6. Construct full name with chat-based naming (like original but unique)
        # Format: chat_{chat_id}_{timestamp}_{unique_id}
        unique_id = str(uuid.uuid4().hex)[:8]  # 8 char unique part
        simple_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # Readable timestamp

        if safe_chat_id and safe_chat_id != "unknown":
            full_name = f"chat_{safe_chat_id}_{simple_timestamp}_{unique_id}"
        else:
            # Fallback if no chat_id
            full_name = f"csv_{simple_timestamp}_{unique_id}"

        # 7. Handle PostgreSQL 63 character limit
        max_len = 63
        if len(full_name) > max_len:
            # Truncate strategy: preserve timestamp and file_hash for uniqueness
            fixed_suffix = f"_{file_part}_{timestamp}"  # ~25 chars
            available_space = max_len - len(fixed_suffix)

            # Split remaining space between user and chat parts
            user_space = available_space // 2
            chat_space = available_space - user_space

            truncated_user = user_part[:user_space].rstrip('_')
            truncated_chat = chat_part[:chat_space].rstrip('_')

            full_name = f"{truncated_user}_{truncated_chat}{fixed_suffix}"

        # 8. Final validation and cleanup
        full_name = re.sub(r'_+', '_', full_name)  # Remove multiple underscores
        full_name = full_name.strip('_')  # Remove leading/trailing underscores

        # Ensure it starts with a letter (PostgreSQL requirement)
        if not full_name[0].isalpha():
            full_name = f"t_{full_name}"

        return full_name[:max_len]


    def _calculate_row_hash(self, record: Dict[str, Any]) -> str:
        """
        Calculate a hash for a row based on its values

        Args:
            record: Dictionary containing the row data

        Returns:
            SHA-256 hash of the row data
        """
        # Sort the keys to ensure consistent hashing
        sorted_items = sorted(
            (k, str(v)) for k, v in record.items()
            if k not in ['id', 'created_at', 'updated_at', 'row_hash'] # Exclude metadata and hash itself
        )

        # Create a string representation of the row
        row_str = json.dumps(sorted_items, sort_keys=True)

        # Calculate SHA-256 hash
        return hashlib.sha256(row_str.encode()).hexdigest()

    def insert_data_to_table(
        self,
        user_id: str,
        table_id: int,
        data: pd.DataFrame
    ) -> int:
        """Insert data into a user table"""
        rows_inserted = 0

        # First get the table info
        with get_session() as session:
            result = session.execute(
                select(UserTable).filter(
                    UserTable.id == table_id,
                    UserTable.user_id == user_id
                )
            )
            user_table = result.scalars().first()

            if not user_table:
                raise ValueError(f"Table with ID {table_id} not found for user {user_id}")

            table_name = user_table.table_name

        # Convert DataFrame to dict records
        records = data.to_dict('records')
        logger.info(f"Converting {len(records)} records for insertion into table {table_name}")

        # Process records one by one with individual sessions
        # Consider batching for better performance if needed
        total_records = len(records)
        for idx, record in enumerate(records):
            if idx % 1000 == 0:  # Log progress every 1000 records
                logger.info(f"Processing record {idx + 1}/{total_records}")
            with get_session() as session:
                with session.begin(): # Use transaction for each row attempt
                    try:
                        # Calculate row hash
                        row_hash = self._calculate_row_hash(record)

                        # Prepare the values for insertion
                        values = []
                        columns = []

                        # Add all fields including the hash
                        record_with_hash = record.copy() # Avoid modifying original record dict
                        record_with_hash['row_hash'] = row_hash

                        for col, value in record_with_hash.items():
                            columns.append(col)
                            # Check for None or NaN values (pandas/numpy NaN)
                            if (value is None or 
                                (isinstance(value, float) and pd.isna(value)) or
                                (hasattr(value, '__str__') and str(value).lower() == 'nan')):
                                values.append('NULL')
                            elif isinstance(value, (int, float)):
                                values.append(str(value))
                            else:
                                # String values need quotes and escaping
                                escaped_value = str(value).replace("'", "''")
                                values.append(f"'{escaped_value}'")

                        # Build and execute the INSERT statement
                        insert_sql = f"""
                            INSERT INTO {table_name}
                            ({', '.join(f'"{c}"' for c in columns)})  -- Quote column names
                            VALUES ({', '.join(values)})
                            ON CONFLICT (row_hash) DO NOTHING
                        """

                        result = session.execute(text(insert_sql))
                        if result.rowcount is not None and result.rowcount > 0:
                            rows_inserted += 1
                        elif idx < 3:  # Log details for first 3 records if they fail
                            logger.info(f"Record {idx + 1} insert returned rowcount: {result.rowcount}")
                            logger.info(f"SQL that returned 0 rows: {insert_sql}")
                        
                        # Log successful insertions for first few records
                        if idx < 3 and result.rowcount and result.rowcount > 0:
                            logger.info(f"Successfully inserted record {idx + 1}")

                    except Exception as e:
                        if idx < 10:  # Log detailed errors for first 10 records
                            logger.warning(f"Failed to insert record {idx + 1}: {str(e)}")
                            logger.debug(f"Failed record data: {record}")
                            logger.debug(f"Failed SQL: {insert_sql}")
                        elif idx % 1000 == 0:  # Log summary errors for every 1000th record after that
                            logger.warning(f"Failed to insert row {idx + 1}: {str(e)}")
                        # The transaction will be rolled back automatically by context manager
                        continue # Move to next record

        # Update the total row count in a final session
        if rows_inserted > 0:
            with get_session() as session:
                with session.begin():
                    try:
                        # Lock the row for update
                        result = session.execute(
                           select(UserTable).filter(UserTable.id == table_id).with_for_update()
                        )
                        user_table = result.scalars().first()
                        if user_table:
                           user_table.row_count = (user_table.row_count or 0) + rows_inserted
                           user_table.updated_at = datetime.now() # Update timestamp
                        else:
                           logger.error(f"Failed to find table {table_id} to update row count.")

                    except Exception as e:
                        logger.error(f"Failed to update row count for table {table_id}: {str(e)}")

        logger.info(f"Data insertion completed: {rows_inserted} rows successfully inserted out of {total_records} total records")
        return rows_inserted

    def process_csv_and_store(
        self,
        user_id: str,
        csv_content: str,
        table_name: Optional[str] = None,
        original_file_name: Optional[str] = None,
        delimiter: str = ',',
        chat_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process CSV data and store it in the database

        Args:
            user_id: ID of the user
            csv_content: CSV content as a string
            table_name: Base name for the table (optional)
            original_file_name: Name of the original file (optional)
            delimiter: CSV delimiter character
            chat_id: Chat identifier for unique table naming (optional)

        Returns:
            Dictionary with processing results
        """
        try:
            # Process the CSV data (synchronous function)
            result = process_csv_from_http(csv_content, delimiter)

            if not result.success:
                return {
                    "success": False,
                    "error": result.error
                }

            # Generate table name if not provided
            if not table_name:
                if original_file_name:
                    base_name = original_file_name.split('.')[0]
                    table_name = base_name # Keep it simple, _get_safe_table_name adds complexity later
                else:
                    table_name = f"d_{datetime.now().strftime('%Y%m%d')}"

            # Normalize the data again to get DataFrame (synchronous function)
            df = normalize_csv_data(csv_content, delimiter)

            if df is None:
                return {
                    "success": False,
                    "error": "Failed to normalize CSV data"
                }

            # Prepare column definitions for matching and creation
            columns = [
                {
                    "name": col.name,
                    "postgresql_type": col.postgresql_type,
                    "nullable": col.nullable
                }
                for col in result.schema_info.columns
            ]

            # Remove the 'id' column if present, as it's auto-generated
            columns = [col for col in columns if col['name'].lower() != 'id']

            # Also remove the 'id' column from the DataFrame if it exists
            if 'id' in df.columns:
                 df = df.drop(columns=['id'])

            # Ensure DataFrame columns match the processed schema (order and names)
            # This is crucial because insert_data relies on the DataFrame structure
            schema_column_names = [col['name'] for col in columns]
            logger.info(f"Schema column names: {schema_column_names}")
            logger.info(f"DataFrame columns: {list(df.columns)}")
            logger.info(f"DataFrame shape before filtering: {df.shape}")
            
            # Check if all schema columns exist in DataFrame
            missing_columns = [col for col in schema_column_names if col not in df.columns]
            if missing_columns:
                logger.error(f"Missing columns in DataFrame: {missing_columns}")
                return {
                    "success": False,
                    "error": f"Missing columns in DataFrame: {missing_columns}"
                }
            
            df = df[schema_column_names] # Reorder/select columns in DataFrame to match schema
            logger.info(f"DataFrame shape after filtering: {df.shape}")
            logger.info(f"Sample DataFrame data (first 2 rows): {df.head(2).to_dict()}")


            # CHANGED: Always create a new table for each CSV (no more table matching)
            # existing_table = self.find_matching_table(user_id, columns)  # DISABLED

            # Create a new table for every CSV upload
            new_table = self.create_user_table(
                user_id=user_id,
                table_name=table_name,
                columns=columns,
                original_file_name=original_file_name,
                chat_id=chat_id,
                file_content=csv_content,
                delimiter=delimiter
            )

            # Insert data into the new table
            logger.info(f"Starting data insertion for table {new_table.table_name} with {len(df)} rows")
            rows_inserted = self.insert_data_to_table(user_id, new_table.id, df)
            logger.info(f"Completed data insertion: {rows_inserted} rows inserted out of {len(df)} total rows")

            # Refetch table to get accurate row_count after potential updates
            final_row_count = 0
            with get_session() as session:
                res = session.execute(select(UserTable.row_count).filter(UserTable.id == new_table.id))
                final_row_count = res.scalar_one_or_none() or 0

            return {
                "success": True,
                "table_id": new_table.id,
                "table_name": new_table.table_name,
                "rows_inserted": rows_inserted,
                "row_count": final_row_count, # Use refetched count
                "columns": columns, # Return the user-facing columns, not internal ones like row_hash
                "created_new_table": True  # Always true now since we always create new tables
            }

        except Exception as e:
            logger.exception(f"Error processing and storing CSV for user {user_id}: {str(e)}") # Use logger.exception for stack trace
            return {
                "success": False,
                "error": f"Internal server error processing CSV. Details: {str(e)}" # Avoid leaking too much detail potentially
            }

    def get_user_tables(self, user_id: str, chat_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get tables owned by a user with metadata, optionally filtered by chat

        Args:
            user_id: ID of the user
            chat_id: Optional chat ID to filter tables for specific chat

        Returns:
            List of dictionaries with table metadata
        """
        with get_session() as session:
            # Build query with optional chat_id filter
            query = select(UserTable).filter(UserTable.user_id == user_id)

            if chat_id:
                # Filter by chat_id if provided
                query = query.filter(UserTable.chat_id == chat_id)
                print(f"Filtering tables for user {user_id} and chat {chat_id}")
            else:
                print(f"Getting all tables for user {user_id}")

            query = query.order_by(UserTable.created_at.desc())
            result = session.execute(query)
            tables = result.scalars().all()

            print(f"Found {len(tables)} tables for user {user_id}" + (f" in chat {chat_id}" if chat_id else ""))

            table_results = []
            for table in tables:
                # Get columns for this table (excluding internal row_hash)
                col_result = session.execute(
                    select(TableColumn)
                    .filter(TableColumn.table_id == table.id, TableColumn.column_name != 'row_hash')
                    .order_by(TableColumn.ordinal_position)
                )
                columns = col_result.scalars().all()

                table_results.append({
                    "id": table.id,
                    "name": table.table_name,
                    "original_file_name": table.original_file_name,
                    "created_at": table.created_at.isoformat() if table.created_at else None,
                    "updated_at": table.updated_at.isoformat() if table.updated_at else None,
                    "row_count": table.row_count or 0, # Ensure row_count is not None
                    "columns": [
                        {
                            "name": col.column_name,
                            "type": col.column_type,
                            "nullable": col.nullable
                        }
                        for col in columns
                    ]
                })

            return table_results

    def execute_user_query(self, user_id: str, query: str, max_rows: int = 1000) -> Dict[str, Any]:
        """
        Execute a complex SQL query for a user with enhanced security checks and support for advanced operations.
        
        Supports:
        - Complex SELECT queries with CTEs (WITH clauses)
        - UNION/UNION ALL operations  
        - Subqueries and complex expressions
        - Analytical functions (CASE, EXTRACT, COUNT, SUM, etc.)
        - CREATE TABLE AS SELECT operations
        - DELETE operations for data cleaning

        Args:
            user_id: ID of the user
            query: SQL query to execute
            max_rows: Maximum number of rows to return (increased default for complex analytics)

        Returns:
            Query results or error information
        """
        with get_session() as session: # Use one session for the whole operation including logging
          with session.begin(): # Use a transaction
            start_time = datetime.now()
            success = False
            query_result = None # Renamed from 'result' to avoid conflict
            error_message = None
            affected_table_id = None
            row_count = 0

            try:
                # Enhanced query validation for complex analytical operations
                query_normalized = query.strip().lower()
                
                # Allow more complex analytical operations
                allowed_operations = [
                    'select', 'with', 'create table', 'delete from',
                    '(select', '( select'  # Handle subqueries in parentheses
                ]
                
                # Check if the query starts with any allowed operation
                is_allowed = any(query_normalized.startswith(op) for op in allowed_operations)
                
                # Special handling for UNION operations (they contain SELECT but don't start with it)
                if not is_allowed and ('union' in query_normalized and 'select' in query_normalized):
                    is_allowed = True
                
                if not is_allowed:
                    raise ValueError("Only SELECT, WITH (CTE), CREATE TABLE AS SELECT, DELETE, and UNION queries are allowed")

                # Enhanced table name extraction for complex queries
                table_names = self._extract_table_names_enhanced(query)
                
                # For analytical queries without explicit tables (like pure UNION with constants), allow execution
                if not table_names:
                    # Check if it's a pure analytical query without table references
                    if any(pattern in query_normalized for pattern in ['union all select', 'union select']):
                        # These are analytical constant queries, allow them to proceed
                        print(f"Executing analytical query without table references for user {user_id}")
                        table_names = []  # Empty list but continue execution
                    else:
                        raise ValueError("Could not identify any valid tables in query")

                # Check user access to all identified tables
                user_tables = []
                for table_name in table_names:
                    table_lookup_result = session.execute(
                        select(UserTable).filter(
                            UserTable.user_id == user_id,
                            UserTable.table_name == table_name
                        )
                    )
                    table = table_lookup_result.scalars().first()

                    if not table:
                        raise ValueError(f"Access denied or table not found: {table_name}")

                    user_tables.append(table)
                    # Use the first table found for logging purposes
                    if affected_table_id is None:
                        affected_table_id = table.id

                # Execute the enhanced query with better error handling
                try:
                    # Set reasonable timeout for complex analytical queries
                    from sqlalchemy.util import LRUCache
                    compiled_cache = LRUCache(capacity=500)
                    conn = session.connection(execution_options={"compiled_cache":compiled_cache})
                    if '`' in query:
                        query = _convert_mysql_quotes(query)
                    if " AS '" in query:
                        query = _fix_mysql_alias_quotes(query)
                    result_proxy = conn.execute(text(query))
                  

                    # Enhanced result processing
                    if result_proxy.returns_rows:
                        rows = []
                        keys = list(result_proxy.keys()) # Get column names
                        result_rows = result_proxy.fetchall()

                        # Process results with enhanced data type handling
                        for row in result_rows[:max_rows]:
                            row_dict = {}
                            for i, value in enumerate(row):
                                # Enhanced data type handling for complex queries
                                if value is None:
                                    row_dict[keys[i]] = None
                                elif hasattr(value, 'isoformat'):  # Handle dates
                                    row_dict[keys[i]] = value.isoformat()
                                elif isinstance(value, (int, float, str, bool)):
                                    row_dict[keys[i]] = value
                                else:
                                    # Convert other types to string representation
                                    row_dict[keys[i]] = str(value)
                            
                            rows.append(row_dict)

                        query_result = {
                            "columns": keys,
                            "rows": rows,
                            "total_rows_available": len(result_rows),
                            "rows_returned": len(rows),
                            "truncated": len(result_rows) > max_rows
                        }
                        row_count = len(rows)
                    else:
                        # Handle non-row returning statements
                        affected_rows = result_proxy.rowcount if hasattr(result_proxy, 'rowcount') else 0
                        if 'create table' in query_normalized:
                            query_result = {"message": "Table created successfully.", "affected_rows": affected_rows}
                        elif 'delete' in query_normalized:
                            query_result = {"message": f"Successfully deleted {affected_rows} rows.", "affected_rows": affected_rows}
                        else:
                            query_result = {"message": "Query executed successfully.", "affected_rows": affected_rows}
                        row_count = affected_rows

                    success = True
                    
                except ProgrammingError as pe:
                    logger.error(f"Database programming error executing query for user {user_id}: {pe}")
                    # Enhanced error message for complex queries
                    if "does not exist" in str(pe):
                        error_message = f"Table or column reference error: {pe.orig}"
                    elif "syntax error" in str(pe):
                        error_message = f"SQL syntax error: {pe.orig}"
                    else:
                        error_message = f"Query execution failed: {pe.orig}"
                    query_result = {"error": error_message}
                    success = False
                    
                except SQLAlchemyError as sqlae:
                    logger.error(f"SQLAlchemy error executing query for user {user_id}: {sqlae}")
                    error_message = f"Database error: {sqlae.orig if hasattr(sqlae, 'orig') else str(sqlae)}"
                    query_result = {"error": error_message}
                    success = False
                    
                except Exception as e:
                    logger.exception(f"Unexpected error executing query for user {user_id}: {e}")
                    error_message = f"Query execution error: {str(e)}"
                    query_result = {"error": error_message}
                    success = False

            except ValueError as ve:
                error_message = str(ve)
                query_result = {"error": error_message}
                success = False
            except Exception as e:
                logger.exception(f"Unexpected error processing query for user {user_id}: {e}")
                error_message = f"Query processing error: {str(e)}"
                query_result = {"error": error_message}
                success = False

            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            # Enhanced query logging with better metadata
            """
            if affected_table_id or success:  # Log even for tableless analytical queries if successful
                try:
                    query_log = QueryLog(
                        user_id=user_id,
                        table_id=affected_table_id,  # May be None for pure analytical queries
                        query_text=query[:2000],  # Truncate very long queries for storage
                        executed_at=start_time,
                        execution_time_ms=int(execution_time),
                        row_count=row_count if success else 0,
                        success=success,
                        error_message=error_message[:500] if error_message and not success else None  # Truncate long errors
                    )
                    session.add(query_log)
                except Exception as log_e:
                    logger.error(f"Failed to log query for user {user_id}: {log_e}")
            """
            # Enhanced return structure with additional metadata
            return {
                "success": success,
                "execution_time_ms": int(execution_time),
                "data": query_result,
                "row_count": row_count if success else 0,
                "query_type": self._determine_query_type(query),
                "tables_accessed": table_names
            }


    def _extract_table_names(self, query: str) -> List[str]:
        """Extract table names from a SQL query (synchronous method as it doesn't use DB)"""
        # Use sqlparse for more robust parsing
        parsed = sqlparse.parse(query)
        if not parsed:
            return []

        table_names = set()

        def find_tables_recursive(tokens):
            from_seen = False
            for token in tokens:
                if token.is_keyword and token.value.upper() == 'FROM':
                    from_seen = True
                elif from_seen and isinstance(token, sqlparse.sql.Identifier):
                     # Check if it's an actual table name vs alias in a subquery etc.
                     # This simple check might need refinement for complex queries (joins, subqueries)
                     # It assumes the first identifier after FROM is the main table.
                     # Need to handle joins (`table1 JOIN table2`) and aliases (`table1 t1`)

                     # Check for alias: look ahead slightly
                     next_idx = tokens.index(token) + 1
                     is_alias = False
                     if next_idx < len(tokens):
                         next_token = tokens[next_idx]
                         # Skip whitespace
                         while next_token.is_whitespace and next_idx + 1 < len(tokens):
                             next_idx += 1
                             next_token = tokens[next_idx]

                         if isinstance(next_token, sqlparse.sql.Identifier): # simple alias like "table alias"
                            is_alias = True
                         elif next_token.is_keyword and next_token.value.upper() == 'AS': # explicit alias "table AS alias"
                             is_alias = True

                     # Basic check: Add the identifier's value. Needs improvement for complex cases.
                     # This won't handle "schema.table" correctly yet.
                     table_names.add(token.get_real_name()) # get_real_name() helps with quoted identifiers

                     # Reset from_seen after finding the first table identifier? Depends on desired complexity.
                     # For simplicity now, let's assume only one FROM clause matters for access check.
                     # To handle multiple tables (JOINs), we'd need to continue parsing.
                     from_seen = False # Reset for now to avoid picking up aliases right after table name.

                elif isinstance(token, sqlparse.sql.IdentifierList):
                     for identifier in token.get_identifiers():
                         # Recursive call or add logic here if needed
                         continue
                elif token.is_group: # Recurse into parentheses, etc.
                    find_tables_recursive(token.tokens)

        find_tables_recursive(parsed[0].tokens)

        # Convert set to list for return consistency
        # Filter out potential keywords captured incorrectly if needed
        # Note: This simple parser might fail on complex JOINs, subqueries, schemas.
        # Consider a more robust SQL parsing library if needed.
        return list(table_names)

    def _extract_table_names_enhanced(self, query: str) -> List[str]:
        """
        Enhanced table name extraction for complex queries including CTEs, subqueries, and UNION operations.
        
        Handles:
        - WITH clauses (CTEs)
        - Multiple FROM clauses 
        - JOIN operations
        - Subqueries
        - UNION operations
        - Complex nested structures
        """
        import re
        
        table_names = set()
        
        try:
            # Use sqlparse for primary parsing
            parsed = sqlparse.parse(query)
            if not parsed:
                return []

            def extract_from_token_list(token_list):
                """Recursively extract table names from token lists"""
                i = 0
                tokens = list(token_list.flatten())
                
                while i < len(tokens):
                    token = tokens[i]
                    
                    if token.is_keyword and token.value.upper() == 'FROM':
                        # Look for the next identifier after FROM
                        j = i + 1
                        while j < len(tokens) and (tokens[j].is_whitespace or tokens[j].value == ','):
                            j += 1
                        
                        if j < len(tokens):
                            if isinstance(tokens[j], sqlparse.sql.Identifier):
                                table_name = tokens[j].get_real_name()
                                if table_name and not self._is_function_or_keyword(table_name):
                                    table_names.add(table_name)
                            elif tokens[j].ttype is None and not tokens[j].is_keyword:
                                # Handle simple table names that aren't wrapped in Identifier
                                table_name = tokens[j].value
                                if table_name and not self._is_function_or_keyword(table_name):
                                    table_names.add(table_name)
                    
                    elif token.is_keyword and token.value.upper() == 'JOIN':
                        # Look for table name after JOIN
                        j = i + 1
                        while j < len(tokens) and tokens[j].is_whitespace:
                            j += 1
                        
                        if j < len(tokens):
                            if isinstance(tokens[j], sqlparse.sql.Identifier):
                                table_name = tokens[j].get_real_name()
                                if table_name and not self._is_function_or_keyword(table_name):
                                    table_names.add(table_name)
                            elif tokens[j].ttype is None and not tokens[j].is_keyword:
                                table_name = tokens[j].value
                                if table_name and not self._is_function_or_keyword(table_name):
                                    table_names.add(table_name)
                    
                    i += 1

            # Extract from all parts of the query
            for statement in parsed:
                extract_from_token_list(statement)
                
                # Handle subqueries and CTEs recursively
                for token in statement.flatten():
                    if token.is_group:
                        extract_from_token_list(token)

            # Additional regex-based extraction for edge cases
            # This catches table names that sqlparse might miss in complex queries
            regex_patterns = [
                r'\bFROM\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # FROM table_name
                r'\bJOIN\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # JOIN table_name
                r'\bINTO\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # INTO table_name (for CREATE TABLE)
            ]
            
            query_upper = query.upper()
            for pattern in regex_patterns:
                matches = re.findall(pattern, query_upper, re.IGNORECASE)
                for match in matches:
                    if not self._is_function_or_keyword(match):
                        table_names.add(match.lower())

        except Exception as e:
            logger.warning(f"Enhanced table extraction failed, falling back to basic extraction: {e}")
            # Fall back to the original method
            return self._extract_table_names(query)

        return list(table_names)

    def _is_function_or_keyword(self, name: str) -> bool:
        """Check if a name is a SQL function or keyword that should be ignored"""
        if not name:
            return True
            
        sql_keywords = {
            'SELECT', 'FROM', 'WHERE', 'GROUP', 'ORDER', 'HAVING', 'UNION', 'ALL',
            'DISTINCT', 'AS', 'ON', 'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN',
            'LIKE', 'IS', 'NULL', 'TRUE', 'FALSE', 'CASE', 'WHEN', 'THEN', 'ELSE',
            'END', 'WITH', 'RECURSIVE', 'CTE', 'VALUES', 'DEFAULT'
        }
        
        sql_functions = {
            'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'EXTRACT', 'CURRENT_DATE', 
            'CURRENT_TIME', 'CURRENT_TIMESTAMP', 'CONCAT', 'UPPER', 'LOWER',
            'SUBSTRING', 'LENGTH', 'TRIM', 'COALESCE', 'NULLIF'
        }
        
        return name.upper() in sql_keywords or name.upper() in sql_functions

    def _determine_query_type(self, query: str) -> str:
        """Determine the type of SQL query for metadata purposes"""
        query_lower = query.strip().lower()
        
        if query_lower.startswith('with'):
            return 'CTE_SELECT'
        elif 'union' in query_lower:
            return 'UNION_SELECT'
        elif query_lower.startswith('create table'):
            return 'CREATE_TABLE'
        elif query_lower.startswith('delete'):
            return 'DELETE'
        elif query_lower.startswith('select') or query_lower.startswith('(select'):
            if 'join' in query_lower:
                return 'JOIN_SELECT'
            elif any(func in query_lower for func in ['count(', 'sum(', 'avg(', 'min(', 'max(']):
                return 'AGGREGATE_SELECT'
            else:
                return 'SIMPLE_SELECT'
        else:
            return 'UNKNOWN'


def get_connector(config: Dict[str, Any] = None) -> 'PostgreSQLProcessor':
    """
    Get a PostgreSQL processor instance with the provided configuration (SYNCHRONOUS)

    Args:
        config: Database configuration including connection details

    Returns:
        PostgreSQLProcessor instance
    """
    # Get connection string from config or environment variables


    # Create and initialize the processor
    processor = PostgreSQLProcessor()

    # Removed: Database initialization call (await processor.initialize_db())

    return processor

def process_csv_for_user(
    user_id: str,
    file_path: str = None,
    csv_content: str = None,
    table_name: str = None,
    original_file_name: str = None,
    delimiter: str = ',',
    db_config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Process a CSV file and store it in the user's database (SYNCHRONOUS)

    Args:
        user_id: ID of the user
        file_path: Path to CSV file (optional if csv_content is provided)
        csv_content: CSV content as string (optional if file_path is provided)
        table_name: Name for the table (optional)
        original_file_name: Original file name (optional)
        delimiter: CSV delimiter character
        db_config: Database configuration

    Returns:
        Dictionary with processing results
    """
    # Initialize the processor (now synchronous)
    try:
      processor = get_connector(db_config)
    except Exception as e:
        logger.error(f"Failed to get database connector: {e}")
        return {'success': False, 'error': f'Database connection failed: {e}'}


    # Process CSV content or file
    if csv_content:
        return processor.process_csv_and_store(
            user_id,
            csv_content,
            table_name,
            original_file_name,
            delimiter
        )
    elif file_path:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                csv_content = f.read()
        except FileNotFoundError:
             logger.error(f"CSV file not found at path: {file_path}")
             return {'success': False, 'error': f"File not found: {file_path}"}
        except Exception as e:
             logger.error(f"Error reading CSV file {file_path}: {e}")
             return {'success': False, 'error': f"Error reading file: {str(e)}"}


        if not original_file_name:
            import os
            original_file_name = os.path.basename(file_path)

        return processor.process_csv_and_store(
            user_id,
            csv_content,
            table_name,
            original_file_name,
            delimiter
        )
    else:
        return {
            'success': False,
            'error': 'Either file_path or csv_content must be provided'
        }
