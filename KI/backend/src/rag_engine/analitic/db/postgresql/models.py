from sqlalchemy import Column, Integer, String, ForeignKey, Text, Boolean, DateTime, MetaData, Table, JSON, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

Base = declarative_base()

class User(Base):
    """User model to track ownership of data tables"""
    __tablename__ = 'users'
    
    id = Column(String(50), primary_key=True)
    username = Column(String(100), nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    tables = relationship("UserTable", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"

class UserTable(Base):
    """Model to track user-owned tables"""
    __tablename__ = 'user_tables'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(50), ForeignKey("users.id"))
    table_name = Column(String(100), nullable=False)
    original_file_name = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    row_count = Column(Integer, default=0)

    # NEW FIELDS for enhanced CSV tracking
    chat_id = Column(String(100), nullable=True, index=True)  # Chat ID tracking
    file_hash = Column(String(64), nullable=True, index=True)  # File content hash
    upload_session_id = Column(String(100), nullable=True)    # Session tracking
    file_size_bytes = Column(Integer, nullable=True)          # File size
    delimiter = Column(String(5), nullable=True)              # CSV delimiter
    encoding = Column(String(20), nullable=True)              # File encoding
    upload_order = Column(Integer, nullable=True)             # Upload sequence in chat
    
    # Relationships
    user = relationship("User", back_populates="tables")
    columns = relationship("TableColumn", back_populates="table", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<UserTable(id={self.id}, user_id={self.user_id}, table_name='{self.table_name}')>"

class TableColumn(Base):
    """Model to track columns in user tables"""
    __tablename__ = 'table_columns'
    
    id = Column(Integer, primary_key=True)
    table_id = Column(Integer, ForeignKey('user_tables.id'), nullable=False)
    column_name = Column(String(100), nullable=False)
    column_type = Column(String(50), nullable=False)
    nullable = Column(Boolean, default=True)
    ordinal_position = Column(Integer, nullable=False)
    
    # Relationships
    table = relationship("UserTable", back_populates="columns")
    
    def __repr__(self):
        return f"<TableColumn(id={self.id}, table_id={self.table_id}, column_name='{self.column_name}', type='{self.column_type}')>"

class QueryLog(Base):
    """Model to track user queries for auditing"""
    __tablename__ = 'query_logs'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(50), ForeignKey('users.id'), nullable=False)
    table_id = Column(Integer, ForeignKey('user_tables.id'), nullable=False)
    query_text = Column(Text, nullable=False)
    executed_at = Column(DateTime, default=datetime.utcnow)
    execution_time_ms = Column(Integer)
    row_count = Column(Integer)
    success = Column(Boolean, default=True)
    error_message = Column(Text)
    
    # Relationships
    user = relationship("User")
    table = relationship("UserTable")
    
    def __repr__(self):
        return f"<QueryLog(id={self.id}, user_id={self.user_id}, executed_at={self.executed_at})>"

def create_dynamic_table(metadata: MetaData, table_name: str, columns: List[Dict[str, Any]]) -> Table:
    """
    Dynamically create a SQLAlchemy Table object based on column definitions
    
    Args:
        metadata: SQLAlchemy MetaData instance
        table_name: Name of the table to create
        columns: List of column definitions with name, type, and nullable status
        
    Returns:
        SQLAlchemy Table object
    """
    table_columns = []
    
    # Add ID column as primary key
    table_columns.append(Column('id', Integer, primary_key=True))
    
    # Add user-defined columns
    for col in columns:
        col_type = _map_pg_type_to_sqlalchemy(col['postgresql_type'])
        table_columns.append(Column(
            col['name'], 
            col_type, 
            nullable=col.get('nullable', True)
        ))
    
    # Add metadata columns
    table_columns.append(Column('created_at', DateTime, default=datetime.utcnow))
    table_columns.append(Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow))
    
    # Create and return the table
    return Table(table_name, metadata, *table_columns)

def _map_pg_type_to_sqlalchemy(pg_type: str) -> Any:
    """Map PostgreSQL type to SQLAlchemy type"""
    from sqlalchemy import Integer, BigInteger, Numeric, String, Date, DateTime, Boolean, Text
    
    type_map = {
        'INTEGER': Integer,
        'BIGINT': BigInteger,
        'NUMERIC': Numeric,
        'DATE': Date,
        'TIMESTAMP': DateTime,
        'BOOLEAN': Boolean,
        'TEXT': Text,
    }
    
    if pg_type.startswith('VARCHAR'):
        import re
        # Extract size from VARCHAR(n)
        match = re.search(r'VARCHAR\((\d+)\)', pg_type)
        if match:
            size = int(match.group(1))
            return String(size)
        return String(255)
    
    return type_map.get(pg_type, Text)

class ChatMessageDB(Base):
    __tablename__ = 'chat_messages'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id = Column(String, nullable=False, index=True)
    user_id = Column(String, nullable=False, index=True)
    task_key = Column(String, nullable=True, index=True, unique=True) # Link to KAI task

    sender = Column(String, nullable=False) # 'user' or 'agent'
    text = Column(Text, nullable=True) # Can be null initially for agent messages
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Fields specific to agent messages / frontend requirements
    sources = Column(JSON, nullable=True) # Store as JSON array
    error = Column(Boolean, default=False, nullable=False)
    plan_reason = Column(Text, nullable=True)
    plan_task_list = Column(JSON, nullable=True) # Store as JSON array of strings
    task_list = Column(JSON, nullable=True) # Store actual executed tasks? JSON array of strings
    is_task_output = Column(Boolean, default=False, nullable=False) # Mark final agent outputs

    # New field for storing assets (charts, reports, etc.)
    assets = Column(JSON, nullable=True, default=lambda: {"charts": [], "reports": [], "other": []})

    # Optional: Define relationships if needed, e.g., back to User or a ChatSession table
    # user = relationship("User", back_populates="chat_messages") # Assuming User model has a chat_messages relationship

    __mapper_args__ = {
        'polymorphic_identity': 'chat_message',
    }

    def __repr__(self):
        return f"<ChatMessageDB(id={self.id}, chat_id='{self.chat_id}', sender='{self.sender}', timestamp='{self.timestamp}')>"

# Add index for faster querying by chat_id and timestamp
Index('ix_chat_messages_chat_id_timestamp', ChatMessageDB.chat_id, ChatMessageDB.timestamp)
