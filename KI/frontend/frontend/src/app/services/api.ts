import axios, { AxiosInstance } from 'axios';
import { StatusResponse } from '../utils/helpers';
import { ChatMessage } from '../../components/types'; // Adjust path as necessary

// Function to get the correct base URL for API requests
const getBaseURL = (): string => {
  // Use the Next.js API proxy routes instead of direct rewrites
  if (typeof window !== 'undefined') {
    return '/api/apiv1';
  }
  // For server-side requests (SSR), use the same proxy routes
  return '/api/apiv1';
};

// Base URL for API.
// All requests go through Next.js API proxy routes
// which handle backend communication and redirects server-side
const baseURL = getBaseURL();

console.log('🔧 API baseURL configured as:', baseURL);

// Standardized API response format
interface ApiResponse<T = any> {
  data: T | null;
  error: {
    code: string;
    message: string;
    field?: string;
    target?: string;
    details?: any;
  } | null;
  request_id: string;
}

// Alternative response format that some endpoints use
interface ApiResponseWithSuccess<T = any> {
  success: boolean;
  data: T;
  message?: string | null;
  error?: any | null;
  request_id?: string | null;
}

// User chats response format (direct array)
interface UserChatsResponse {
  chats: Chat[];
}

// Create Axios instance
const createApiInstance = (url: string = baseURL): AxiosInstance => {
  return axios.create({
    baseURL: url,
    headers: {
      'Content-Type': 'application/json',
    },
    // Set timeouts
    timeout: 30000000000,
    // For CORS compatibility
    withCredentials: false,
  });
};

const api = createApiInstance();

// Add error handling with Axios interceptors
api.interceptors.request.use(
  config => {
    const fullUrl = (config.baseURL || '') + (config.url || '');
    console.log('🔄 Sending request:', config.method?.toUpperCase(), config.url);
    console.log('📍 Full URL being requested:', fullUrl);
    console.log('🌐 Current window location:', typeof window !== 'undefined' ? window.location.href : 'Server-side');
    console.log('📡 Request headers:', config.headers);
    if (config.data) {
      console.log('📦 Request data:', config.data);
    }
    return config;
  },
  error => {
    console.error('❌ Request error:', error);
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  response => {
    console.log('✅ Response received:', response.status, response.config.url);
    return response;
  },
  error => {
    console.error('❌ Response error:', error);
    if (error.response) {
      console.log('🔴 Response status:', error.response.status);
      console.log('📋 Response data:', error.response.data);
      
      // Special handling for 500 server errors
      if (error.response.status === 500) {
        console.error('💥 Server error detected (500). This might be a temporary issue.');
        
        // Depending on the endpoint, we might want to return a fallback response
        // instead of letting the error propagate
        const url = error.config?.url;
        if (url?.includes('/kai/analyze')) {
          console.log('📌 Providing fallback response for analyze endpoint');
          // Return a fallback response for analyze endpoint
          return Promise.resolve({
            data: {
              status: 'failed',
              message: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.',
              error: 'Sunucu geçici olarak yanıt veremiyor (500 hatası).'
            }
          });
        }
        
        // For other endpoints, you can add similar handling if needed
      }
    }
    return Promise.reject(error);
  }
);

/**
 * API health check
 * @returns {Promise<{status: string, version: string}>} - Response containing API health status
 */
export const checkHealth = async (): Promise<{status: string, version: string}> => {
  try {
    console.log('💓 Starting health check');
    
    // Use the normalized URL without trailing slash to avoid redirects
    const response = await api.get<ApiResponse<{status: string, version: string}>>('/health');
    console.log(`✅ Successful health response:`, response.data);
    return response.data.data || { status: 'healthy', version: '1.0.0' };

  } catch (error: any) {
    console.error('❌ Health check error:', error);
    
    // Handle redirect responses
    if (error.response && (error.response.status === 307 || error.response.status === 308)) {
      console.log('🔄 Handling redirect response');
      try {
        // Try the redirected path but keep it relative
        const redirectPath = error.response.headers.location;
        if (redirectPath && redirectPath.includes('/apiv1/health')) {
          const response = await api.get<ApiResponse<{status: string, version: string}>>('/health/');
          return response.data.data || { status: 'healthy', version: '1.0.0' };
        }
      } catch (redirectError) {
        console.error('❌ Redirect handling failed:', redirectError);
      }
    }
    
    // Temporary test response - if backend is not ready
    console.log('🤖 Returning test response...');
    return {
      status: 'healthy',
      version: 'test-version-1.0.0'
    };
  }
};

// User Authentication Types - Updated for new API format
export interface UserLoginRequest {
  username: string;
}

export interface UserLoginResponse {
  user_id: string;
}

export interface CreateChatRequest {
  user_id: string;
  chat_name?: string;
}

export interface CreateChatResponse {
  chat_id: string;
  user_id: string;
  chat_name?: string;
}

export interface Chat {
  chat_id: string;
  chat_name?: string;
  created_at: string;
}

/**
 * Login a user with username
 * @param username The username to login with
 * @returns User ID if successful
 */
export const loginUser = async (username: string): Promise<UserLoginResponse> => {
  try {
    console.log('�� /apiv1/users/login/ isteği gönderiliyor');
    const response = await api.post<UserLoginResponse>('/users/login/', { username });
    console.log('✅ Kullanıcı girişi başarılı:', response.data);
    
    // The response is already in the correct format, no need to access .data.data
    return response.data;
  } catch (error) {
    console.error('❌ Kullanıcı girişi hatası:', error);
    throw error;
  }
};

/**
 * Create a new chat session for a user
 * @param userId The user ID
 * @param chatName Optional chat name
 * @returns Chat ID if successful
 */
export const createChat = async (userId: string, chatName?: string): Promise<CreateChatResponse> => {
  try {
    console.log('📤 /apiv1/chats/ isteği gönderiliyor');
    const response = await api.post<ApiResponseWithSuccess<CreateChatResponse>>('/chats/', { 
      user_id: userId,
      chat_name: chatName
    });
    console.log('✅ Chat oturumu oluşturuldu:', response.data);
    
    // Handle the response format with 'success' field
    if (!response.data.success || response.data.error) {
      throw new Error(response.data.error?.message || 'Chat creation failed');
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Chat oturumu oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Get all chat sessions for a user
 * @param userId The user ID
 * @returns List of chat sessions
 */
export const getUserChats = async (userId: string): Promise<Chat[]> => {
  try {
    console.log('📤 /apiv1/users/:user_id/chats/ isteği gönderiliyor');
    const response = await api.get<UserChatsResponse>(`/users/${userId}/chats/`);
    console.log('✅ Kullanıcı chat oturumları alındı:', response.data);
    
    // Handle direct chats array response
    if (response.data && Array.isArray(response.data.chats)) {
      return response.data.chats;
    }
    
    console.warn('⚠️ Unexpected response format for getUserChats:', response.data);
    return [];
  } catch (error) {
    console.error('❌ Kullanıcı chat oturumları alma hatası:', error);
    throw error;
  }
};

// Agent Communication Types - Updated for new API format
export interface AnalyzeRequest {
  user_id: string;
  input_text: string;
}

// Define the type for the analysis response
export interface AnalyzeResponse {
  task_key?: string;
  plan?: {
    reason: string;
    task_list: string[];
    agents?: Array<{
      agent_id: string;
      agent_name: string;
      task: string;
      reason: string;
    }>;
  };
  message?: string;
  status?: 'processing' | 'completed' | 'failed';
  error?: string;
}

/**
 * Sends a message to the agent for analysis.
 * @param userId The user ID.
 * @param chatId The chat ID.
 * @param inputText The user's message.
 * @returns The agent's response.
 */
export const sendMessageToAgent = async (
  userId: string, 
  chatId: string, 
  inputText: string
): Promise<AnalyzeResponse> => {
  try {
    console.log('📤 /apiv1/chats/:chat_id/analyze/ isteği gönderiliyor');
    const response = await api.post<ApiResponseWithSuccess<AnalyzeResponse>>(`/chats/${chatId}/analyze/`, { 
      user_id: userId,
      input_text: inputText
    });
    console.log('✅ Agent yanıtı alındı:', response.data);
    
    // Handle the response format with 'success' field
    if (!response.data.success || response.data.error) {
      return {
        status: 'failed',
        message: response.data.error?.message || 'Analysis failed',
        error: response.data.error?.code || 'unknown',
      };
    }
    
    // Extract the data and add default status if not present
    const analyzeData = response.data.data;
    return {
      ...analyzeData,
      status: analyzeData.status || 'processing', // Default to processing if not specified
    };
  } catch (error: any) {
    console.error('❌ Agent ile iletişim hatası:', error);
    
    // Return a graceful error response rather than throwing
    return {
      status: 'failed',
      message: error.response?.data?.error?.message || 'Bağlantı hatası. Lütfen daha sonra tekrar deneyin.',
      error: error.message || 'Bilinmeyen hata',
    };
  }
};

// Type for the chat history response - Updated for new API format
export interface ChatHistoryResponse {
  messages: ChatMessage[];
  total_count: number;
}

/**
 * Fetches the chat message history for a given chat ID.
 * @param chatId The ID of the chat to fetch history for.
 * @param limit Maximum number of messages to return.
 * @param skip Number of messages to skip.
 * @returns A promise resolving to the chat history response.
 */
export const getChatHistory = async (
  chatId: string,
  limit: number = 100, 
  skip: number = 0
): Promise<{success: boolean; messages: ChatMessage[]; total_count: number}> => {
  if (!chatId) {
    console.error('❌ getChatHistory called with empty chatId');
    return { success: false, messages: [], total_count: 0 };
  }
  try {
    console.log(`📤 /apiv1/chats/${chatId}/messages/ isteği gönderiliyor`);
    const response = await api.get<ApiResponse<ChatHistoryResponse>>(`/chats/${chatId}/messages/`, {
      params: { limit, skip },
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    console.log('✅ Chat geçmişi alındı:', response.data);
    
    if (response.data.error) {
      console.error('❌ Chat geçmişi alma hatası:', response.data.error);
      return {
        success: false,
        messages: [],
        total_count: 0
      };
    }
    
    const data = response.data.data!;
    const messagesWithParsedTimestamps = data.messages.map(msg => ({
      ...msg,
      timestamp: msg.timestamp // Assuming backend sends milliseconds already
    }));
    
    return { 
      success: true,
      messages: messagesWithParsedTimestamps,
      total_count: data.total_count
    };
  } catch (error: any) {
    console.error('❌ Chat geçmişi alma hatası:', error);
    return {
      success: false,
      messages: [],
      total_count: 0
    };
  }
};

/**
 * Uploads a file to a specific chat.
 * @param chatId The chat ID to upload the file to.
 * @param userId The ID of the user uploading the file.
 * @param file The file to upload.
 * @param fileType The type of the file ('csv', 'excel', 'PDF', 'DOC', 'DOCX').
 * @returns The response data from the API.
 */
export const uploadFile = async (
  chatId: string,
  userId: string,
  file: File,
  fileType: 'csv' | 'excel' | 'PDF' | 'DOC' | 'DOCX'
): Promise<{message: string; filename: string; chat_id: string; task_key?: string}> => {
  try {
    console.log('📤 /apiv1/chats/:chat_id/files/ isteği gönderiliyor');
    
    const formData = new FormData();
    formData.append('user_id', userId);
    formData.append('file_type', fileType);
    formData.append('file', file);
    
    const response = await api.post<ApiResponse<{message: string; filename: string; chat_id: string}>>(
      `/chats/${chatId}/files/`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    console.log('✅ Dosya yüklendi:', response.data);
    
    if (response.data.error) {
      throw new Error(response.data.error.message);
    }
    
    return response.data.data!;
  } catch (error) {
    console.error('❌ Dosya yükleme hatası:', error);
    throw error;
  }
};

// Remove old analyze and status endpoints since they're replaced by the new chat-based endpoints

/**
 * Start analysis - Deprecated, use sendMessageToAgent instead
 * @deprecated Use sendMessageToAgent instead
 */
export const startAnalysis = async (question: string): Promise<StatusResponse> => {
  console.warn('⚠️ startAnalysis is deprecated. Use sendMessageToAgent instead.');
  
  // Return a test response for backward compatibility
  return {
    task_id: 'deprecated-' + Date.now(),
    status: 'completed',
    message: 'Bu fonksiyon artık kullanılmıyor. Lütfen yeni chat tabanlı API kullanın.'
  };
};

/**
 * Check analysis status - Deprecated
 * @deprecated Use WebSocket connection or polling chat messages instead
 */
export const getAnalysisStatus = async (taskId: string): Promise<StatusResponse | null> => {
  console.warn('⚠️ getAnalysisStatus is deprecated. Use WebSocket or polling instead.');
  return null;
};

/**
 * Downloads a report file from a specific chat.
 * @param chatId The chat ID to download the file from.
 * @param fileName The name of the file to download.
 * @param userId The ID of the user downloading the file.
 * @returns Blob for file download
 */
export const downloadReportFile = async (
  chatId: string,
  fileName: string,
  userId: string
): Promise<Blob> => {
  try {
    console.log('📤 /apiv1/chats/:chat_id/reports/:file_name isteği gönderiliyor');
    
    const response = await api.get(`/chats/${chatId}/reports/${fileName}`, {
      params: { user_id: userId },
      responseType: 'blob', // Important for file downloads
    });
    
    console.log('✅ Dosya indirildi:', fileName);
    return response.data;
  } catch (error) {
    console.error('❌ Dosya indirme hatası:', error);
    throw error;
  }
};

export default api; // Export the configured axios instance if needed elsewhere 