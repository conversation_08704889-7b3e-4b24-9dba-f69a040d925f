import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale, // Keep for non-time category axes
  LinearScale,   // For Y-axis or non-time X-axis
  TimeScale,     // Import TimeScale
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement,
  ArcElement
} from 'chart.js';
import 'chartjs-adapter-date-fns'; // Import the date-fns adapter
import { Bar, Line, Pie, Scatter } from 'react-chartjs-2';
import Box from '@mui/material/Box';

// Register the necessary components for Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  TimeScale,     // Register TimeScale
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ChartDisplayProps {
  chartType: 'bar' | 'line' | 'pie' | 'scatter';
  data: any; // Chart.js data object
  options?: any; // Chart.js options object
}

const ChartDisplay: React.FC<ChartDisplayProps> = ({ chartType, data, options }) => {
  // Default options if none provided
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false, // Allow chart to fit container height
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Chart', // Default title
      },
    },
    // Ensure scales are defined, especially if options might not include them
    scales: {
      x: { // Default x-axis settings
          // No specific type here allows Chart.js to infer or use CategoryScale by default
      },
      y: { // Default y-axis settings
        beginAtZero: true
      }
    }
  };

  // Deep merge options might be better if options structure is complex
  // For now, simple spread should work for basic overrides
  const finalOptions = {
      ...defaultOptions,
      ...options,
      // Ensure nested structures like plugins and scales are merged reasonably
      plugins: { ...defaultOptions.plugins, ...options?.plugins },
      scales: { ...defaultOptions.scales, ...options?.scales }
  };

  let ChartComponent;
  switch (chartType) {
    case 'line':
      ChartComponent = Line;
      break;
    case 'pie':
      // Pie charts don't typically use X/Y scales, remove them from options if needed
      delete finalOptions.scales;
      ChartComponent = Pie;
      break;
    case 'scatter':
      ChartComponent = Scatter;
      break;
    case 'bar':
    default:
      ChartComponent = Bar;
      break;
  }

  return (
    <Box sx={{ height: '400px', width: '100%', p: 1 }}> {/* Set a default height */}
      <ChartComponent options={finalOptions} data={data} />
    </Box>
  );
};

export default ChartDisplay;