import React from 'react';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

const Navbar: React.FC = () => {
  return (
    <AppBar 
      position="static" // Or "fixed" if you want it always visible on scroll
      color="inherit" // Use inherit to match theme background, or primary/default
      elevation={1} // Add a subtle shadow like paper
      sx={{ 
        bgcolor: 'background.paper', // Explicitly set background to paper (white)
        zIndex: (theme) => theme.zIndex.drawer + 1 // Ensure it's above side panels if they become drawers
      }}
    >
      <Toolbar variant="dense"> {/* Use dense variant for a shorter navbar */}
        <Typography 
          variant="h6" 
          noWrap 
          component="div" 
          sx={{ 
            fontWeight: 'bold', 
            color: 'primary.main' // Use primary color for the logo text
          }}
        >
          KAI by QmindLab
        </Typography>
        {/* Add other navbar items here if needed later (e.g., user menu) */}
        <Box sx={{ flexGrow: 1 }} /> {/* Pushes items to the right */}
      </Toolbar>
    </AppBar>
  );
};

export default Navbar; 