"""
Common API schemas for standardized responses
"""

from typing import Type<PERSON><PERSON>, Generic, Optional, Any, Dict
from pydantic import BaseModel, validator
from pydantic.generics import GenericModel

# Try to import ErrorCode, fallback if not available
try:
    from src.api.common.error_codes import <PERSON>rrorCode
except ImportError:
    # Fallback if error_codes module doesn't exist
    from enum import Enum
    class ErrorCode(str, Enum):
        INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
        INVALID_REQUEST = "INVALID_REQUEST"

T = TypeVar('T')

class ErrorDetail(BaseModel):
    """Error detail structure"""
    code: str
    message: str
    field: Optional[str] = None
    target: Optional[str] = None  # Parameter or entity that caused the error
    details: Optional[Dict[str, Any]] = None

class ApiResponse(GenericModel, Generic[T]):
    """Standard API response wrapper with generic typing"""
    success: bool = True
    data: Optional[T] = None
    message: Optional[str] = None
    error: Optional[ErrorDetail] = None
    request_id: Optional[str] = None  # Populated by middleware

    class Config:
        arbitrary_types_allowed = True

# Backward compatibility - simple ApiResponse for cases that don't need generics
class SimpleApiResponse(BaseModel):
    """Simple API response wrapper for backward compatibility"""
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None
    error: Optional[str] = None

class HealthStatusData(BaseModel):
    """Health check response data"""
    status: str
    version: str
    timestamp: Optional[str] = None
    async_db_available: Optional[bool] = None
    rate_limiter_available: Optional[bool] = None
