from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import os
import logging

# Load environment variables
load_dotenv()

# Get database connection string from environment variables
DB_CONNECTION_STRING = os.getenv("DATABASE_URL", "postgresql://admin:adminpassword@localhost:5432/userdb_template")

logger = logging.getLogger(__name__)

class Database:
    """Database connection and session management (Synchronous)"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance

    def __init__(self, db_uri: str = None):
        if not self.initialized:
            self.engine = create_engine(db_uri or DB_CONNECTION_STRING)
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,  # Prevent detached instance errors
                bind=self.engine
            )
            self.initialized = True

    def initialize_db(self):
        """Initialize database tables if they don't exist (Synchronous)"""
        from .models import Base

        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    def get_session(self):
        """Get a new database session (Synchronous)"""
        return self.SessionLocal()

def get_db(db_uri: str = None) -> Database:
    """Get the database instance with optional custom connection URI (Synchronous)"""
    db = Database()
    if db_uri and not db.initialized:
        db.__init__(db_uri)
    return db

def initialize_db() -> None:
    """Initialize the database tables (Synchronous)"""
    db = get_db()
    db.initialize_db()

def get_session():
    """Get a new database session (Synchronous)"""
    return get_db().get_session()