"""
PostgreSQL models for chat history storage
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, List, Optional, Any

Base = declarative_base()

class UserChatInfo(Base):
    """
    User chat information model to track chat sessions
    """
    __tablename__ = 'user_chat_info'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(50), nullable=False, index=True)
    chat_id = Column(String(50), nullable=False, unique=True, index=True)
    chat_name = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    # Relationships
    messages = relationship("UserChatHistory", back_populates="chat_info", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<UserChatInfo(id={self.id}, user_id='{self.user_id}', chat_id='{self.chat_id}')>"


class UserChatHistory(Base):
    """
    User chat history model to store conversation messages in CrewAI memory format
    """
    __tablename__ = 'user_chat_history'

    id = Column(Integer, primary_key=True)
    chat_id = Column(String(50), ForeignKey("user_chat_info.chat_id"), nullable=False, index=True)
    role = Column(String(20), nullable=False)  # 'user', 'assistant', 'system', etc.
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    message_metadata = Column(JSON, nullable=True)  # Additional metadata for CrewAI memory

    # Relationships
    chat_info = relationship("UserChatInfo", back_populates="messages")

    def __repr__(self):
        return f"<UserChatHistory(id={self.id}, chat_id='{self.chat_id}', role='{self.role}')>"

    def to_crewai_memory_format(self) -> Dict[str, Any]:
        """
        Convert the database record to CrewAI memory format

        Returns:
            Dict[str, Any]: Message in CrewAI memory format
        """
        return {
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.message_metadata or {}
        }
