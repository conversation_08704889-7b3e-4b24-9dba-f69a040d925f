import * as XLSX from 'xlsx';

export interface ParsedExcel {
  headers: string[];
  rows: (string | number | boolean | Date | null)[][];
}

/**
 * Parses an Excel file from an ArrayBuffer.
 * @param arrayBuffer The ArrayBuffer of the Excel file.
 * @returns A promise that resolves to the parsed data.
 */
export const parseExcel = (arrayBuffer: ArrayBuffer): Promise<ParsedExcel> => {
  return new Promise((resolve, reject) => {
    try {
      const workbook = XLSX.read(arrayBuffer, { type: 'buffer', cellDates: true });
      
      // Assume we're interested in the first sheet
      const firstSheetName = workbook.SheetNames[0];
      if (!firstSheetName) {
        throw new Error('Excel file contains no sheets.');
      }
      
      const worksheet = workbook.Sheets[firstSheetName];
      
      // Convert sheet to JSON array of arrays, preserving blank values
      const data: any[][] = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: null // Preserve blank cells as null
      });
      
      if (data.length === 0) {
        resolve({ headers: [], rows: [] });
        return;
      }
      
      // Extract headers from the first row
      const headers = data[0].map(header => (header !== null && header !== undefined) ? String(header) : '');
      
      // The rest of the data is rows
      const rows = data.slice(1);
      
      resolve({ headers, rows });
      
    } catch (error) {
      console.error('Error parsing Excel file:', error);
      reject(error);
    }
  });
}; 