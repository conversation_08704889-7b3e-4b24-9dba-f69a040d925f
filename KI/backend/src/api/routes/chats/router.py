"""
Chat management endpoints, including file uploads, analysis, messaging, and task status within a chat.
"""

# FastAPI and Pydantic
from fastapi import (
    APIRouter, status, File, UploadFile, Form,
    WebSocket, WebSocketDisconnect, Query, HTTPException, Request
)
from starlette.websockets import WebSocketState
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from typing import Optional, Literal, Dict, List, Any

# Standard Library
import asyncio # Keep for thread_pool if used with asyncio.to_thread in future
import concurrent.futures
import os
import time # Keep
import json # Keep if used directly, else remove
import uuid # Keep for file naming
import traceback # Keep for websocket error logging
from dotenv import load_dotenv # Keep
import threading # Keep

# Database and SQLAlchemy
from sqlalchemy import select
from sqlalchemy.orm import Session
from src.rag_engine.analitic.db.postgresql.models import User
from src.rag_engine.analitic.db.postgresql.database import get_session
from src.agents.datasets.database import chat_history_db

# RAG Interface
from src.rag_engine.interface import RAGInterface

# File handling utilities
from io import BytesIO
from PyPDF2 import PdfReader
import mammoth
from docx import Document
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Common API Schemas & Exceptions
from src.api.common.schemas import ApiResponse
from src.api.common.exceptions import StandardAPIException
from src.api.common.error_codes import ErrorCode

# Chat-specific components - Agent System  
from src.agents.agno_agents.agent_factory import AgentFactory
from src.agents.agno_agents.agent_service import AgentService
from src.api.routes.chats.models import Task, TaskStatus, TaskResult # Assuming these are still relevant for future analyze logic
from src.api.routes.chats.schemas import (
    CreateChatRequest, CreateChatResponse,      # CreateChatResponse is now data
    AnalyzeRequest, AnalyzeData,                # AnalyzeData (formerly AnalyzeResponse)
    # TaskStatusRequest, TaskStatusData,        # TaskStatus not in current router
    ChatHistoryData,                           # ChatHistoryData (formerly ChatHistoryResponse)
    Plan, ChatMessage, AgentTask,              # Plan, ChatMessage and AgentTask are sub-models
    UploadResponseData,                        # New schema for upload response data
    ChatAssetData
)
from src.api.routes.chats.services import  *
# Chat service (for storing/retrieving messages)
from src.api.routes.chats.chat_service import *
# WebSocket and Redis
from src.api.routes.chats.websocket import connection_manager
from src.api.routes.chats.redis_connector import get_redis_client, create_task_key, create_chat_tasks_key
from . import pdf_report

# OpenRouter utilities
from src.api.util import is_processable_with_key, get_openrouter_credits

# --- Router Definition ---
router = APIRouter(prefix="/chats")
router.include_router(pdf_report.router)

# --- OpenRouter Credit Check Helper ---
async def check_openrouter_credits(api_key: str, threshold: float = 0.5) -> tuple[bool, str]:
    """
    Check if there are sufficient OpenRouter credits to process a request.
    
    Args:
        api_key: OpenRouter API key
        threshold: Minimum credit threshold required
        
    Returns:
        Tuple of (has_sufficient_credits: bool, message: str)
    """
    try:
        # Get detailed credit information for logging
        credits_info = get_openrouter_credits(api_key)
        
        if not credits_info.success:
            print(f"❌ OpenRouter credit check failed: {credits_info.error}")
            return False, f"Failed to check API credits: {credits_info.error}"
        
        # Log credit information
        print(f"📊 OpenRouter credits - Total: {credits_info.total_credits}, Used: {credits_info.total_usage}, Remaining: {credits_info.limit_remaining}")
        
        # Check if we have enough credits
        has_sufficient = credits_info.limit_remaining >= threshold
        
        if not has_sufficient:
            print(f"⚠️ Insufficient OpenRouter credits: {credits_info.limit_remaining} (threshold: {threshold})")
            return False, f"Insufficient API credits: {credits_info.limit_remaining} remaining (minimum required: {threshold})"
        
        return True, f"Sufficient API credits: {credits_info.limit_remaining} remaining"
        
    except Exception as e:
        print(f"❌ Error checking OpenRouter credits: {str(e)}")
        return False, f"Error checking API credits: {str(e)}"

# --- Async Agent Execution Function ---
async def run_agent_execution_async(plan, user_query: str, user_id: str, chat_id: str, task_key: str, agent_service: AgentService):
    """Run agent execution in the background using AgentService"""
    try:
        # Run the planned agents and then the output agent
        result = await agent_service.partial_run(
            plan=plan,
            user_query=user_query,
            user_id=user_id,
            chat_id=chat_id,
            task_key=task_key,
            socket_manager=connection_manager
        )
        
        # Handle backward compatibility - check if we got 3 values or 2
        if len(result) == 3:
            final_output, final_task, full_reports = result
        else:
            final_output, final_task = result
            full_reports = []
        
        # Extract markdown from the final output
        if final_output and hasattr(final_output, 'content'):
            if hasattr(final_output.content, 'markdown_response'):
                markdown_text = final_output.content.markdown_response
            else:
                markdown_text = str(final_output.content)
        else:
            markdown_text = str(final_output) if final_output else "Analysis completed"

        # Include both charts and reports in assets (with safety checks)
        assets = {}
        reports_to_store = []
        
        if final_task and final_task.output:
            assets = {
                "charts": final_task.output.get("charts", []),
                "tables": final_task.output.get("tables", [])
            }
            # Use full reports metadata (with file paths) for database storage
            reports_to_store = full_reports if full_reports else []
        else:
            print(f"⚠️  WARNING: final_task or final_task.output is None, using empty assets")
            assets = {
                "charts": [],
                "tables": []
            }
        
        # Update the agent message with final result including reports
        await update_agent_message(
            task_key=task_key,
            text=markdown_text,
            error=False,
            assets=assets,
            is_task_output=True,
            reports=reports_to_store  # Pass the reports to be stored in message assets
        )
        
    except Exception as e:
        print(f"Error in agent execution: {e}")
        # Update with error message
        await update_agent_message(
            task_key=task_key,
            text=f"Analysis error: {str(e)}",
            error=True
        )

# --- Global Instances & Config ---
interface = RAGInterface()
UPLOADS_DIR = os.path.abspath("uploads")
if not os.path.exists(UPLOADS_DIR):
    os.makedirs(UPLOADS_DIR)

load_dotenv()

thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=int(os.getenv("THREAD_POOL_MAX_WORKERS", 4)))

# --- Endpoint: Create Chat Session ---
@router.post("/", response_model=ApiResponse[CreateChatResponse], status_code=status.HTTP_201_CREATED)
async def create_chat_session_endpoint(request: CreateChatRequest):
    try:
        if not request.user_id:
            raise StandardAPIException(
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=ErrorCode.MISSING_REQUIRED_FIELD,
                field="user_id"
            )
        if request.chat_name is not None and len(request.chat_name.strip()) == 0:
            raise StandardAPIException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                error_code=ErrorCode.INVALID_PARAMETER_VALUE,
                message="Chat adı boş olamaz.",
                field="chat_name"
            )

        with get_session() as session:
            db_user = session.execute(select(User).filter(User.id == request.user_id)).scalars().first()
            if not db_user:
                raise StandardAPIException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=ErrorCode.RECORD_NOT_FOUND_IN_DB,
                    message=f"Kullanıcı bulunamadı: {request.user_id}",
                    target="User"
                )
        chat_id_val = chat_history_db.create_chat_session(request.user_id, request.chat_name)
        response_data = CreateChatResponse(
            chat_id=chat_id_val,
            user_id=request.user_id,
            chat_name=request.chat_name or "Untitled Chat"
        )
        return ApiResponse[CreateChatResponse](data=response_data)
    except StandardAPIException: # Re-raise if already a StandardAPIException
        raise
    except Exception as e:
        # Log the exception e
        print(f"Error creating chat session: {str(e)}") # Basic logging
        raise StandardAPIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCode.DATABASE_OPERATION_FAILED, # Assuming most other errors here are DB related
            message="Chat oturumu oluşturulurken bir veritabanı hatası oluştu."
        )

# --- File Handling Helper Functions (Moved from files.py) ---
# These helpers are internal and don't need ApiResponse directly unless they become endpoints.
def convert_docx_to_pdf(docx_bytes: bytes) -> bytes:
    docx_stream = BytesIO(docx_bytes); doc = Document(docx_stream); pdf_stream = BytesIO(); c = canvas.Canvas(pdf_stream, pagesize=letter); c.setFont("Helvetica-Bold", 12); y = 750
    for paragraph in doc.paragraphs: text = paragraph.text; words = text.split(); lines = []; current_line = []
    for word in words: current_line.append(word)
    if c.stringWidth(' '.join(current_line), c._fontname, c._fontsize) > 500:
        if len(current_line) > 1: lines.append(' '.join(current_line[:-1])); current_line = [word]
        else: lines.append(' '.join(current_line)); current_line = []
    if current_line: lines.append(' '.join(current_line))
    for line_text in lines:
        try: c.drawString(50, y, line_text)
        except UnicodeEncodeError:
            try: encoded_line = line_text.encode('utf-8').decode('utf-8'); c.drawString(50, y, encoded_line)
            except: safe_line = line_text.encode('ascii', 'replace').decode('ascii'); c.drawString(50, y, safe_line)
        y -= 20
        if y < 50: c.showPage(); y = 750; c.setFont("Helvetica-Bold", 12)
    c.save(); pdf_bytes = pdf_stream.getvalue(); pdf_stream.close(); docx_stream.close(); return pdf_bytes

def save_file_to_disk(user_id: str, file_bytes: bytes, file_type_disk: str, original_filename: str = None) -> str:
    user_dir = os.path.join(UPLOADS_DIR, user_id);
    if not os.path.exists(user_dir): os.makedirs(user_dir)
    # Corrected datetime import for file naming
    from datetime import datetime
    if not original_filename: timestamp = datetime.now().strftime("%Y%m%d_%H%M%S"); unique_id = str(uuid.uuid4())[:8]; extension = ".pdf" if file_type_disk == "application/pdf" else ".docx" if file_type_disk == "application/docx" else ".csv"; filename = f"chatfile_{timestamp}_{unique_id}{extension}"
    else: filename = original_filename
    file_path = os.path.join(user_dir, filename);
    with open(file_path, "wb") as f: f.write(file_bytes)
    return file_path



# --- Endpoint: Upload File to Chat ---
@router.post("/{chat_id}/files", response_model=ApiResponse[UploadResponseData], status_code=status.HTTP_200_OK)
async def upload_file_to_chat(
    chat_id: str,
    user_id: str = Form(...),
    file_type_form: Literal["csv", "PDF", "DOC", "DOCX"] = Form(..., alias="file_type"),
    file: UploadFile = File(...)
):
    print(f"Upload endpoint called - chat_id: {chat_id}, user_id: {user_id}, file_type: {file_type_form}, filename: {file.filename}")
    
    # Validate required fields first
    if not chat_id:
        raise StandardAPIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=ErrorCode.MISSING_REQUIRED_FIELD,
            message="Chat ID gerekli.",
            field="chat_id"
        )
    
    if not user_id:
        raise StandardAPIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=ErrorCode.MISSING_REQUIRED_FIELD,
            message="User ID gerekli.",
            field="user_id"
        )
    
    if not file or not file.filename:
        raise StandardAPIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=ErrorCode.MISSING_REQUIRED_FIELD,
            message="Dosya gerekli.",
            field="file"
        )
    
    if file_type_form not in ["csv", "PDF", "DOC", "DOCX"]:
        raise StandardAPIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=ErrorCode.INVALID_PARAMETER_VALUE,
            message="file_type 'csv', 'PDF', 'DOC' veya 'DOCX' olmalıdır.",
            field="file_type"
        )
    try:
        print(f"Validating chat session {chat_id} for user {user_id}")
        chat_session = chat_history_db.get_chat_info(chat_id)
        print(f"Chat session retrieved: {chat_session}")
        
        if not chat_session:
            print(f"Chat session not found: {chat_id}")
            raise StandardAPIException(
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCode.CHAT_SESSION_NOT_FOUND,
                message=f"Chat oturumu '{chat_id}' bulunamadı.",
                target="ChatSession"
            )
        if chat_session.get('user_id') != user_id:
            print(f"User {user_id} does not own chat {chat_id}. Chat owner: {chat_session.get('user_id')}")
            raise StandardAPIException(
                status_code=status.HTTP_403_FORBIDDEN,
                error_code=ErrorCode.PERMISSION_DENIED,
                message=f"Kullanıcının '{chat_id}' chat oturumuna erişim yetkisi yok.",
                target="ChatSession"
             )
        print(f"Chat session validation successful")
    except StandardAPIException:
        raise
    except Exception as e:
        print(f"Error fetching chat session {chat_id}: {e}")
        raise StandardAPIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCode.INTERNAL_SERVER_ERROR, # Could be DATABASE_OPERATION_FAILED
            message="Chat oturumu doğrulanırken bir hata oluştu."
        )

    def detect_csv_delimiter(csv_content: str) -> str:
        """Detect the delimiter used in CSV content"""
        import csv
        try:
            # Sample the first few lines to detect delimiter
            lines = csv_content.split('\n')[:5]  # Check first 5 lines
            sample = '\n'.join(lines)
            
            # Use csv.Sniffer to detect delimiter
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample, delimiters=';,\t|').delimiter
            return delimiter
        except:
            # If detection fails, check for common delimiters manually
            first_line = csv_content.split('\n')[0] if csv_content else ""
            if ';' in first_line and first_line.count(';') > first_line.count(','):
                return ';'
            elif ',' in first_line:
                return ','
            elif '\t' in first_line:
                return '\t'
            elif '|' in first_line:
                return '|'
            else:
                return ','  # Default to comma

    async def process_csv_as_chat_message(uid, csv_content_str_inner, chat_id_inner, filename_inner):
        """Process CSV file as a chat message to use the existing chat flow"""
        task_key_result = None
        try:
            print(f"Processing CSV '{filename_inner}' as chat message for chat {chat_id_inner}")

            # Step 1: Validate CSV
            from src.rag_engine.analitic.csv_validator import validate_csv_content
            delimiter = detect_csv_delimiter(csv_content_str_inner)
            print(f"Detected CSV delimiter: '{delimiter}' for chat {chat_id_inner}")
            validation_result = validate_csv_content(csv_content_str_inner, delimiter, filename_inner)

            # Step 2: Save CSV if validation passes
            if validation_result.should_save:
                print(f"CSV validation passed for {filename_inner}, saving to database")

                # FIXED: Check store_csv_data return value
                store_result = interface.store_csv_data(
                    user_id=uid,
                    csv_content=csv_content_str_inner,
                    table_name=f"chat_{chat_id_inner}_data",
                    delimiter=delimiter,
                    chat_id=chat_id_inner,
                    original_file_name=filename_inner
                )

                print(f"DEBUG: store_csv_data result: {store_result}")

                # Check if storage was actually successful
                if store_result and store_result.get("success", False):
                    user_message_text = f"📁 CSV dosyası '{filename_inner}' başarıyla yüklendi. Lütfen dosya doğrulaması yap ve temel istatistiksel analiz sun."
                    print(f"✅ CSV successfully stored: {store_result.get('table_name', 'unknown table')}")
                else:
                    error_msg = store_result.get("error", "Unknown storage error") if store_result else "No result returned"
                    user_message_text = f"❌ CSV dosyası '{filename_inner}' validation geçti ancak database'e kaydedilemedi. Hata: {error_msg}"
                    print(f"❌ CSV storage failed: {error_msg}")
            else:
                print(f"CSV validation failed for {filename_inner}, file not saved")
                user_message_text = f"📁 CSV dosyası '{filename_inner}' yüklenmeye çalışıldı ancak kritik hatalar nedeniyle kaydedilemedi. Validation sonucu: {validation_result.summary}. Lütfen hataları analiz et ve düzeltme önerileri sun."

            # Step 3: Store user message (simulating user uploaded a file)
            await store_user_message(user_id=uid, chat_id=chat_id_inner, text=user_message_text)

            # Step 4: Trigger chat analysis using existing endpoint logic
            # Create AnalyzeRequest object
            analyze_request = AnalyzeRequest(user_id=uid, input_text=user_message_text)

            # Step 5: Call the existing start_chat_analysis logic directly
            try:
                # Use the existing chat analysis logic
                redis_conn = get_redis_client()
                task_key_val = f"{chat_id_inner}_{uid}_{uuid.uuid4()}"

                kai_task = create_task(task_key=task_key_val, user_id=uid, chat_id=chat_id_inner, input_text=user_message_text, title="CSV File Analysis")
                kai_task.status = TaskStatus.PLANNING
                kai_task.metadata.created_at = time.time()

                redis_task_key_str = create_task_key(kai_task.task_key)
                redis_chat_key_str = create_chat_tasks_key(kai_task.chat_id)

                pipeline = redis_conn.pipeline()
                pipeline.set(redis_task_key_str, kai_task.model_dump_json())
                pipeline.sadd(redis_chat_key_str, kai_task.task_key)
                pipeline.execute()

                # Initialize Agent System
                factory = AgentFactory()
                agent_service = AgentService(factory)

                # Run the planner agent to get the plan
                plan = await agent_service.run_planner(
                    user_query=user_message_text,
                    user_id=uid,
                    chat_id=chat_id_inner,
                    task_key=kai_task.task_key,
                    socket_manager=connection_manager
                )

                if plan:
                    # Convert agents to storage format
                    detailed_agents_for_storage = []
                    for agent_plan in plan.agents:
                        detailed_agent_for_storage = {
                            "agent_id": agent_plan.agent_id,
                            "agent_name": agent_plan.agent_name,
                            "task": agent_plan.task_for_agent,
                            "reason": agent_plan.reason
                        }
                        detailed_agents_for_storage.append(detailed_agent_for_storage)

                    plan_reason_val = "CSV file analysis plan created"
                    await store_initial_agent_message(user_id=uid, chat_id=chat_id_inner, task_key=kai_task.task_key, plan_reason=plan_reason_val, plan_task_list=detailed_agents_for_storage)

                    # Start background execution using AgentService
                    asyncio.create_task(run_agent_execution_async(plan, user_message_text, uid, chat_id_inner, kai_task.task_key, agent_service))

                    print(f"CSV analysis started successfully for chat {chat_id_inner}")
                    task_key_result = kai_task.task_key
                else:
                    error_detail = "CSV analysis planning failed - no plan generated"
                    await store_initial_agent_message(user_id=uid, chat_id=chat_id_inner, task_key=kai_task.task_key, plan_reason="Planning failed.", plan_task_list=[])
                    await update_agent_message(task_key=kai_task.task_key, text=f"CSV analysis error: {error_detail}", error=True)
                    print(f"CSV analysis planning failed for chat {chat_id_inner}")

            except Exception as analysis_error:
                print(f"Error starting CSV analysis for chat {chat_id_inner}: {analysis_error}")
                import traceback
                print(f"Analysis error traceback: {traceback.format_exc()}")

                # Store error message
                error_message_text = f"❌ CSV analizi başlatılırken hata oluştu: {str(analysis_error)}"
                await store_user_message(user_id=uid, chat_id=chat_id_inner, text=error_message_text)

        except Exception as e_thread:
            print(f"Error in process_csv_as_chat_message for chat {chat_id_inner}: {e_thread}")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

            # Store error message as user message
            try:
                error_message_text = f"❌ CSV dosyası '{filename_inner}' işlenirken hata oluştu: {str(e_thread)}"
                await store_user_message(user_id=uid, chat_id=chat_id_inner, text=error_message_text)
            except Exception as store_error:
                print(f"Error storing error message: {store_error}")

        return task_key_result

    def process_csv_in_thread_wrapper(uid, csv_content_str_inner, chat_id_inner, filename_inner):
        """Thread wrapper for async CSV processing"""
        task_key_result = None
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the async function
            task_key_result = loop.run_until_complete(
                process_csv_as_chat_message(uid, csv_content_str_inner, chat_id_inner, filename_inner)
            )

            loop.close()
            print(f"CSV processing completed for chat {chat_id_inner}, task_key: {task_key_result}")

        except Exception as e:
            print(f"Error in CSV thread wrapper for chat {chat_id_inner}: {e}")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

        return task_key_result

    def process_csv_in_thread(uid, csv_content_str_inner, chat_id_inner, filename_inner):
        try:
            # Import validation module
            from src.rag_engine.analitic.csv_validator import validate_csv_content
            from src.api.routes.chats.websocket import connection_manager
            import asyncio

            # Auto-detect the CSV delimiter
            delimiter = detect_csv_delimiter(csv_content_str_inner)
            print(f"Detected CSV delimiter: '{delimiter}' for chat {chat_id_inner}")

            # Validate CSV before processing
            validation_result = validate_csv_content(csv_content_str_inner, delimiter, filename_inner)
            print(f"CSV validation result for {filename_inner}: {validation_result.summary}")

            # Prepare validation message for WebSocket
            validation_message = {
                "type": "csv_validation",
                "filename": filename_inner,
                "is_valid": validation_result.is_valid,
                "should_save": validation_result.should_save,
                "level": validation_result.overall_level.value,
                "summary": validation_result.summary,
                "stats": validation_result.stats,
                "issues_count": len(validation_result.issues)
            }

            # Only proceed if validation allows saving
            if validation_result.should_save:
                # FIXED: Check store_csv_data return value
                store_result = interface.store_csv_data(
                    user_id=uid,
                    csv_content=csv_content_str_inner,
                    table_name=f"chat_{chat_id_inner}_data",
                    delimiter=delimiter,
                    chat_id=chat_id_inner,
                    original_file_name=filename_inner
                )

                print(f"DEBUG: store_csv_data result: {store_result}")

                # Check if storage was actually successful
                if store_result and store_result.get("success", False):
                    print(f"✅ CSV successfully stored: {store_result.get('table_name', 'unknown table')}")
                    validation_message["saved"] = True
                    validation_message["message"] = f"✅ CSV dosyası '{filename_inner}' başarıyla kaydedildi ve analiz için hazır."
                else:
                    error_msg = store_result.get("error", "Unknown storage error") if store_result else "No result returned"
                    print(f"❌ CSV storage failed: {error_msg}")
                    validation_message["saved"] = False
                    validation_message["message"] = f"❌ CSV dosyası '{filename_inner}' validation geçti ancak database'e kaydedilemedi. Hata: {error_msg}"

                # Automatically trigger CSV Validator agent
                try:
                    from src.agents.agno_agents.agent_factory import get_agent
                    from src.agents.agno_agents.tool_manager import get_all_tool_objects_map

                    # Get CSV Validator agent
                    csv_validator_agent = get_agent("csv_validator", uid)
                    if csv_validator_agent:
                        # Get tools for the agent
                        tools_map = get_all_tool_objects_map(uid)

                        # Create validation task message
                        validation_task = f"CSV dosyası '{filename_inner}' yüklendi. Lütfen dosyanın kalitesini kontrol et ve kullanıcıya detaylı rapor sun."

                        # Run CSV validation agent
                        agent_response = csv_validator_agent.run(validation_task, tools=list(tools_map.values()))
                        print(f"CSV Validator agent response: {agent_response}")

                        # Send agent response via WebSocket
                        agent_message = {
                            "type": "csv_validation_report",
                            "filename": filename_inner,
                            "agent_response": agent_response,
                            "message": f"📊 CSV Validator Agent Raporu:\n\n{agent_response}"
                        }

                        # Send agent validation report
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(
                            connection_manager.broadcast_to_chat(chat_id_inner, agent_message)
                        )
                        loop.close()
                        print(f"CSV Validator agent report sent for chat {chat_id_inner}")

                except Exception as agent_error:
                    print(f"Error running CSV Validator agent: {agent_error}")

            else:
                print(f"CSV validation failed for {filename_inner}, file not saved")
                validation_message["saved"] = False
                validation_message["message"] = f"❌ CSV dosyası '{filename_inner}' kritik hatalar nedeniyle kaydedilemedi."

            # Always trigger CSV Validator agent for detailed report
            try:
                from src.agents.agno_agents.agent_factory import AgentFactory
                from src.agents.agno_agents.tool_manager import AgnoToolManager

                # Get CSV Validator agent
                agent_factory = AgentFactory()
                csv_validator_agent = agent_factory.get_agent("csv_validator")
                if csv_validator_agent:
                    # Get tools for the agent
                    tool_manager = AgnoToolManager()
                    tools_map = tool_manager.get_all_tool_objects_map()

                    # Create validation task message based on result with detailed validation info
                    if validation_result.should_save:
                        # Add basic statistics for successful uploads
                        import pandas as pd
                        import io
                        try:
                            df = pd.read_csv(io.StringIO(csv_content_str_inner), delimiter=delimiter)

                            # Basic statistics
                            row_count = len(df)
                            col_count = len(df.columns)
                            column_names = list(df.columns)

                            # Data type analysis
                            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
                            text_cols = df.select_dtypes(include=['object']).columns.tolist()

                            # Dynamic business insights based on data patterns
                            insights = []

                            # Smart column detection patterns
                            def detect_column_type(col_name, col_data):
                                """Dynamically detect column purpose based on name patterns and data"""
                                col_lower = col_name.lower()

                                # ID patterns (customer, user, account, etc.)
                                id_patterns = ['id', 'customer', 'müşteri', 'user', 'kullanıcı', 'account', 'hesap', 'client', 'müvekkil']

                                # Amount/Price patterns
                                amount_patterns = ['price', 'fiyat', 'amount', 'tutar', 'cost', 'maliyet', 'total', 'toplam', 'value', 'değer', 'revenue', 'gelir', 'sales', 'satış', 'fee', 'ücret', 'charge', 'ödeme']

                                # Date patterns
                                date_patterns = ['date', 'tarih', 'time', 'zaman', 'created', 'oluşturulma', 'updated', 'güncelleme', 'timestamp', 'day', 'gün', 'month', 'ay', 'year', 'yıl']

                                # Quantity patterns
                                quantity_patterns = ['quantity', 'miktar', 'count', 'sayı', 'number', 'adet', 'piece', 'parça', 'volume', 'hacim']

                                # Category patterns
                                category_patterns = ['category', 'kategori', 'type', 'tip', 'class', 'sınıf', 'group', 'grup', 'segment', 'bölüm', 'product', 'ürün', 'service', 'hizmet']

                                # Location patterns
                                location_patterns = ['city', 'şehir', 'region', 'bölge', 'country', 'ülke', 'location', 'konum', 'address', 'adres', 'state', 'eyalet']

                                if any(pattern in col_lower for pattern in id_patterns):
                                    return 'id'
                                elif any(pattern in col_lower for pattern in amount_patterns) and col_data.dtype in ['int64', 'float64']:
                                    return 'amount'
                                elif any(pattern in col_lower for pattern in date_patterns):
                                    return 'date'
                                elif any(pattern in col_lower for pattern in quantity_patterns) and col_data.dtype in ['int64', 'float64']:
                                    return 'quantity'
                                elif any(pattern in col_lower for pattern in category_patterns):
                                    return 'category'
                                elif any(pattern in col_lower for pattern in location_patterns):
                                    return 'location'
                                else:
                                    return 'other'

                            # Analyze each column dynamically
                            column_analysis = {}
                            for col in df.columns:
                                col_type = detect_column_type(col, df[col])
                                if col_type not in column_analysis:
                                    column_analysis[col_type] = []
                                column_analysis[col_type].append(col)

                            # Generate insights based on detected patterns
                            if 'id' in column_analysis:
                                for id_col in column_analysis['id']:
                                    unique_count = df[id_col].nunique()
                                    insights.append(f"Benzersiz {id_col} sayısı: {unique_count:,}")

                            if 'amount' in column_analysis:
                                for amount_col in column_analysis['amount']:
                                    if df[amount_col].dtype in ['int64', 'float64']:
                                        total_amount = df[amount_col].sum()
                                        avg_amount = df[amount_col].mean()
                                        insights.append(f"{amount_col} - Toplam: {total_amount:,.2f}, Ortalama: {avg_amount:,.2f}")

                            if 'quantity' in column_analysis:
                                for qty_col in column_analysis['quantity']:
                                    if df[qty_col].dtype in ['int64', 'float64']:
                                        total_qty = df[qty_col].sum()
                                        insights.append(f"{qty_col} - Toplam miktar: {total_qty:,}")

                            if 'date' in column_analysis:
                                insights.append(f"Tarih sütunları mevcut ({', '.join(column_analysis['date'])}) - zaman serisi analizi yapılabilir")

                            if 'category' in column_analysis:
                                for cat_col in column_analysis['category']:
                                    unique_categories = df[cat_col].nunique()
                                    insights.append(f"{cat_col} - {unique_categories} farklı kategori")

                            if 'location' in column_analysis:
                                for loc_col in column_analysis['location']:
                                    unique_locations = df[loc_col].nunique()
                                    insights.append(f"{loc_col} - {unique_locations} farklı lokasyon")

                            basic_stats = f"\n\nTemel İstatistikler:\n- Satır sayısı: {row_count:,}\n- Sütun sayısı: {col_count}\n- Sütunlar: {', '.join(column_names)}\n- Sayısal sütunlar: {', '.join(numeric_cols) if numeric_cols else 'Yok'}\n- Metin sütunları: {', '.join(text_cols) if text_cols else 'Yok'}"

                            if insights:
                                basic_stats += f"\n\nİş Analizleri:\n- " + "\n- ".join(insights)

                            # Dynamic analysis suggestions based on detected data patterns
                            analysis_suggestions = [
                                "Tanımlayıcı istatistikler ve veri profilleme",
                                "Veri kalitesi analizi ve eksik değer tespiti"
                            ]

                            if len(numeric_cols) >= 2:
                                analysis_suggestions.append("Korelasyon analizi (sayısal değişkenler arası ilişki)")

                            if len(numeric_cols) >= 1:
                                analysis_suggestions.append("Anomali tespiti (aykırı değer analizi)")

                            if 'id' in column_analysis and 'amount' in column_analysis:
                                analysis_suggestions.append("Müşteri segmentasyonu ve RFM analizi")
                                analysis_suggestions.append("Müşteri yaşam boyu değeri (CLTV) analizi")

                            if 'date' in column_analysis:
                                analysis_suggestions.append("Zaman serisi analizi ve trend tespiti")
                                analysis_suggestions.append("Mevsimsellik analizi")

                            if 'category' in column_analysis:
                                analysis_suggestions.append("Kategori bazlı performans analizi")

                            if 'location' in column_analysis:
                                analysis_suggestions.append("Coğrafi analiz ve bölgesel performans")

                            if len(numeric_cols) >= 1 and len(text_cols) >= 1:
                                analysis_suggestions.append("Tahmin modelleme ve makine öğrenmesi")

                            if 'id' in column_analysis and 'date' in column_analysis:
                                analysis_suggestions.append("Churn (müşteri kaybı) analizi")

                            analysis_suggestions.extend([
                                "İnteraktif görselleştirme ve dashboard",
                                "Detaylı raporlama ve sunum"
                            ])

                            analysis_suggestions_text = f"\n\nYapılabilecek Analizler:\n- " + "\n- ".join(analysis_suggestions)

                        except Exception as e:
                            basic_stats = f"\n\nTemel istatistikler alınamadı: {str(e)}"
                            analysis_suggestions_text = ""

                        validation_task = f"""CSV dosyası '{filename_inner}' başarıyla yüklendi ve kaydedildi.{basic_stats}{analysis_suggestions_text}

ÖNEMLİ: Bu dosya başarıyla kaydedildi. validate_uploaded_csv tool'unu kullanarak veritabanından detaylı analiz yap.

GÖREVLER:
1. Dosyanın temel istatistiklerini sun
2. İş analizleri yap (müşteri sayısı, toplam tutar, vs.)
3. En çok/en az değerleri bul
4. Tarih aralığını belirle
5. Yapılabilecek analizleri öner

Kullanıcıya kapsamlı bir özet rapor sun."""
                    else:
                        # Include detailed validation results and data samples for failed uploads
                        issues_detail = ""
                        if validation_result.issues:
                            issues_detail = "\n\nDetaylı hatalar:\n"
                            for issue in validation_result.issues:
                                issues_detail += f"- {issue.level.value}: {issue.column} - {issue.message}"
                                if issue.examples:
                                    issues_detail += f" (Örnekler: {', '.join(map(str, issue.examples[:3]))})"
                                issues_detail += "\n"

                        # Add data samples for better analysis
                        import pandas as pd
                        import io
                        try:
                            print(f"DEBUG: Attempting to parse CSV for data samples. Delimiter: '{delimiter}'")
                            df = pd.read_csv(io.StringIO(csv_content_str_inner), delimiter=delimiter)
                            print(f"DEBUG: Successfully parsed CSV. Shape: {df.shape}")

                            # Get head 10 rows
                            head_sample = df.head(10).to_string(index=False, max_cols=None, max_colwidth=50)
                            print(f"DEBUG: Head sample created. Length: {len(head_sample)}")

                            # Get random sample (smaller for token limits)
                            sample_size = min(20, len(df))  # Reduced from 50 to 20 for token limits
                            if len(df) > 10:
                                random_sample = df.sample(n=sample_size, random_state=42).to_string(index=False, max_cols=None, max_colwidth=50)
                                print(f"DEBUG: Random sample created. Size: {sample_size}, Length: {len(random_sample)}")
                            else:
                                random_sample = "Dosya çok küçük, random sample alınamadı."
                                print("DEBUG: File too small for random sampling")

                            data_samples = f"\n\nVeri Örnekleri:\n\n=== İLK 10 SATIR ===\n{head_sample}\n\n=== RANDOM {sample_size} SATIR ÖRNEĞİ ===\n{random_sample}"
                            print(f"DEBUG: Data samples prepared. Total length: {len(data_samples)}")

                        except Exception as e:
                            print(f"DEBUG: Error creating data samples: {str(e)}")
                            data_samples = f"\n\nVeri örnekleri alınamadı: {str(e)}"

                        validation_task = f"CSV dosyası '{filename_inner}' yüklenmeye çalışıldı ancak kritik hatalar nedeniyle kaydedilemedi. Validation sonucu: {validation_result.summary}{issues_detail}{data_samples}\n\nLütfen bu hataları gerçek veri örnekleri ile analiz et ve kullanıcıya detaylı düzeltme önerileri sun."

                        print(f"DEBUG: Validation task message length: {len(validation_task)}")
                        print(f"DEBUG: Validation task preview (first 500 chars): {validation_task[:500]}...")

                    # Run CSV validation agent with enhanced data samples
                    if not validation_result.should_save:
                        # For failed uploads, create data samples directly
                        try:
                            import pandas as pd
                            import io

                            print(f"DEBUG: Creating direct data samples for {filename_inner}")
                            df = pd.read_csv(io.StringIO(csv_content_str_inner), delimiter=delimiter)
                            print(f"DEBUG: CSV parsed successfully. Shape: {df.shape}")

                            # Create simplified data samples
                            head_sample = df.head(5).to_string(index=False, max_cols=8, max_colwidth=30)

                            # Get a few random samples
                            if len(df) > 5:
                                random_sample = df.sample(n=min(5, len(df)), random_state=42).to_string(index=False, max_cols=8, max_colwidth=30)
                            else:
                                random_sample = "Dosya çok küçük"

                            enhanced_task = f"""CSV dosyası '{filename_inner}' kaydedilemedi - kritik hatalar var.

ÖNEMLİ: Bu dosya veritabanında YOK. validate_uploaded_csv tool'unu KULLANMA!

DOSYA BİLGİLERİ:
- Dosya: {filename_inner}
- Satır sayısı: {len(df):,}
- Sütun sayısı: {len(df.columns)}
- Sütunlar: {', '.join(df.columns)}

VALIDATION SONUCU:
{validation_result.summary}

HATALAR:
{issues_detail}

VERİ ÖRNEKLERİ:

İLK 5 SATIR:
{head_sample}

RANDOM 5 SATIR:
{random_sample}

Bu gerçek veri örneklerini kullanarak kullanıcıya spesifik düzeltme önerileri sun. Veritabanına erişme!"""

                            print(f"DEBUG: Enhanced task created, length: {len(enhanced_task)}")

                        except Exception as e:
                            print(f"DEBUG: Error creating data samples: {str(e)}")
                            enhanced_task = f"""CSV dosyası '{filename_inner}' kaydedilemedi.

VALIDATION SONUCU: {validation_result.summary}
HATALAR: {issues_detail}

Veri örnekleri alınamadı: {str(e)}

Genel düzeltme önerileri sun."""

                        # For failed uploads, use only ReasoningTools (no database access)
                        from agno.tools.reasoning import ReasoningTools
                        reasoning_tools = ReasoningTools()
                        agent_response = csv_validator_agent.run(enhanced_task, tools=[reasoning_tools])
                    else:
                        # For successful uploads, use all tools including database access
                        agent_response = csv_validator_agent.run(validation_task, tools=list(tools_map.values()))
                    print(f"CSV Validator agent response: {agent_response}")

                    # Send agent response via WebSocket
                    agent_message = {
                        "type": "csv_validation_report",
                        "filename": filename_inner,
                        "agent_response": agent_response,
                        "validation_level": validation_result.overall_level.value,
                        "should_save": validation_result.should_save,
                        "message": f"📊 CSV Validator Agent Raporu:\n\n{agent_response}"
                    }

                    # Send agent validation report
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        connection_manager.broadcast_to_chat(chat_id_inner, agent_message)
                    )
                    loop.close()
                    print(f"CSV Validator agent report sent for chat {chat_id_inner}")

            except Exception as agent_error:
                print(f"Error running CSV Validator agent: {agent_error}")

            # Send basic validation result via WebSocket
            try:
                # Use asyncio to send WebSocket message from thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    connection_manager.broadcast_to_chat(chat_id_inner, validation_message)
                )
                loop.close()
                print(f"Validation result sent via WebSocket for chat {chat_id_inner}")
            except Exception as ws_error:
                print(f"Error sending validation result via WebSocket: {ws_error}")

        except Exception as e_thread:
            print(f"Error in process_csv_in_thread for chat {chat_id_inner}: {e_thread}")
            # Send error message via WebSocket
            try:
                error_message = {
                    "type": "csv_validation",
                    "filename": filename_inner,
                    "is_valid": False,
                    "should_save": False,
                    "level": "error",
                    "summary": f"❌ CSV işleme hatası: {str(e_thread)}",
                    "saved": False,
                    "message": f"CSV dosyası '{filename_inner}' işlenirken hata oluştu."
                }
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    connection_manager.broadcast_to_chat(chat_id_inner, error_message)
                )
                loop.close()
            except:
                pass  # If WebSocket also fails, just log to console

    def process_document_in_thread(uid, doc_bytes_inner, detected_doc_type_inner, original_filename_inner, chat_id_inner):
        try:
            if detected_doc_type_inner == "application/docx":
                try:
                    doc_bytes_inner = convert_docx_to_pdf(doc_bytes_inner);
                    detected_doc_type_inner = "application/pdf"
                except Exception as e_convert:
                    print(f"Error converting DOCX for chat {chat_id_inner}: {str(e_convert)}");
                    return
            file_path = save_file_to_disk(uid, doc_bytes_inner, detected_doc_type_inner, original_filename_inner)
            if detected_doc_type_inner == "application/pdf":
                print(f"Indexing PDF document for chat {chat_id_inner}: {file_path}")
                interface.index_general_document(file_path, f"chat_{chat_id_inner}_documents")
            print(f"Document processing started in background for chat {chat_id_inner}")
        except Exception as e_thread:
            print(f"Error in process_document_in_thread for chat {chat_id_inner}: {e_thread}")

    try:
        task_key_for_response = None

        if file_type_form == "csv":
            # Handle both CSV and XLSX files
            if file.filename.lower().endswith('.xlsx'):
                # Handle Excel files - convert to CSV format
                try:
                    import pandas as pd
                    import io

                    # Read Excel file content
                    excel_content_bytes = await file.read()

                    # Convert Excel to DataFrame
                    excel_file = io.BytesIO(excel_content_bytes)
                    df = pd.read_excel(excel_file, engine='openpyxl')

                    # Convert DataFrame to CSV string
                    csv_content_string = df.to_csv(index=False)

                    print(f"Successfully converted Excel file '{file.filename}' to CSV format")
                    print(f"Excel file shape: {df.shape}")

                except Exception as excel_error:
                    print(f"Error processing Excel file: {excel_error}")
                    raise StandardAPIException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        error_code=ErrorCode.FILE_PROCESSING_FAILED,
                        message=f"Excel dosyası işlenirken hata oluştu: {str(excel_error)}",
                        field="file"
                    )
            else:
                # Handle regular CSV files
                csv_content_bytes = await file.read()
                try: csv_content_string = csv_content_bytes.decode('utf-8')
                except UnicodeDecodeError:
                    try: csv_content_string = csv_content_bytes.decode('latin-1')
                    except UnicodeDecodeError:
                        raise StandardAPIException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            error_code=ErrorCode.INVALID_FILE_ENCODING,
                            message="CSV dosyası UTF-8 veya Latin-1 formatında olmalıdır.",
                            field="file"
                        )

            # For both CSV and Excel files, process using the same workflow
            # Process and trigger chat analysis using async function directly
            task_key_for_response = await process_csv_as_chat_message(user_id, csv_content_string, chat_id, file.filename)

        elif file_type_form in ["PDF", "DOC", "DOCX"]:
            file_bytes_content = await file.read()
            print(f"File bytes content length: {len(file_bytes_content)}")
            print(f"Frontend specified file type: {file_type_form}")

            # Map frontend file types to internal types for processing
            if file_type_form == "PDF":
                internal_file_type = "application/pdf"
            elif file_type_form in ["DOC", "DOCX"]:
                internal_file_type = "application/docx"

            # For documents, we still use thread since they don't trigger immediate analysis
            thread = threading.Thread(target=process_document_in_thread, args=(user_id, file_bytes_content, internal_file_type, file.filename, chat_id), daemon=True)
            thread.start()

        response_data = UploadResponseData(
            message="Dosya alındı ve arka planda işlenmeye başlandı.",
            filename=file.filename,
            chat_id=chat_id,
            task_key=task_key_for_response
        )
        return ApiResponse[UploadResponseData](data=response_data)

    except StandardAPIException:
        raise
    except Exception as e:
        print(f"Error processing file upload for chat {chat_id}: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        raise StandardAPIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCode.FILE_PROCESSING_FAILED,
            message="Dosya yükleme sırasında beklenmedik bir hata oluştu."
        )

# --- Kai Endpoints (Moved and Adapted) ---
@router.post("/{chat_id}/analyze", response_model=ApiResponse[AnalyzeData])
async def start_chat_analysis(chat_id: str, request: AnalyzeRequest): # AnalyzeRequest still contains user_id and input_text
    # TODO: Add authentication and authorization to verify user_id from request / token owns/can access chat_id
    print(f"🌟 ANALYZE ENDPOINT: Starting analysis for user_id={request.user_id}, chat_id={chat_id}")
    print(f"🌟 ANALYZE ENDPOINT: Input text: {request.input_text[:100]}...")
    
    validate_request_fields(request.user_id, chat_id, request.input_text) # chat_id from path
    await verify_user_and_chat(request.user_id, chat_id) # chat_id from path

    # Check OpenRouter API credits before proceeding
    # Get API key from environment
    openrouter_api_key = os.getenv("OPENROUTER_API_KEY") or os.getenv("LLM_API_KEY")
    
    # Define minimum credit threshold (adjust as needed)
    min_credit_threshold = 0.5
    
    # Check if we have enough credits to process the request
    has_sufficient_credits, credit_message = await check_openrouter_credits(
        api_key=openrouter_api_key, 
        threshold=min_credit_threshold
    )
    
    if not has_sufficient_credits:
        error_message = f"Insufficient API credits to process this request. Please contact the administrator. ({credit_message})"
        print(f"❌ ANALYZE ENDPOINT: {error_message}")
        
        # Create a unique task key for the error message
        error_task_key = f"{chat_id}_{request.user_id}_{uuid.uuid4()}"
        
        # Store the user message and error response
        await store_user_message(user_id=request.user_id, chat_id=chat_id, text=request.input_text)
        await store_initial_agent_message(
            user_id=request.user_id, 
            chat_id=chat_id, 
            task_key=error_task_key, 
            plan_reason="Analysis failed due to insufficient API credits.", 
            plan_task_list=[]
        )
        
        # Update the agent message with the error
        await update_agent_message(
            task_key=error_task_key,
            text=error_message,
            error=True,
            is_task_output=True
        )
        
        # Return an error response
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=error_message
        )

    # Store the user message
    await store_user_message(user_id=request.user_id, chat_id=chat_id, text=request.input_text)
    
    redis_conn = get_redis_client()
    task_key_val = f"{chat_id}_{request.user_id}_{uuid.uuid4()}" # task_key includes chat_id
    
    kai_task = create_task(task_key=task_key_val, user_id=request.user_id, chat_id=chat_id, input_text=request.input_text, title="Chat Analysis Request")
    kai_task.status = TaskStatus.PLANNING
    kai_task.metadata.created_at = time.time()
    
    redis_task_key_str = create_task_key(kai_task.task_key)
    redis_chat_key_str = create_chat_tasks_key(kai_task.chat_id)
    try:
        pipeline = redis_conn.pipeline()
        pipeline.set(redis_task_key_str, kai_task.model_dump_json())
        pipeline.sadd(redis_chat_key_str, kai_task.task_key)
        pipeline.execute()
    except Exception as redis_err:

        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Task storage failed.")

    # Task updates will be sent from agent_service.py during execution
    
    try:
        # Initialize Agent System
        factory = AgentFactory()
        agent_service = AgentService(factory)
    except Exception as config_err:
        kai_task.status = TaskStatus.FAILED; kai_task.error = str(config_err)
        # Error will be handled by agent_service.py task updates
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"LLM Config error: {config_err}")

    try:
        # Run the planner agent to get the plan
        print(f"🌟 ANALYZE ENDPOINT: Calling agent_service.run_planner with user_id={request.user_id}")
        plan = await agent_service.run_planner(
            user_query=request.input_text,
            user_id=request.user_id,
            chat_id=chat_id,
            task_key=kai_task.task_key,
            socket_manager=connection_manager
        )
        print(f"🌟 ANALYZE ENDPOINT: Planner returned plan with {len(plan.agents) if plan else 0} agents")
        if not plan:
            error_detail = "Planning failed - no plan generated"
            await store_initial_agent_message(user_id=request.user_id, chat_id=chat_id, task_key=kai_task.task_key, plan_reason="Planning failed.", plan_task_list=[])
            await update_agent_message(task_key=kai_task.task_key, text=f"Analysis error: {error_detail}", error=True)
            return ApiResponse[AnalyzeData](
                data=AnalyzeData(
                    task_key=kai_task.task_key
                )
            )

        # Convert agents to both formats: detailed objects for storage and simple strings for backward compatibility
        detailed_agents_for_storage = []
        task_list_val = []
        detailed_agents = []
        
        for agent_plan in plan.agents:
            # Create detailed agent object for storage (this will be stored in the database)
            detailed_agent_for_storage = {
                "agent_id": agent_plan.agent_id,
                "agent_name": agent_plan.agent_name,
                "task": agent_plan.task_for_agent,
                "reason": agent_plan.reason
            }
            detailed_agents_for_storage.append(detailed_agent_for_storage)
            
            # Create a readable string representation for backward compatibility
            task_string = f"{agent_plan.agent_name}: {agent_plan.task_for_agent}"
            task_list_val.append(task_string)
            
            # Create detailed agent task object for API response
            agent_task = AgentTask(
                agent_id=agent_plan.agent_id,
                agent_name=agent_plan.agent_name,
                task=agent_plan.task_for_agent,
                reason=agent_plan.reason
            )
            detailed_agents.append(agent_task)
            
        plan_reason_val = "Analysis plan created"
        await store_initial_agent_message(user_id=request.user_id, chat_id=chat_id, task_key=kai_task.task_key, plan_reason=plan_reason_val, plan_task_list=detailed_agents_for_storage)
        
        # Start background execution using AgentService
        loop = asyncio.get_running_loop()
        asyncio.create_task(run_agent_execution_async(plan, request.input_text, request.user_id, chat_id, kai_task.task_key, agent_service))
        
        return ApiResponse[AnalyzeData](
            data=AnalyzeData(
                task_key=kai_task.task_key,
                plan=Plan(
                    reason=plan_reason_val, 
                    task_list=task_list_val,
                    agents=detailed_agents
                )
            )
        )
    except Exception as e:
        # ... (error handling from original kai_router) ...
        error_detail = f"Task processing error: {str(e)}"; print(f"[Error] {error_detail}\n{traceback.format_exc()}")
        current_task_data = redis_conn.get(redis_task_key_str)
        if current_task_data: failed_task = Task.model_validate_json(current_task_data); failed_task.status = TaskStatus.FAILED; failed_task.error = error_detail; redis_conn.set(redis_task_key_str, failed_task.model_dump_json())
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=error_detail)



@router.get("/{chat_id}/messages", response_model=ApiResponse[ChatHistoryData])
async def get_chat_messages_endpoint(chat_id: str, limit: Optional[int] = Query(100), skip: Optional[int] = Query(0)):
    try:
        messages, message_count = await get_chat_messages(chat_id=chat_id, limit=limit, skip=skip)
        # Ensure 'messages' contains ChatMessage instances or dicts that match ChatMessage schema
        # The get_kai_chat_messages should already return data compatible with ChatMessage schema.
        data = ChatHistoryData(messages=messages, total_count=message_count)
        return ApiResponse[ChatHistoryData](data=data)
    except StandardAPIException: # If get_kai_chat_messages raises it
        raise
    except Exception as e:
        print(f"Error fetching messages for chat {chat_id}: {str(e)}")
        raise StandardAPIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCode.DATABASE_QUERY_FAILED, # More specific than INTERNAL_SERVER_ERROR
            message=f"Chat '{chat_id}' için mesajlar alınırken bir veritabanı hatası oluştu."
        )

# WebSocket endpoint - remains largely unchanged in its core streaming logic
# Error handling during connect or specific broadcast errors could be enhanced if needed.
@router.websocket("/{chat_id}/ws")
async def chat_websocket_task_updates(websocket: WebSocket, chat_id: str):
    # TODO: Authentication for WebSocket connection based on chat_id (can raise StandardAPIException if auth fails before connect)
    try:
        await connection_manager.connect(websocket, chat_id)

        # Keep connection alive by receiving messages
        while True:
            # Check if websocket is still connected before trying to receive
            if websocket.client_state == WebSocketState.DISCONNECTED:
                print(f"WebSocket already disconnected for chat {chat_id}")
                break

            try:
                await websocket.receive_text()  # Keep connection alive
            except WebSocketDisconnect:
                print(f"WebSocket disconnected normally for chat {chat_id}")
                break
            except Exception as receive_error:
                print(f"Error receiving WebSocket message for chat {chat_id}: {str(receive_error)}")
                # If we can't receive, the connection is probably broken
                break

    except WebSocketDisconnect:
        print(f"WebSocket disconnected during connection for chat {chat_id}")
    except Exception as e:
        print(f"WebSocket error for chat {chat_id}: {str(e)}\n{traceback.format_exc()}")
    finally:
        # Always ensure cleanup
        try:
            connection_manager.disconnect(websocket, chat_id)
        except Exception as cleanup_error:
            print(f"Error during WebSocket cleanup for chat {chat_id}: {str(cleanup_error)}")

@router.get("/{chat_id}/assets", response_model=ApiResponse[ChatAssetData])
async def get_chat_assets_endpoint(chat_id: str):
    """
    Get all assets (charts, etc.) associated with a chat session.
    
    This endpoint aggregates all assets that were generated during chat interactions,
    including charts and other output types. Each asset includes its context (which task
    generated it, when it was created, etc.).
    
    Args:
        chat_id: The chat session ID
        
    Returns:
        ApiResponse containing categorized assets and metadata
    """
    try:
        assets_data = await get_chat_assets(chat_id)
        return ApiResponse[ChatAssetData](data=ChatAssetData(**assets_data))
    except Exception as e:
        print(f"Error fetching assets for chat {chat_id}: {str(e)}")
        raise StandardAPIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCode.DATABASE_QUERY_FAILED,
            message=f"Chat '{chat_id}' için varlıklar alınırken bir hata oluştu."
        )

@router.get("/{chat_id}/reports/{file_name}")
async def download_report_file(chat_id: str, file_name: str, user_id: str = Query(...)):
    """
    Download a specific report file for a chat.
    
    Args:
        chat_id: The chat ID
        file_name: The name of the file to download
        user_id: The user ID (for security validation)
    
    Returns:
        File response for download
    """
    from fastapi.responses import FileResponse
    import os
    
    try:
        # Validate that the user has access to this chat
        chat_session = chat_history_db.get_chat_info(chat_id)
        if not chat_session:
            raise StandardAPIException(
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCode.CHAT_SESSION_NOT_FOUND,
                message=f"Chat oturumu '{chat_id}' bulunamadı."
            )
        
        if chat_session.get('user_id') != user_id:
            raise StandardAPIException(
                status_code=status.HTTP_403_FORBIDDEN,
                error_code=ErrorCode.PERMISSION_DENIED,
                message="Bu chat oturumuna erişim yetkiniz yok."
            )
        
        # Try to find the file via chat assets first
        assets_data = await get_chat_assets(chat_id)
        
        # Look for the file in reports
        target_file_path = None
        for asset_type, assets in assets_data.get("assets", {}).items():
            if asset_type == "reports":
                for report in assets:
                    if report.get("data", {}).get("file_name") == file_name:
                        target_file_path = report.get("data", {}).get("file_path")
                        print(f"📁 Found report file via chat assets: {target_file_path}")
                        break
        
        # Fallback: Look for the file directly in the reports directory
        if not target_file_path:
            print(f"📁 Report not found in chat assets, checking reports directory for: {file_name}")
            
            # Check common report locations
            possible_paths = [
                os.path.join(os.getcwd(), 'reports', 'csv', file_name),
                os.path.join(os.getcwd(), 'reports', file_name),
                os.path.join(os.getcwd(), 'reports', 'excel', file_name),
                os.path.join("reports", "excel", file_name),
                os.path.join('reports', 'csv', file_name),
                os.path.join('reports', file_name)
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    target_file_path = path
                    print(f"📁 Found report file at: {target_file_path}")
                    break
        
        if not target_file_path:
            # List available files for debugging
            reports_dir = os.path.join(os.getcwd(), 'reports', 'csv')
            available_files = []
            if os.path.exists(reports_dir):
                available_files = os.listdir(reports_dir)
                print(f"📁 Available files in reports/csv: {available_files}")
            
            raise StandardAPIException(
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCode.RECORD_NOT_FOUND_IN_DB,
                message=f"Rapor dosyası '{file_name}' bulunamadı. Chat: {chat_id}"
            )
        
        # Verify file exists on disk
        if not os.path.exists(target_file_path):
            raise StandardAPIException(
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCode.RECORD_NOT_FOUND_IN_DB,
                message=f"Dosya sistemde bulunamadı: {file_name}"
            )
        
        # Return file for download
        return FileResponse(
            path=target_file_path,
            filename=file_name,
            media_type='application/octet-stream'
        )
        
    except StandardAPIException:
        raise
    except Exception as e:
        print(f"Error downloading report file: {e}")
        raise StandardAPIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            message="Dosya indirilirken bir hata oluştu."
        )

