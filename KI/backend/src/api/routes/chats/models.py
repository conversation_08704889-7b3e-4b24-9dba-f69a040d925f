"""
Task models for KAI service
"""

from enum import Enum
from typing import Dict, List, Optional, Any, DefaultDict
from pydantic import BaseModel, Field
import uuid
import time
from collections import defaultdict


class TaskStatus(str, Enum):
    """Enum for possible task statuses"""
    PENDING = "pending"
    PLANNING = "planning"
    ROUTING = "routing"
    QUEUED = "queued_for_execution"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskMetadata(BaseModel):
    """Metadata for task tracking"""
    created_at: float = Field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    input_tokens: int = 0
    output_tokens: int = 0


class TaskResult(BaseModel):
    """Result of a specific subtask execution"""
    task_name: str
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class Task(BaseModel):
    """Standardized task model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    task_key: str
    title: str
    status: TaskStatus = TaskStatus.PENDING
    description: Optional[str] = None
    user_id: str
    chat_id: str
    input_text: str
    error: Optional[str] = None
    output: Optional[Dict[str, Any]] = None
    metadata: TaskMetadata = Field(default_factory=TaskMetadata)
    router_output: Optional[Dict[str, Any]] = None
    task_list: List[str] = []
    current_task: Optional[str] = None
    task_results: List[TaskResult] = []
    reports: List[Dict[str, Any]] = Field(default_factory=list, description="Generated reports with file paths and metadata")


