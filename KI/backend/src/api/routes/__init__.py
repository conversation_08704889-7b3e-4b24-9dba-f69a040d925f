"""
API routes package
"""

from fastapi import APIRouter

# Create the main API router
api_router = APIRouter()

# Import and include other route modules
from .health import router as health_router
from .users import router as users_router
from .chats.router import router as chats_router
from .data import router as data_router


# Include all route modules
api_router.include_router(health_router, tags=["health"] )
api_router.include_router(users_router,  tags=["users"])
api_router.include_router(chats_router,  tags=["chats"])
api_router.include_router(data_router,  tags=["data"])

# api_router.include_router(kai_router, tags=["kai"]) # Functionality moved to chats.py
# api_router.include_router(files_router, tags=["files"]) # Functionality moved to chats.py