import os
import requests
from typing import Dict, Any, Optional, Union
import logging
from pydantic import BaseModel, Field

# Set up logger
logger = logging.getLogger(__name__)

# Define Pydantic models for function outputs
class OpenRouterCreditsResponse(BaseModel):
    """Pydantic model for OpenRouter credits response"""
    success: bool = Field(description="Whether the request was successful")
    total_credits: float = Field(0.0, description="Total credits purchased")
    total_usage: float = Field(0.0, description="Total credits used")
    limit_remaining: float = Field(0.0, description="Remaining credits")
    error: Optional[str] = Field(None, description="Error message if request failed")

class OpenRouterKeyDetailsResponse(BaseModel):
    """Pydantic model for OpenRouter key details response"""
    success: bool = Field(description="Whether the request was successful")
    label: str = Field("", description="API key label")
    limit: Optional[float] = Field(None, description="Credit limit for the key (or None if unlimited)")
    usage: float = Field(0.0, description="Number of credits used")
    is_free_tier: bool = Field(False, description="Whether user has paid for credits before")
    limit_remaining: Optional[float] = Field(None, description="Remaining credits")
    is_provisioning_key: bool = Field(False, description="Whether this is a provisioning key")
    error: Optional[str] = Field(None, description="Error message if request failed")

def get_openrouter_credits(api_key: Optional[str] = None) -> OpenRouterCreditsResponse:
    """
    Get available credits for an OpenRouter API key.
    
    Args:
        api_key: OpenRouter API key. If None, will try to get from environment variables.
        
    Returns:
        OpenRouterCreditsResponse object containing credit information
    """
    # Get API key from environment if not provided
    if not api_key:
        api_key = os.getenv("OPENROUTER_API_KEY")
        
    if not api_key:
        logger.error("OpenRouter API key not provided and not found in environment variables")
        return OpenRouterCreditsResponse(
            success=False,
            error="API key not provided and not found in environment variables"
        )
    
    # Prepare headers with API key
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    try:
        # Make request to OpenRouter credits endpoint
        response = requests.get(
            "https://openrouter.ai/api/v1/credits",
            headers=headers
        )
        
        # Check if request was successful
        if response.status_code == 200:
            data = response.json().get("data", {})
            total_credits = data.get("total_credits", 0.0)
            total_usage = data.get("total_usage", 0.0)
            return OpenRouterCreditsResponse(
                success=True,
                total_credits=total_credits,
                total_usage=total_usage,
                limit_remaining=total_credits - total_usage
            )
        else:
            error_message = f"Error getting OpenRouter credits: {response.status_code} - {response.text}"
            logger.error(error_message)
            return OpenRouterCreditsResponse(
                success=False,
                error=error_message
            )
            
    except Exception as e:
        error_message = f"Exception while getting OpenRouter credits: {str(e)}"
        logger.error(error_message)
        return OpenRouterCreditsResponse(
            success=False,
            error=error_message
        )
    

def is_processable_with_key(api_key: Optional[str] = None, threshold: float = 0.2) -> bool:
    """
    Check if an OpenRouter API key is capable of processing requests based on remaining credits.
    
    Args:
        api_key: OpenRouter API key. If None, will try to get from environment variables.
        threshold: Threshold for the remaining credits to be considered capable of processing requests.
        
    Returns:
        bool indicating if the key has sufficient credits for processing.
    """
    # Get API key from environment if not provided
    if not api_key:
        api_key = os.getenv("OPENROUTER_API_KEY")
        
    if not api_key:
        logger.error("OpenRouter API key not provided and not found in environment variables")
        return False
    
    # Get key details
    key_details = get_openrouter_key_details(api_key)
    
    if not key_details.success:
        logger.error(f"Error getting OpenRouter key details: {key_details.error}")
        return False
    
    # Check if the key has sufficient remaining credits
    # If limit_remaining is None, we'll use the credits endpoint as a fallback
    if key_details.limit_remaining is None:
        credits_info = get_openrouter_credits(api_key)
        if not credits_info.success:
            logger.error(f"Error getting OpenRouter credits: {credits_info.error}")
            return False
        remaining = credits_info.limit_remaining
    else:
        remaining = key_details.limit_remaining
    
    if remaining < threshold:
        return False
    
    return True

def get_openrouter_key_details(api_key: Optional[str] = None) -> OpenRouterKeyDetailsResponse:
    """
    Get detailed information about an OpenRouter API key.
    
    Args:
        api_key: OpenRouter API key. If None, will try to get from environment variables.
        
    Returns:
        OpenRouterKeyDetailsResponse object containing key details
    """
    # Get API key from environment if not provided
    if not api_key:
        api_key = os.getenv("OPENROUTER_API_KEY")
        
    if not api_key:
        logger.error("OpenRouter API key not provided and not found in environment variables")
        return OpenRouterKeyDetailsResponse(
            success=False,
            error="API key not provided and not found in environment variables"
        )
    
    # Prepare headers with API key
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    try:
        # Make request to OpenRouter key endpoint
        response = requests.get(
            "https://openrouter.ai/api/v1/key",
            headers=headers
        )
        
        # Check if request was successful
        if response.status_code == 200:
            data = response.json().get("data", {})
            
            # Handle the case where limit_remaining might be None
            limit_remaining = data.get("limit_remaining")
            
            # If limit_remaining is None but we have limit and usage, calculate it
            if limit_remaining is None and data.get("limit") is not None and data.get("usage") is not None:
                limit_remaining = data.get("limit") - data.get("usage")
            
            return OpenRouterKeyDetailsResponse(
                success=True,
                label=data.get("label", ""),
                limit=data.get("limit"),
                usage=data.get("usage", 0.0),
                is_free_tier=data.get("is_free_tier", False),
                limit_remaining=limit_remaining,
                is_provisioning_key=data.get("is_provisioning_key", False)
            )
        else:
            error_message = f"Error getting OpenRouter key details: {response.status_code} - {response.text}"
            logger.error(error_message)
            return OpenRouterKeyDetailsResponse(
                success=False,
                error=error_message
            )
            
    except Exception as e:
        error_message = f"Exception while getting OpenRouter key details: {str(e)}"
        logger.error(error_message)
        return OpenRouterKeyDetailsResponse(
            success=False,
            error=error_message
        ) 