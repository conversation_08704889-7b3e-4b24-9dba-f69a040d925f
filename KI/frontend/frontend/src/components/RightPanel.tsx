import React, { useState, useEffect, useRef } from 'react';
import IconButton from '@mui/material/IconButton';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Alert from '@mui/material/Alert';
import LinearProgress from '@mui/material/LinearProgress';
import TaskDetailItem from './TaskDetailItem';
import theme from '../app/utils/theme';
import { TaskStatus, TaskUpdate, ProgressStatus, TaskHistory, TaskHistoryItem, ChartConfig } from './types';
import { useWebSocket } from '../app/hooks/useWebSocket';

/**
 * RightPanel Component
 *
 * This component displays the real-time task status and history for user queries.
 * It connects to a WebSocket server to receive live updates on task progress,
 * and displays them in a timeline view that shows the full progression of each task.
 *
 * Features:
 * - Real-time WebSocket connection for live task updates
 * - Timeline view of task progression through different states
 * - Task history grouping by task ID
 * - Expandable/collapsible task details
 * - Display of detailed output and error information
 * - Connection status indicator
 */

const mapTaskUpdateToProgress = (taskUpdate: TaskUpdate): ProgressStatus => {
  let uiStatus: ProgressStatus['status'] = 'info';
  let message = '';
  switch (taskUpdate.status) {
    case 'pending': uiStatus = 'info'; message = 'Görev beklemede.'; break;
    case 'planning': uiStatus = 'processing'; message = 'Görev planlanıyor...'; break;
    case 'routing': uiStatus = 'processing'; message = 'Rotalama yapılıyor...'; break;
    case 'queued_for_execution': uiStatus = 'processing'; message = 'Yürütme kuyruğunda bekliyor...'; break;
    case 'executing':
      uiStatus = 'processing';
      message = taskUpdate.current_task ? `Yürütülüyor: ${taskUpdate.current_task}` : 'Görev yürütülüyor...';
      break;
    case 'completed': uiStatus = 'success'; message = 'Görev tamamlandı.'; break;
    case 'failed':
      uiStatus = 'error';
      message = taskUpdate.error ? `Hata: ${taskUpdate.error}` : 'Görev başarısız oldu.';
      break;
  }

  // Merge the reports from top-level into the output
  let enhancedOutput = taskUpdate.output;
  if (taskUpdate.reports && Array.isArray(taskUpdate.reports)) {
    enhancedOutput = {
      ...taskUpdate.output,
      reports: taskUpdate.reports
    };
    console.log('📊 RightPanel: Enhanced output with reports:', {
      reportsCount: taskUpdate.reports.length,
      reports: taskUpdate.reports,
      enhancedOutput
    });
  }

  return {
    id: `${taskUpdate.task_key}-${taskUpdate.status}-${taskUpdate.current_task || 'main'}`,
    taskId: taskUpdate.task_key,
    timestamp: taskUpdate.progress?.started_at ? taskUpdate.progress.started_at * 1000 : Date.now(),
    status: uiStatus,
    message: message,
    taskStatus: taskUpdate.status,
    currentTask: taskUpdate.current_task,
    output: enhancedOutput,
    error: taskUpdate.error,
    subtasks: taskUpdate.subtasks,
    subtaskStatus: taskUpdate.subtask_status,
    percentComplete: taskUpdate.progress?.percent_complete,
    createdAt: taskUpdate.progress?.created_at ? taskUpdate.progress.created_at * 1000 : undefined,
    startedAt: taskUpdate.progress?.started_at ? taskUpdate.progress.started_at * 1000 : undefined,
    completedAt: taskUpdate.progress?.completed_at ? taskUpdate.progress.completed_at * 1000 : undefined,
    charts: taskUpdate.output?.charts || null
  };
};

interface RightPanelProps {
  currentChatId: string;
  userId: string;
  taskKey?: string;
  initialTaskData?: {
    task_key: string;
    plan?: {
      reason: string;
      task_list: string[];
      agents?: Array<{
        agent_id: string;
        agent_name: string;
        task: string;
        reason: string;
      }>;
    };
  };
  onTaskCompletion?: (messageData: { text: string; error: boolean; taskKey: string; isTaskOutput: true }) => void;
  onCollapse?: (isCollapsed: boolean) => void;
}

const RightPanel: React.FC<RightPanelProps> = ({ currentChatId, userId, taskKey: propTaskKey, initialTaskData, onTaskCompletion, onCollapse }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [taskHistory, setTaskHistory] = useState<TaskHistory>({});
  
  // Use the WebSocket hook
  const { 
    isConnected, 
    connectionStatus, 
    lastMessage, 
    connect, 
    disconnect,
    sendMessage 
  } = useWebSocket();

  const processedCompletions = useRef<Set<string>>(new Set());
  const isInitialLoadRef = useRef<boolean>(true);
  const connectionTimeRef = useRef<number>(0);

  // Handle initial task data (from analyze response)
  useEffect(() => {
    if (initialTaskData && initialTaskData.task_key) {
      console.log('📋 RightPanel: Processing initial task data:', initialTaskData);
      
      const taskId = initialTaskData.task_key;
      const planText = initialTaskData.plan ? 
        `Plan: ${initialTaskData.plan.reason}\n\nGörevler:\n${initialTaskData.plan.task_list.map((task, i) => `${i + 1}. ${task}`).join('\n')}` : 
        'Görev planı oluşturuluyor...';
      
      const initialProgressItem: ProgressStatus = {
        id: `${taskId}-planning-initial`,
        taskId: taskId,
        timestamp: Date.now(),
        status: 'processing',
        message: 'Görev planı oluşturuldu',
        taskStatus: 'planning',
        currentTask: null,
        output: initialTaskData.plan ? { plan: initialTaskData.plan } : null,
        error: null,
        subtasks: initialTaskData.plan?.task_list || null,
        subtaskStatus: null,
        percentComplete: 10,
        createdAt: Date.now(),
        startedAt: Date.now(),
        completedAt: undefined,
        charts: null
      };
      
      setTaskHistory(prevHistory => ({
        ...prevHistory,
        [taskId]: {
          updates: [initialProgressItem],
          lastUpdate: initialProgressItem,
          isExpanded: true,
          createdAt: Date.now(),
          charts: null
        }
      }));
    }
  }, [initialTaskData]);

  // Connect to WebSocket when chat changes
  useEffect(() => {
    if (currentChatId) {
      console.log('🔌 RightPanel: Connecting to WebSocket for chat:', currentChatId);
      
      // Mark as initial load and record connection time
      isInitialLoadRef.current = true;
      connectionTimeRef.current = Date.now();
      
      connect(currentChatId);
      
      // After 3 seconds, consider the initial load complete
      // This allows time for historical messages to be received
      const timer = setTimeout(() => {
        isInitialLoadRef.current = false;
        console.log('🔄 RightPanel: Initial load period complete, now processing real-time updates only');
      }, 3000);
      
      return () => clearTimeout(timer);
    } else {
      console.log('🔌 RightPanel: No chat ID, disconnecting WebSocket');
      disconnect();
    }

    // Cleanup on chat change
    return () => {
      if (!currentChatId) {
        setTaskHistory({});
        processedCompletions.current.clear();
        isInitialLoadRef.current = true;
      }
    };
  }, [currentChatId, connect, disconnect]);

  // Handle incoming WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    console.log('📨 RightPanel: Processing WebSocket message:', lastMessage);
    console.log('📋 Message type:', lastMessage.type);
    console.log('📋 Message data:', lastMessage.data);
    console.log('📋 Full message structure:', JSON.stringify(lastMessage, null, 2));

    try {
      // Handle different message types - be more flexible with message format
      let taskUpdateData = null;
      
      // Check different possible message structures
      if (lastMessage.type === 'task_update' && lastMessage.data) {
        taskUpdateData = lastMessage.data;
      } else if (lastMessage.data && lastMessage.data.type === 'task_update') {
        taskUpdateData = lastMessage.data;
      } else if (lastMessage.type && lastMessage.type.includes('task') && lastMessage.data) {
        taskUpdateData = lastMessage.data;
      } else if ((lastMessage as any).task_key || (lastMessage as any).status) {
        // Direct task update in the message
        taskUpdateData = lastMessage;
      }
      
      if (taskUpdateData) {
        console.log('🎯 RightPanel: Found task update data:', taskUpdateData);
        const progressItem = mapTaskUpdateToProgress(taskUpdateData as TaskUpdate);
        const taskId = progressItem.taskId;
        const messageKey = `${taskId}-${progressItem.status}`;

        let shouldNotifyParent = false;
        let notificationData: { text: string; error: boolean; taskKey: string; isTaskOutput: true } | null = null;

        // Check if this specific completion/failure event for this task has already been processed for notification
        const alreadyNotified = processedCompletions.current.has(messageKey);

        const isFinalReportMessage =
            progressItem.taskStatus === 'completed' && 
            (
              // Check for report property
              (progressItem.output?.report && 
               typeof progressItem.output.report === 'string' && 
               progressItem.output.report.trim() !== '') ||
              // Check for markdown_output property directly
              (progressItem.output?.markdown_output && 
               typeof progressItem.output.markdown_output === 'string' && 
               progressItem.output.markdown_output.trim() !== '') ||
              // Check in output.content if it exists
              (progressItem.output?.content && 
               typeof progressItem.output.content === 'string' && 
               progressItem.output.content.trim() !== '')
            );

        const isFailureMessage = progressItem.taskStatus === 'failed';

        console.log('[RightPanel] Message analysis:', {
          taskId,
          status: progressItem.taskStatus,
          alreadyNotified,
          isFinalReportMessage,
          hasOutput: !!progressItem.output,
          hasReport: !!progressItem.output?.report,
          hasMarkdownOutput: !!progressItem.output?.markdown_output,
          reportType: progressItem.output?.report ? typeof progressItem.output.report : 'none',
          markdownOutputType: progressItem.output?.markdown_output ? typeof progressItem.output.markdown_output : 'none',
          outputStructure: progressItem.output ? Object.keys(progressItem.output) : [],
          isFailureMessage
        });

        // Only notify parent if this is a real-time update (not from initial load/refresh)
        const isHistoricalMessage = isInitialLoadRef.current && 
          (Date.now() - connectionTimeRef.current < 5000); // Within 5 seconds of connection
        
        console.log('🔍 RightPanel: Message timing check:', {
          taskId,
          status: progressItem.taskStatus,
          isInitialLoad: isInitialLoadRef.current,
          timeSinceConnection: Date.now() - connectionTimeRef.current,
          isHistoricalMessage,
          alreadyNotified
        });

        // Notify if it's a new final report or a new failure, we haven't notified for this specific event yet,
        // AND it's not a historical message from page refresh
        if (!alreadyNotified && (isFinalReportMessage || isFailureMessage) && onTaskCompletion && !isHistoricalMessage) {
            shouldNotifyParent = true;
            
            // Get the text content from wherever it exists in the output
            let textContent = '';
            if (isFinalReportMessage) {
                if (progressItem.output?.report) {
                    textContent = progressItem.output.report;
                } else if (progressItem.output?.markdown_output) {
                    textContent = progressItem.output.markdown_output;
                } else if (progressItem.output?.content) {
                    textContent = progressItem.output.content;
                }
            } else if (isFailureMessage) {
                textContent = progressItem.error || 'Görev başarısız oldu.';
            }

            notificationData = {
                text: textContent,
                error: isFailureMessage,
                taskKey: taskId,
                isTaskOutput: true
            };

            console.log('✅ RightPanel: Will notify parent (real-time completion):', taskId);
        } else if (isHistoricalMessage && (isFinalReportMessage || isFailureMessage)) {
            console.log('⏪ RightPanel: Skipping notification for historical completion:', taskId);
        }

        // Always mark completion as processed to avoid duplicates
        if (!alreadyNotified && (isFinalReportMessage || isFailureMessage)) {
            processedCompletions.current.add(messageKey);
        }

                 // Update task history
         setTaskHistory(prevHistory => {
             const updatedHistory = { ...prevHistory };
             if (!updatedHistory[taskId]) {
                 updatedHistory[taskId] = {
                     updates: [progressItem],
                     lastUpdate: progressItem,
                     isExpanded: true,
                     createdAt: progressItem.createdAt || progressItem.timestamp,
                     charts: progressItem.charts
                 };
             } else {
                 // Update existing task
                 const prevTaskInfo = updatedHistory[taskId];
                 const hasStatus = prevTaskInfo.updates.some(
                   update => update.id === progressItem.id
                 );
                 const updatedUpdates = hasStatus
                   ? prevTaskInfo.updates
                   : [...prevTaskInfo.updates, progressItem].sort((a, b) => a.timestamp - b.timestamp);

                 const latestUpdate = progressItem.timestamp >= prevTaskInfo.lastUpdate.timestamp
                                      ? progressItem
                                      : prevTaskInfo.lastUpdate;

                 updatedHistory[taskId] = {
                   ...prevTaskInfo,
                   updates: updatedUpdates,
                   lastUpdate: latestUpdate,
                   charts: latestUpdate.id === progressItem.id ? progressItem.charts : prevTaskInfo.charts
                 };
             }

             return updatedHistory;
         });

                 // Notify parent component if needed
         if (shouldNotifyParent && notificationData && onTaskCompletion) {
             console.log('[RightPanel] Notifying parent with completion data:', notificationData);
             onTaskCompletion(notificationData);
         }
      } else if (lastMessage.type === 'chat_message' && lastMessage.data) {
        // Handle chat messages if needed
        console.log('💬 RightPanel: Received chat message:', lastMessage.data);
      } else if (lastMessage.type === 'system' && lastMessage.data) {
        // Handle system messages
        console.log('🔧 RightPanel: Received system message:', lastMessage.data);
      } else {
        // Log unhandled message types for debugging
        console.log('❓ RightPanel: Unhandled message type:', lastMessage.type);
        console.log('❓ Available properties:', Object.keys(lastMessage));
        
        // Try to create a test task update to verify the UI works
        if (lastMessage.type === 'test') {
          const testTaskUpdate: TaskUpdate = {
            type: 'task_update',
            task_key: 'test-task-' + Date.now(),
            status: 'executing',
            current_task: 'Test Task',
            progress: {
              created_at: Date.now() / 1000,
              started_at: Date.now() / 1000,
              percent_complete: 50
            },
            output: null,
            error: null,
            subtasks: ['Step 1', 'Step 2'],
            subtask_status: { 'Step 1': 'completed', 'Step 2': 'executing' }
          };
          
          const testProgressItem = mapTaskUpdateToProgress(testTaskUpdate);
          console.log('🧪 Creating test task update:', testProgressItem);
          
          setTaskHistory(prevHistory => ({
            ...prevHistory,
            [testProgressItem.taskId]: {
              updates: [testProgressItem],
              lastUpdate: testProgressItem,
              isExpanded: true,
              createdAt: testProgressItem.timestamp,
              charts: null
            }
          }));
        }
      }
    } catch (error) {
      console.error('❌ RightPanel: Error processing WebSocket message:', error);
    }
  }, [lastMessage, onTaskCompletion]);

  const toggleTaskExpansion = (taskId: string) => {
    setTaskHistory(prev => {
      if (!prev[taskId]) return prev;
      return {
        ...prev,
        [taskId]: { ...prev[taskId], isExpanded: !prev[taskId].isExpanded }
      };
    });
  };

  const toggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    if (onCollapse) {
      onCollapse(newCollapsedState);
    }
  };

  const sortedTasks = Object.entries(taskHistory)
    .sort(([, a], [, b]) => a.createdAt - b.createdAt);

  return (
    <div className={isCollapsed ? 'right-panel collapsed' : 'right-panel'}>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', overflow: 'hidden' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: isCollapsed ? 'center' : 'space-between', p: 1.5, borderBottom: `1px solid ${theme.palette.divider}`, flexShrink: 0 }}>
          {!isCollapsed && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                İşlem Durumu
                {connectionStatus === 'connected' && (
                  <span style={{ 
                    display: 'inline-block', 
                    width: '8px', 
                    height: '8px', 
                    borderRadius: '50%', 
                    background: isInitialLoadRef.current ? 'orange' : 'green', 
                    marginLeft: '8px' 
                  }}></span>
                )}
                {connectionStatus === 'connected' && isInitialLoadRef.current && (
                  <span style={{ fontSize: '0.7rem', marginLeft: '4px', opacity: 0.7 }}>
                    (geçmiş yükleniyor...)
                  </span>
                )}
              </Typography>
                             {/* Test button for debugging */}
               {process.env.NODE_ENV === 'development' && isConnected && (
                 <IconButton 
                   size="small" 
                   onClick={() => {
                     // Send a test message through WebSocket
                     const testMessage = {
                       type: 'ping',
                       message: 'test from frontend'
                     };
                     console.log('🧪 Sending test message via WebSocket:', testMessage);
                     sendMessage(testMessage);
                   }}
                   title="Send Test Message"
                 >
                   🧪
                 </IconButton>
               )}
            </Box>
          )}
          <IconButton onClick={toggleCollapse} size="small">
            {isCollapsed ? <ChevronLeftIcon /> : <ChevronRightIcon />}
          </IconButton>
        </Box>

        {!isCollapsed && (
          <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
            {connectionStatus === 'connecting' && (
              <Box sx={{ width: '100%', p: 2 }}>
                <LinearProgress />
                <Typography variant="caption" sx={{ display: 'block', mt: 1, textAlign: 'center' }}>Bağlanıyor...</Typography>
              </Box>
            )}
            {connectionStatus === 'disconnected' && currentChatId && (
              <Alert severity="warning" sx={{ m: 2 }}>WebSocket bağlantısı kurulamadı. Gerçek zamanlı güncellemeler alınamıyor.</Alert>
            )}
            {!currentChatId && (
              <Typography variant="body2" sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>Görev güncellemelerini görmek için bir sohbet seçin.</Typography>
            )}
            {sortedTasks.length === 0 && currentChatId && connectionStatus === 'connected' && (
              <Typography variant="body2" sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>Henüz görev güncelleme yok. Yeni bir soru sorduğunuzda güncellemeler burada görünecek.</Typography>
            )}

            <List sx={{ p: 1.5 }}>
              {sortedTasks.map(([taskId, taskInfo]) => (
                 <TaskDetailItem
                    key={taskId}
                    taskId={taskId}
                    taskInfo={taskInfo}
                    toggleTaskExpansion={toggleTaskExpansion}
                    chatId={currentChatId}
                    userId={userId}
                 />
              ))}
            </List>
          </Box>
        )}
      </Box>
    </div>
  );
};

export default RightPanel;