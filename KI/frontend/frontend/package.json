{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^5.13.0", "@types/react-resizable": "^3.0.8", "autoprefixer": "^10.4.21", "axios": "^1.4.0", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "next": "15.2.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-markdown": "^8.0.7", "react-resizable": "^3.0.5", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^9.0.8", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}