# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set the working directory in the container
WORKDIR /app

# Install uv
RUN pip install uv

# Install dependencies using uv
COPY requirements.txt /app/
# Use uv pip install instead of pip install
RUN uv pip install --no-cache --system -r requirements.txt

RUN uv pip install --no-cache --system fitz
# Copy the rest of the application code
COPY . /app/

# Make port 8000 available to the world outside this container
EXPOSE 8000

# Define the command to run the application using uvicorn directly
# Ensure main:app refers to the FastAPI app instance in your main.py
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
