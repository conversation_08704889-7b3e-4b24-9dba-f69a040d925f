import threading
from typing import Dict, Any, Optional, Set, Callable
from contextlib import contextmanager
import inspect
import functools

class UserContextManager:
    """
    Manages user context for tool execution in a multi-user environment.
    Ensures secure injection of user_id into tool calls to prevent data leakage.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(UserContextManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._local = threading.local()
            self._tools_requiring_user_id: Set[str] = set()
            self._tools_requiring_chat_id: Set[str] = set()
            self._initialized = True

            # Register known tools that require user_id and chat_id
            self._register_default_tools()
    
    def _register_default_tools(self):
        """Register default tools that require user_id and chat_id injection"""
        self._tools_requiring_user_id.update([
            'get_user_tables',
            'agregate_data',
            'agregate_limited_data',
            'search_knowledge_base',
            'rfm_analysis',
            'cltv_analysis',
            'churn_analysis'
        ])

        self._tools_requiring_chat_id.update([
            'get_latest_chat_table',
            'get_user_tables',
            'validate_csv_data'
        ])
    
    def register_tool_requiring_user_id(self, tool_name: str):
        """Register a tool that requires user_id injection"""
        self._tools_requiring_user_id.add(tool_name)
    
    def set_user_context(self, user_id: str, chat_id: str = None, task_key: str = None):
        """Set the current user context for the thread"""
        if not user_id:
            raise ValueError("user_id cannot be empty")
        
        self._local.user_id = user_id
        self._local.chat_id = chat_id
        self._local.task_key = task_key
        
        print(f"🔐 UserContextManager: Set context for user_id={user_id}, chat_id={chat_id}, task_key={task_key}")
        print(f"🔐 UserContextManager: Thread ID = {threading.current_thread().ident}")
    
    def get_current_user_id(self) -> Optional[str]:
        """Get the current user_id from thread context"""
        return getattr(self._local, 'user_id', None)
    
    def get_current_chat_id(self) -> Optional[str]:
        """Get the current chat_id from thread context"""
        return getattr(self._local, 'chat_id', None)
    
    def get_current_task_key(self) -> Optional[str]:
        """Get the current task_key from thread context"""
        return getattr(self._local, 'task_key', None)
    
    def clear_context(self):
        """Clear the current user context"""
        if hasattr(self._local, 'user_id'):
            delattr(self._local, 'user_id')
        if hasattr(self._local, 'chat_id'):
            delattr(self._local, 'chat_id')
        if hasattr(self._local, 'task_key'):
            delattr(self._local, 'task_key')
    
    @contextmanager
    def user_context(self, user_id: str, chat_id: str = None, task_key: str = None):
        """Context manager for setting user context"""
        old_user_id = self.get_current_user_id()
        old_chat_id = self.get_current_chat_id()
        old_task_key = self.get_current_task_key()
        
        try:
            self.set_user_context(user_id, chat_id, task_key)
            yield
        finally:
            if old_user_id:
                self.set_user_context(old_user_id, old_chat_id, old_task_key)
            else:
                self.clear_context()
    
    def inject_user_id_if_needed(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Inject user_id into function arguments if the function requires it and it's missing.
        This is the core security mechanism to prevent data leakage.
        """
        print(f"🔍 UserContextManager: Checking tool '{function_name}' - requires user_id: {function_name in self._tools_requiring_user_id}")
        print(f"🔍 UserContextManager: Original arguments: {arguments}")
        
        if function_name not in self._tools_requiring_user_id:
            print(f"🔍 UserContextManager: Tool '{function_name}' doesn't require user_id - passing through unchanged")
            return arguments
        
        # Get current user_id from context
        current_user_id = self.get_current_user_id()
        print(f"🔍 UserContextManager: Current user_id from context: {current_user_id}")
        print(f"🔍 UserContextManager: Current thread ID: {threading.current_thread().ident}")
        
        if not current_user_id:
            error_msg = f"No user context set for tool '{function_name}' that requires user_id"
            print(f"❌ UserContextManager: ERROR - {error_msg}")
            raise ValueError(error_msg)
        
        # Check if user_id is missing, None, or incorrectly set to "user"
        needs_injection = (
            'user_id' not in arguments or 
            arguments.get('user_id') is None or 
            arguments.get('user_id') == "user" or
            arguments.get('user_id') == ""
        )
        
        print(f"🔍 UserContextManager: Needs injection: {needs_injection}")
        
        if needs_injection:
            # Create a copy of arguments to avoid modifying the original
            new_arguments = arguments.copy()
            new_arguments['user_id'] = current_user_id
            
            print(f"✅ UserContextManager: Injected user_id={current_user_id} into {function_name}")
            print(f"✅ UserContextManager: New arguments: {new_arguments}")
            return new_arguments
        
        # Verify that the provided user_id matches the current context for security
        provided_user_id = arguments.get('user_id')
        if provided_user_id != current_user_id:
            print(f"⚠️ UserContextManager: WARNING - user_id mismatch for {function_name}. "
                  f"Provided: {provided_user_id}, Context: {current_user_id}")
            
            # For security, override with context user_id
            new_arguments = arguments.copy()
            new_arguments['user_id'] = current_user_id
            print(f"🔒 UserContextManager: Overrode user_id with context value for security")
            print(f"🔒 UserContextManager: Final arguments: {new_arguments}")
            return new_arguments
        
        print(f"✅ UserContextManager: user_id matches context - passing through unchanged")
        return arguments

    def inject_chat_id_if_needed(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Inject chat_id into function arguments if the function requires it and it's missing.
        """
        print(f"🔍 UserContextManager: Checking tool '{function_name}' - requires chat_id: {function_name in self._tools_requiring_chat_id}")

        if function_name not in self._tools_requiring_chat_id:
            return arguments

        # Get current chat_id from context
        current_chat_id = self.get_current_chat_id()
        print(f"🔍 UserContextManager: Current chat_id from context: {current_chat_id}")

        if not current_chat_id:
            error_msg = f"No chat context set for tool '{function_name}' that requires chat_id"
            print(f"❌ UserContextManager: ERROR - {error_msg}")
            raise ValueError(error_msg)

        # Check if chat_id is missing, None, or incorrectly set
        needs_injection = (
            'chat_id' not in arguments or
            arguments.get('chat_id') is None or
            arguments.get('chat_id') == "chat" or
            arguments.get('chat_id') == ""
        )

        if needs_injection:
            new_arguments = arguments.copy()
            new_arguments['chat_id'] = current_chat_id
            print(f"✅ UserContextManager: Injected chat_id={current_chat_id} into {function_name}")
            return new_arguments

        # Verify that the provided chat_id matches the current context for security
        provided_chat_id = arguments.get('chat_id')
        if provided_chat_id != current_chat_id:
            print(f"⚠️ UserContextManager: WARNING - chat_id mismatch for {function_name}. "
                  f"Provided: {provided_chat_id}, Context: {current_chat_id}")

            # For security, override with context chat_id
            new_arguments = arguments.copy()
            new_arguments['chat_id'] = current_chat_id
            print(f"🔒 UserContextManager: Overrode chat_id with context value for security")
            return new_arguments

        print(f"✅ UserContextManager: chat_id matches context - passing through unchanged")
        return arguments


def create_user_context_hook():
    """
    Create a hook function that can be used with Agno agents to inject user_id.
    This replaces the simple logger_hook with a security-aware version.
    """
    context_manager = UserContextManager()
    
    def user_context_hook(function_name: str, function_call: Callable, arguments: Dict[str, Any]):
        """Hook that logs function calls and injects user_id where needed"""
        import time
        
        print(f"🚀 Hook: Starting execution of '{function_name}'")
        print(f"🚀 Hook: Thread ID = {threading.current_thread().ident}")
        
        start_time = time.time()
        
        # Inject user_id if needed for security
        try:
            secure_arguments = context_manager.inject_user_id_if_needed(function_name, arguments)
        except Exception as e:
            print(f"❌ Hook: ERROR - Failed to inject user_id for {function_name}: {e}")
            raise

        # Inject chat_id if needed for security
        try:
            secure_arguments = context_manager.inject_chat_id_if_needed(function_name, secure_arguments)
        except Exception as e:
            print(f"❌ Hook: ERROR - Failed to inject chat_id for {function_name}: {e}")
            raise
        
        # Call the function with the secure arguments
        try:
            print(f"🔧 Hook: Calling {function_name} with secure arguments: {secure_arguments}")
            print(f"🔧 Hook: Function object: {function_call}")
            print(f"🔧 Hook: Function signature: {inspect.signature(function_call) if hasattr(inspect, 'signature') else 'N/A'}")

            # Debug: Try to inspect what we're actually calling
            if hasattr(function_call, '__name__'):
                print(f"🔧 Hook: Function name: {function_call.__name__}")
            if hasattr(function_call, '__self__'):
                print(f"🔧 Hook: Function self: {function_call.__self__}")

            # SPECIAL HANDLING for toolkit methods to bypass Agno validation
            if function_name in ["get_latest_chat_table", "get_user_tables", "validate_csv_data"]:
                print(f"🔧 Hook: Special handling for {function_name}")
                # Try to get the actual toolkit instance
                if hasattr(function_call, '__self__') and hasattr(function_call.__self__, function_name):
                    toolkit_instance = function_call.__self__
                    print(f"🔧 Hook: Found toolkit instance: {toolkit_instance}")
                    # Call the method directly on the toolkit instance
                    method = getattr(toolkit_instance, function_name)
                    result = method(**secure_arguments)
                else:
                    # Fallback to normal call
                    result = function_call(**secure_arguments)
            else:
                result = function_call(**secure_arguments)

            print(f"✅ Hook: Function {function_name} completed successfully")
        except Exception as e:
            print(f"❌ Hook: ERROR - Function {function_name} failed: {e}")
            import traceback
            traceback.print_exc()
            raise
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Log the execution (but hide sensitive data in production)
        print("🎯 ================================")
        print(f"🎯 Function {function_name} executed with user_id={context_manager.get_current_user_id()}")
        print(f"🎯 Function {function_name} took {duration:.2f} seconds")
        print(f"🎯 Arguments modified: {arguments != secure_arguments}")
        print(f"🎯 Result type: {type(result)}")
        print("🎯 ================================")
        
        return result
    
    return user_context_hook 