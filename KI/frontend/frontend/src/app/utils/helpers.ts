import { v4 as uuidv4 } from 'uuid';
import { extractChartsFromMarkdown } from './chartParser';
import { ChartConfig } from '@/components/types';

// Types
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'thinking' | 'info';
  content: string;
  timestamp: Date;
  status?: 'error' | 'info' | 'warning' | 'success' | 'completed' | 'status';
  charts?: ChartConfig[];
}

export interface StatusResponse {
  task_id: string;
  status: string;
  current_task?: string;
  message?: string;
  error?: string;
  report?: string;
  router_output?: {
    tasks?: string[];
    text?: string;
    task_list?: string[];
    reasoning?: string;
  };
  tasks?: Record<string, any>;
}

/**
 * Generates a unique ID
 * @returns {string} Unique ID
 */
export const generateId = (): string => uuidv4();

/**
 * Formats the date as a Turkish formatted time
 * @param {Date} date - Date to format
 * @returns {string} Formatted time (e.g., 15:30)
 */
export const formatDate = (date: Date | string): string => {
  return new Date(date).toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Creates a message object showing the thinking state
 * @returns {Message} Message object
 */
export const createThinkingMessage = (): Message => ({
  id: generateId(),
  role: 'thinking',
  content: 'Preparing the answer',
  timestamp: new Date()
});

/**
 * Creates messages from analysis status
 * @param {StatusResponse} statusResponse - Analysis status response
 * @param {Array<Message>} existingMessages - Existing messages array
 * @returns {Array<Message>} - New messages array
 */
export const createMessagesFromAnalysisStatus = (
  statusResponse: StatusResponse,
  existingMessages: Message[] = []
): Message[] => {
  // Invalid status response check
  if (!statusResponse || !statusResponse.status) {
    console.error('❌ Invalid status response:', statusResponse);
    return [];
  }

  console.log('🔍 createMessagesFromAnalysisStatus called:', statusResponse.status, statusResponse);

  // Array of new messages to be created
  const newMessages: Message[] = [];

  // Base message ID (without timestamp)
  const baseMessageId = `analysis-${statusResponse.task_id}`;

  // Determine message status (completion, error, or info)
  let messageStatus: Message['status'] = 'info'; // Default status

  if (statusResponse.status === 'failed') {
    messageStatus = 'error';
  } else if (statusResponse.status === 'completed') {
    messageStatus = 'success';
  } else if (statusResponse.status === 'info') {
    messageStatus = 'info';
  } else {
    // Intermediate states (routing, executing, reporting, processing) marked as status
    messageStatus = 'status';
  }

  // Helper function that adds a unique timestamp to each new message
  const createNewMessage = (role: Message['role'], content: string, idSuffix = ''): Message => {
    const timestamp = new Date();
    const uniqueId = `${baseMessageId}${idSuffix}_${timestamp.getTime()}`;

    return {
      id: uniqueId,
      role,
      content,
      timestamp,
      status: messageStatus
    };
  };

  // Special message creation in thinking state - we do nothing here
  // Thinking message update is done with updateThinkingMessage in useChat
  if (statusResponse.status === 'thinking') {
    return newMessages; // Return empty array
  }

  // Create main message content
  let content = '';

  // Show report and results in completed state
  if (statusResponse.status === 'completed' && statusResponse.report) {
    // Check if a message with the same base ID already exists
    const reportId = `${baseMessageId}-report`;
    const hasExistingReport = existingMessages.some(msg =>
      msg.id.startsWith(reportId) && msg.role === 'assistant'
    );

    // If there's no report message with the same ID, add a new message
    if (!hasExistingReport) {
      console.log('✅ Adding a new completed analysis report');

      // Add the report itself - extract charts first
      content = statusResponse.report;

      // Extract charts from the report content
      const { cleanedContent, charts } = extractChartsFromMarkdown(content);

      // Create message with cleaned content and charts
      const reportMessage = createNewMessage('assistant', cleanedContent, '-report');
      if (charts.length > 0) {
        reportMessage.charts = charts;
        console.log('📊 Extracted charts from report:', charts.length, charts);
      }
      newMessages.push(reportMessage);

      // Also add router output or task results if they exist
      const tasks = statusResponse.tasks || {};

      // Router output information
      if (tasks.router_output) {
        let routerContent = '';
        try {
          const routerOutput = tasks.router_output;
          // Check if it's an object
          if (typeof routerOutput === 'object') {
            const taskList = routerOutput.task_list || [];
            const reasoning = routerOutput.reasoning || '';

            routerContent = `### Analysis Plan\n\n${reasoning}\n\n`;

            if (taskList.length > 0) {
              routerContent += '**Operations Performed:**\n\n';
              taskList.forEach((task:any, index:any) => {
                routerContent += `${index + 1}. ${task}\n`;
              });
            }
          } else {
            routerContent = `### Analysis Plan\n\n${String(routerOutput)}`;
          }

          // Router output message
          if (routerContent) {
            newMessages.push(createNewMessage('assistant', routerContent, '-plan'));
          }
        } catch (e) {
          console.error('Error processing router output:', e);
        }
      }

      // Task results
      if (tasks.task_results && Object.keys(tasks.task_results).length > 0) {
        let tasksContent = '';

        console.log('🔍 ** *** *** *** tasks.task_results:', tasks.task_results);

        Object.entries(tasks.task_results).forEach(([taskName, taskResult]) => {
          // Check if the result has markdown_output field
          if (typeof taskResult === 'object' && (taskResult as any).markdown_output) {
            tasksContent += `${(taskResult as any).markdown_output}\n\n`;
          }
          // Handle other object formats
          else if (typeof taskResult === 'object') {
            if ((taskResult as any).sonuç) {
              tasksContent += `${(taskResult as any).sonuç}\n\n`;
            }

            if ((taskResult as any).açıklama) {
              tasksContent += `*${(taskResult as any).açıklama}*\n\n`;
            }

            // Add other fields as well
            Object.entries(taskResult as object).forEach(([key, value]) => {
              if (key !== 'sonuç' && key !== 'açıklama' && key !== 'markdown_output') {
                tasksContent += `**${key}:** ${value}\n\n`;
              }
            });
          } else {
            tasksContent += `#### ${taskName}\n\n${String(taskResult)}\n\n`;
          }
        });

        newMessages.push(createNewMessage('assistant', tasksContent, '-details'));
      }
    } else {
      console.log('⚠️ This analysis report has already been added, will not be added again');
    }

    return newMessages;
  }

  // Add error message in error state
  if (statusResponse.status === 'failed' || statusResponse.error) {
    // Check if an error message with the same base ID already exists
    const errorId = `${baseMessageId}-error`;
    const hasExistingError = existingMessages.some(msg =>
      msg.id.startsWith(errorId) && msg.role === 'assistant'
    );

    // If there's no error message with the same ID, add a new message
    if (!hasExistingError) {
      content = statusResponse.error || 'An error occurred during analysis. Please try again later.';
      newMessages.push(createNewMessage('assistant', content, '-error'));
    } else {
      console.log('⚠️ This error message has already been added, will not be added again');
    }

    return newMessages;
  }

  // Information status message
  if (statusResponse.status === 'info' && statusResponse.message) {
    content = statusResponse.message;
    newMessages.push(createNewMessage('info', content, '-info'));
    return newMessages;
  }

  // Here, intermediate status messages (routing, executing, reporting) will be processed
  // Display these messages as normal "status" messages
  if (['routing', 'executing', 'reporting', 'processing'].includes(statusResponse.status)) {
    // If there's a message field from the backend, use it directly as it may already contain current_task info
    if (statusResponse.message) {
      content = statusResponse.message;
    } else {
      // Create content based on status
      switch (statusResponse.status) {
        case 'routing':
          content = 'Determining analysis stages. Selecting the most suitable tasks for your question...';
          break;
        case 'executing':
          content = 'Analysis in progress, operations being executed...';

          // Backend stores current_task at the top level, accessing directly from statusResponse
          if (statusResponse.current_task) {
            content += `\n\nCurrently running task: **${statusResponse.current_task}**`;
            console.log('👉 Current task found:', statusResponse.current_task);
          } else {
            console.log('⚠️ Current task information not found!');
          }
          break;
        case 'reporting':
          content = 'Analysis completed. Results are being compiled and the report is being prepared...';
          break;
        default:
          content = `Analysis in progress: ${statusResponse.status}`;
      }
    }

    // Make the status ID unique - by mixing timestamp and status
    const statusId = `${baseMessageId}-${statusResponse.status}`;

    // For executing messages specifically, we will always update
    if (statusResponse.status === 'executing') {
      // Always update executing messages - both when there's a new task and when the same task continues
      console.log('♻️ Executing message mandatory update - new content:', content);
      newMessages.push(createNewMessage('assistant', content, `-${statusResponse.status}`));
    } else {
      // Normal check will be done for other messages
      const executingMessageIndex = existingMessages.findIndex(msg =>
        msg.id.includes(statusId) && msg.role === 'assistant'
      );

      if (executingMessageIndex === -1) {
        // Status message not yet added, create a new message
        newMessages.push(createNewMessage('assistant', content, `-${statusResponse.status}`));
      }
    }
  }

  return newMessages;
};

/**
 * Gets thinking stage text from analysis status
 */
export const getThinkingStageText = (statusResponse: StatusResponse): string => {
  if (!statusResponse) return 'Thinking...';

  switch (statusResponse.status) {
    case 'thinking':
    case 'received':
      return 'Processing the question...';
    case 'routing':
      return 'Analyzing the question...';
    case 'executing':
      // If current_task exists, show it
      if (statusResponse.current_task) {
        return `Working on: ${statusResponse.current_task}`;
      }
      return 'Executing analysis tasks...';
    case 'reporting':
      return 'Compiling analysis results...';
    case 'completed':
      return 'Analysis completed!';
    case 'failed':
    case 'error':
      return 'An error occurred during analysis.';
    default:
      return `Status: ${statusResponse.status}`;
  }
};

/**
 * Gets analysis stage from status response
 */
export const getAnalysisStageFromStatus = (statusResponse: StatusResponse): string => {
  if (!statusResponse) return 'idle';

  switch (statusResponse.status) {
    case 'thinking':
    case 'received':
      return 'routing';
    case 'routing':
      return 'routing';
    case 'executing':
      return 'executing';
    case 'reporting':
      return 'reporting';
    case 'completed':
      return 'completed';
    case 'failed':
    case 'error':
      return 'error';
    default:
      return statusResponse.status;
  }
};