from agno.tools import Toolkit, tool
from typing import Dict, Any, Optional

from src.rag_engine.interface import RAGInterface

class KnowledgeToolkit(Toolkit):
    def __init__(self):
        super().__init__(
            name="KnowledgeToolkit",
            instructions=None,
            add_instructions=False,
        )
                
        self.rag_interface = RAGInterface()
        self._user_id = None

        # Also register with the new name
        self.register(self.search_knowledge_base, name="search_knowledge_base")
        # Add other knowledge-based tools here if any
    
    def set_context(self, user_id: str):
        """Set the user context for the toolkit"""
        self._user_id = user_id
        print(f"KnowledgeToolkit: User context set to {user_id}")

    def search_knowledge_base(self, query: str, user_id: Optional[str] = None, top_k: int = 10) -> Dict[str, Any]:
        """
        Belgelerde arama yapar. Hem genel knowledge base'de hem de kullanıcının knowledge base'inde arama yapar.

        Args:
            query: Aranacak sorgu.
            user_id: Kullanıcı ID'si (kanca tarafından enjekte edilir).
            top_k: Döndürülecek sonuç sayısı (varsayılan: 6).

        Returns:
            <PERSON><PERSON> sonuçlarını içeren bir sözlük.
        """
        try:
            print(f"Original input - query: {repr(query)}, top_k: {repr(top_k)}")
            
            # Handle the case where the entire input is duplicated JSON
            if isinstance(query, str):
                # Check if this might be a duplicated JSON string
                try:
                    import json
                    import re
                    
                    # Try to find a JSON object pattern in the string
                    json_pattern = r'(\{[^{}]*\})'
                    matches = re.findall(json_pattern, query)
                    
                    if matches and len(matches) > 0:
                        print(f"Found potential JSON matches: {len(matches)}")
                        # Try parsing the first match
                        try:
                            first_json = matches[0]
                            parsed_data = json.loads(first_json)
                            if isinstance(parsed_data, dict) and 'query' in parsed_data:
                                extracted_query = parsed_data.get('query')
                                extracted_top_k = parsed_data.get('top_k', top_k)
                                print(f"Extracted from JSON - query: {extracted_query}, top_k: {extracted_top_k}")
                                query = extracted_query
                                if extracted_top_k is not None:
                                    top_k = extracted_top_k
                        except json.JSONDecodeError as json_err:
                            print(f"Error parsing first JSON match: {json_err}")
                except Exception as parse_error:
                    print(f"Error during JSON extraction: {parse_error}")
            
            # SECURITY FIX: Get user_id from context if it's wrong or missing
            from src.agents.agno_agents.user_context_manager import UserContextManager
            context_manager = UserContextManager()
            context_user_id = context_manager.get_current_user_id()
            
            # First check if user_id is provided directly
            effective_user_id = user_id
            
            # If context user_id exists, use it for security
            if context_user_id:
                if not effective_user_id or effective_user_id in ['user', 'placeholder', 'user_id_placeholder', '']:
                    print(f"🔧 KnowledgeToolkit: search_knowledge_base - Injecting user_id from context: {context_user_id}")
                    effective_user_id = context_user_id
                elif effective_user_id != context_user_id:
                    print(f"⚠️ KnowledgeToolkit: search_knowledge_base - Security override - using context user_id: {context_user_id} instead of: {effective_user_id}")
                    effective_user_id = context_user_id
            
            # If not, use the one set via set_context (legacy)
            if not effective_user_id:
                effective_user_id = self._user_id

            # Make sure top_k is an integer
            if top_k and not isinstance(top_k, int):
                try:
                    top_k = int(top_k)
                except (ValueError, TypeError):
                    print(f"Invalid top_k value '{top_k}', using default of 6")
                    top_k = 6

            # Calculate how many results to get from each knowledge base
            # We'll get top_k from each and then merge/sort them
            per_kb_limit = top_k

            all_results = []
            search_sources = []

            # 1. Always search in general knowledge base
            print(f"Searching in general knowledge base - query: {query}, top_k: {per_kb_limit}")
            try:
                general_results = self.rag_interface.search_knowledge_base(query=query, user_id=None, top_k=per_kb_limit)
                if general_results.get("status") == "success" and general_results.get("results"):
                    # Add source information to each result
                    for result in general_results["results"]:
                        result["knowledge_base_source"] = "general"
                    all_results.extend(general_results["results"])
                    search_sources.append("general")
                    print(f"Found {len(general_results['results'])} results in general knowledge base")
            except Exception as e:
                print(f"Error searching general knowledge base: {str(e)}")

            # 2. If user_id is available, also search in user-specific knowledge base
            if effective_user_id:
                print(f"Searching in user knowledge base - user_id: {effective_user_id}, query: {query}, top_k: {per_kb_limit}")
                try:
                    user_results = self.rag_interface.search_knowledge_base(query=query, user_id=effective_user_id, top_k=per_kb_limit)
                    if user_results.get("status") == "success" and user_results.get("results"):
                        # Add source information to each result
                        for result in user_results["results"]:
                            result["knowledge_base_source"] = f"user_{effective_user_id}"
                        all_results.extend(user_results["results"])
                        search_sources.append(f"user_{effective_user_id}")
                        print(f"Found {len(user_results['results'])} results in user knowledge base")
                except Exception as e:
                    print(f"Error searching user knowledge base: {str(e)}")

            # 3. Sort all results by score (descending) and limit to top_k
            if all_results:
                # Sort by score (higher is better)
                all_results.sort(key=lambda x: x.get('score', 0), reverse=True)
                # Limit to requested number of results
                # final_results = all_results[:top_k]
                final_results = all_results
            else:
                final_results = []

            # 4. Prepare response
            search_summary = f"Searched in: {', '.join(search_sources)}" if search_sources else "No knowledge bases searched"

            print(f"Final search results: {len(final_results)} results from {len(search_sources)} knowledge bases")

            return {
                "status": "success",
                "message": f"Doküman araması başarıyla tamamlandı. {search_summary}",
                "results": final_results,
                "query": query,
                "searched_knowledge_bases": search_sources,
                "total_results_found": len(all_results),
                "returned_results": len(final_results)
            }
            
        except Exception as e:
            error_message = f"Error in search_knowledge_base: {str(e)}"
            print(error_message)
            return {
                "status": "error",
                "message": error_message,
                "results": []
            }