import importlib
import inspect
import yaml
from pathlib import Path
from typing import List, Optional, Dict, Any, Callable, Union
from agno.tools.decorator import Function

from agno.tools import Toolkit # Toolkit base class
from agno.tools.reasoning import ReasoningTools # Specific toolkit



class AgnoToolManager:
    def __init__(self):
        self._tool_objects_map: Dict[str, Union[Callable, Toolkit]] = {}
        self.loaded_toolkit_instances: List[Toolkit] = []
        self._initialize_toolkits_and_tools()


    def _initialize_toolkits_and_tools(self):
        config_file_path = Path(__file__).parent / "config" / "tools.yaml"
        if not config_file_path.exists():
            print(f"Warning: Tool configuration file not found at {config_file_path}.")
            return
        try:
            with open(config_file_path, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)
        except Exception as e:
            print(f"Error reading or parsing tools.yaml: {e}")
            return

        toolkit_class_paths = config_data.get("enabled_toolkits", [])
        if not isinstance(toolkit_class_paths, list):
            print("Warning: 'enabled_toolkits' in tools.yaml is not a list or missing.")
            return

        for toolkit_path_str in toolkit_class_paths:
            try:
                module_name, class_name = toolkit_path_str.rsplit('.', 1)
                module = importlib.import_module(module_name)
                toolkit_class = getattr(module, class_name)
                
                toolkit_instance = toolkit_class()
                self.loaded_toolkit_instances.append(toolkit_instance)
                
            
                if isinstance(toolkit_instance, Toolkit):
                    # Class name    
                    self._tool_objects_map[class_name] = toolkit_instance
                    print(f"Toolkit instances: {class_name}")
            except Exception as e:
                print(f"Error loading or processing toolkit '{toolkit_path_str}': {e}")
        
        print(f"AgnoToolManager initialized. Tool objects map: {list(self._tool_objects_map.keys())}")

    def get_all_tool_objects_map(self) -> Dict[str, Union[Callable, Toolkit]]:
        return self._tool_objects_map


    def get_all_tools(self, agent_name: Optional[str] = None) -> List[Union[Callable, Toolkit]]: # Corrected type hint
        return list(self._tool_objects_map.values())
