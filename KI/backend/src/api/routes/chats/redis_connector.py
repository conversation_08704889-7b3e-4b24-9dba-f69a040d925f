"""
Redis connection setup for KAI task storage
"""

import os
import redis
import logging
from dotenv import load_dotenv
from fastapi import HTTPException

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Global Redis client instance
redis_client = None

def get_redis_client() -> redis.Redis:
    """
    Initializes and returns a Redis client instance.
    Reads connection details from environment variables.
    
    Environment Variables:
        REDIS_HOST (default: localhost)
        REDIS_PORT (default: 6379)
        REDIS_DB (default: 0)
        REDIS_PASSWORD (optional)
    """
    global redis_client
    if redis_client is None:
        try:
            redis_host = os.getenv("REDIS_HOST", "localhost")
            redis_port = int(os.getenv("REDIS_PORT", 6379))
            redis_db = int(os.getenv("REDIS_DB", 0))
            redis_password = os.getenv("REDIS_PASSWORD")
            
            logger.info(f"Connecting to Redis at {redis_host}:{redis_port} DB {redis_db}")
            
            redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password,
                decode_responses=True # Automatically decode responses to strings
            )
            # Test connection
            redis_client.ping()
            logger.info("Successfully connected to Redis")
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            # Depending on requirements, you might raise the exception or return None
            raise HTTPException(status_code=503, detail="Could not connect to Redis task store") from e
        except Exception as e:
            logger.error(f"An unexpected error occurred during Redis connection: {e}")
            raise HTTPException(status_code=500, detail="Error connecting to Redis task store") from e
            
    return redis_client

def create_task_key(task_key: str) -> str:
    """Standardize the Redis key format for task hashes."""
    return f"task:{task_key}"

def create_chat_tasks_key(chat_id: str) -> str:
    """Standardize the Redis key format for chat task sets."""
    return f"chat_tasks:{chat_id}" 