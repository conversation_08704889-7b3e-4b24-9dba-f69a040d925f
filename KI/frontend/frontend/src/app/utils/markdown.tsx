import React from 'react';
import ReactMarkdown from 'react-markdown';
// Temporarily remove complex imports if they are part of the issue for dev build
// import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
// import { prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import {
  Typography,
  Link,
  Box,
  List,
  ListItem,
  // Paper, // Temporarily remove if blockquote is simplified
} from '@mui/material';

interface MarkdownProps {
  children: string;
}

/**
 * Renders markdown content
 * @param {string} content - Markdown content to process
 * @returns {React.ReactElement} Rendered markdown
 */
export const renderMarkdown = (content: string): React.ReactElement => {
  return (
    <ReactMarkdown
      components={{
        // Headings
        h1: ({ node, ...props }) => (
          <Typography variant="h4" gutterBottom fontWeight="bold" {...props} />
        ),
        h2: ({ node, ...props }) => (
          <Typography variant="h5" gutterBottom fontWeight="bold" {...props} />
        ),
        h3: ({ node, ...props }) => (
          <Typography variant="h6" gutterBottom fontWeight="bold" {...props} />
        ),
        h4: ({ node, ...props }) => (
          <Typography variant="subtitle1" gutterBottom fontWeight="bold" {...props} />
        ),
        p: ({ node, ...props }) => (
          <Typography variant="body1" paragraph {...props} />
        ),
        ul: ({ node, ...props }) => (
          <List sx={{ pl: 4, mb: 2 }} {...props} />
        ),
        ol: ({ node, ...props }) => (
          <List sx={{ pl: 4, mb: 2 }} component="ol" {...props} />
        ),
        li: ({ node, ...props }) => (
          <ListItem sx={{ display: 'list-item', pl: 0 }} {...props} />
        ),
        a: ({ node, ...props }) => (
          <Link color="primary" {...props} />
        ),
        // Temporarily simplify or remove problematic custom components
        code: (props: any) => {
          const { node, inline, className, children, ...rest } = props;
          // Basic code rendering without syntax highlighting for now
          const langMatch = className && className.startsWith('language-');
          if (!inline && langMatch) {
            return (
              <Box component="pre" sx={{ backgroundColor: '#f5f5f5', p: 2, borderRadius: 1, overflowX: 'auto' }}>
                <code {...rest}>{children}</code>
              </Box>
            );
          }
          return (
            <Box component="code" sx={{ backgroundColor: 'rgba(0,0,0,0.06)', borderRadius: 1, px:0.5, py:0.25, fontFamily: 'monospace'}} {...rest}>
              {children}
            </Box>
          );
        },
        strong: ({ node, ...props }) => (
          <Box component="strong" fontWeight="bold" {...props} />
        ),
        em: ({ node, ...props }) => (
          <Box component="em" fontStyle="italic" {...props} />
        ),
        // blockquote: ({ node, ...props }) => (
        //   <Paper elevation={0} sx={{ borderLeft: 4, borderColor: 'grey.300', backgroundColor: 'grey.100', p: 2, my: 2, mx: 0 }} {...props} />
        // ),
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export const MarkdownContent = ({ children }: MarkdownProps): React.ReactElement => {
  return renderMarkdown(children);
}; 