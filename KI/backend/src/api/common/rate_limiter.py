"""
Rate limiting middleware for multi-user API protection
"""

import time
import asyncio
from typing import Dict, <PERSON><PERSON>
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import redis
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class InMemoryRateLimiter:
    """In-memory rate limiter for development/testing"""
    
    def __init__(self):
        self.requests = {}
        self.lock = asyncio.Lock()
    
    async def is_allowed(self, key: str, limit: int, window: int) -> Tuple[bool, Dict]:
        """Check if request is allowed within rate limit"""
        async with self.lock:
            now = time.time()
            window_start = now - window
            
            # Clean old entries
            if key in self.requests:
                self.requests[key] = [req_time for req_time in self.requests[key] if req_time > window_start]
            else:
                self.requests[key] = []
            
            # Check if under limit
            if len(self.requests[key]) < limit:
                self.requests[key].append(now)
                return True, {
                    'allowed': True,
                    'limit': limit,
                    'remaining': limit - len(self.requests[key]),
                    'reset_time': int(window_start + window)
                }
            else:
                return False, {
                    'allowed': False,
                    'limit': limit,
                    'remaining': 0,
                    'reset_time': int(self.requests[key][0] + window)
                }

class RedisRateLimiter:
    """Redis-based rate limiter for production use"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    async def is_allowed(self, key: str, limit: int, window: int) -> Tuple[bool, Dict]:
        """Check if request is allowed within rate limit using Redis"""
        try:
            pipe = self.redis.pipeline()
            now = time.time()
            window_start = now - window
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            # Count current entries
            pipe.zcard(key)
            # Add current request
            pipe.zadd(key, {str(now): now})
            # Set expiration
            pipe.expire(key, window)
            
            results = pipe.execute()
            current_count = results[1]
            
            if current_count < limit:
                return True, {
                    'allowed': True,
                    'limit': limit,
                    'remaining': limit - current_count - 1,  # -1 for current request
                    'reset_time': int(now + window)
                }
            else:
                # Remove the request we just added since it's over limit
                self.redis.zrem(key, str(now))
                return False, {
                    'allowed': False,
                    'limit': limit,
                    'remaining': 0,
                    'reset_time': int(now + window)
                }
                
        except Exception as e:
            logger.error(f"Redis rate limiter error: {e}")
            # Fallback: allow request on Redis errors
            return True, {
                'allowed': True,
                'limit': limit,
                'remaining': limit,
                'reset_time': int(now + window),
                'error': 'Rate limiter unavailable'
            }

class RateLimitMiddleware:
    """FastAPI middleware for rate limiting"""
    
    def __init__(
        self,
        calls_per_minute: int = 60,
        calls_per_hour: int = 1000,
        concurrent_requests_per_user: int = 5,
        redis_client=None
    ):
        self.calls_per_minute = calls_per_minute
        self.calls_per_hour = calls_per_hour
        self.concurrent_requests_per_user = concurrent_requests_per_user
        
        # Choose rate limiter based on Redis availability
        if redis_client:
            self.limiter = RedisRateLimiter(redis_client)
        else:
            self.limiter = InMemoryRateLimiter()
            logger.warning("Using in-memory rate limiter. Consider using Redis for production.")
        
        # Track concurrent requests per user
        self.active_requests = {}
        self.request_lock = asyncio.Lock()
    
    async def __call__(self, request: Request, call_next):
        # Extract user identifier
        user_id = await self._get_user_id(request)
        
        # Check concurrent requests limit
        async with self.request_lock:
            if user_id in self.active_requests:
                if self.active_requests[user_id] >= self.concurrent_requests_per_user:
                    return JSONResponse(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        content={
                            "error": "Too many concurrent requests",
                            "message": f"Maximum {self.concurrent_requests_per_user} concurrent requests per user"
                        }
                    )
            else:
                self.active_requests[user_id] = 0
            
            self.active_requests[user_id] += 1
        
        try:
            # Check rate limits
            minute_allowed, minute_info = await self.limiter.is_allowed(
                f"rate_limit:minute:{user_id}", 
                self.calls_per_minute, 
                60
            )
            
            hour_allowed, hour_info = await self.limiter.is_allowed(
                f"rate_limit:hour:{user_id}", 
                self.calls_per_hour, 
                3600
            )
            
            if not minute_allowed:
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Rate limit exceeded",
                        "message": f"Too many requests per minute. Limit: {self.calls_per_minute}/min",
                        "reset_time": minute_info['reset_time']
                    },
                    headers={
                        "X-RateLimit-Limit": str(self.calls_per_minute),
                        "X-RateLimit-Remaining": str(minute_info['remaining']),
                        "X-RateLimit-Reset": str(minute_info['reset_time'])
                    }
                )
            
            if not hour_allowed:
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Rate limit exceeded", 
                        "message": f"Too many requests per hour. Limit: {self.calls_per_hour}/hour",
                        "reset_time": hour_info['reset_time']
                    },
                    headers={
                        "X-RateLimit-Limit": str(self.calls_per_hour),
                        "X-RateLimit-Remaining": str(hour_info['remaining']),
                        "X-RateLimit-Reset": str(hour_info['reset_time'])
                    }
                )
            
            # Process request
            response = await call_next(request)
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit-Minute"] = str(self.calls_per_minute)
            response.headers["X-RateLimit-Remaining-Minute"] = str(minute_info['remaining'])
            response.headers["X-RateLimit-Limit-Hour"] = str(self.calls_per_hour)
            response.headers["X-RateLimit-Remaining-Hour"] = str(hour_info['remaining'])
            
            return response
            
        finally:
            # Decrease concurrent request count
            async with self.request_lock:
                if user_id in self.active_requests:
                    self.active_requests[user_id] -= 1
                    if self.active_requests[user_id] <= 0:
                        del self.active_requests[user_id]
    
    async def _get_user_id(self, request: Request) -> str:
        """Extract user ID from request"""
        # Try to get user_id from different sources
        
        # 1. From query parameters
        user_id = request.query_params.get('user_id')
        if user_id:
            return user_id
        
        # 2. From request body (for POST requests)
        if request.method == "POST":
            try:
                body = await request.body()
                if body:
                    data = json.loads(body)
                    if isinstance(data, dict) and 'user_id' in data:
                        return data['user_id']
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
        
        # 3. From headers
        user_id = request.headers.get('X-User-ID')
        if user_id:
            return user_id
        
        # 4. From path parameters (if user_id is in path)
        path_params = request.path_params
        if 'user_id' in path_params:
            return path_params['user_id']
        
        # 5. Fallback to IP address
        client_ip = request.client.host if request.client else 'unknown'
        return f"ip:{client_ip}"

# Factory function for creating rate limiter middleware
def create_rate_limiter(
    calls_per_minute: int = 60,
    calls_per_hour: int = 1000, 
    concurrent_requests_per_user: int = 5,
    redis_client=None
) -> RateLimitMiddleware:
    """Factory function to create rate limiter with custom settings"""
    return RateLimitMiddleware(
        calls_per_minute=calls_per_minute,
        calls_per_hour=calls_per_hour,
        concurrent_requests_per_user=concurrent_requests_per_user,
        redis_client=redis_client
    )

# Decorator for applying rate limits to specific endpoints
def rate_limit(calls_per_minute: int = 60, calls_per_hour: int = 1000):
    """Decorator for applying rate limits to specific FastAPI endpoints"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # This would need to be implemented based on specific needs
            # For now, rely on middleware for global rate limiting
            return await func(*args, **kwargs)
        return wrapper
    return decorator 