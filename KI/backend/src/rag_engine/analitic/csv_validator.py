"""
CSV Validation Module
Provides comprehensive validation for CSV files before database storage.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime
import re

logger = logging.getLogger(__name__)

class ValidationLevel(Enum):
    """Validation result levels"""
    SUCCESS = "success"
    WARNING = "warning" 
    ERROR = "error"

@dataclass
class ValidationIssue:
    """Represents a single validation issue"""
    level: ValidationLevel
    column: Optional[str]
    message: str
    count: Optional[int] = None
    percentage: Optional[float] = None
    examples: Optional[List[str]] = None

@dataclass
class CSVValidationResult:
    """Complete CSV validation result"""
    is_valid: bool
    should_save: bool
    overall_level: ValidationLevel
    summary: str
    issues: List[ValidationIssue]
    stats: Dict[str, Any]

class CSVValidator:
    """Comprehensive CSV validation class"""
    
    def __init__(self):
        self.critical_columns = ['id', 'customer_id', 'invoice_id', 'date', 'amount', 'price', 'quantity']
        self.max_missing_percentage = 30.0  # Maximum allowed missing percentage
        self.max_duplicate_percentage = 50.0  # Maximum allowed duplicate percentage
        
    def validate_csv(self, df: pd.DataFrame, filename: str = "uploaded_file.csv") -> CSVValidationResult:
        """
        Perform comprehensive CSV validation
        
        Args:
            df: Pandas DataFrame to validate
            filename: Original filename for context
            
        Returns:
            CSVValidationResult with validation details
        """
        issues = []
        stats = {
            'filename': filename,
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'validation_timestamp': datetime.now().isoformat()
        }
        
        # 1. Basic structure validation
        issues.extend(self._validate_structure(df))
        
        # 2. Missing values validation
        issues.extend(self._validate_missing_values(df))
        
        # 3. Data type validation
        issues.extend(self._validate_data_types(df))
        
        # 4. Duplicate validation
        issues.extend(self._validate_duplicates(df))
        
        # 5. Critical columns validation
        issues.extend(self._validate_critical_columns(df))
        
        # 6. Data consistency validation
        issues.extend(self._validate_data_consistency(df))
        
        # Determine overall result
        error_count = len([i for i in issues if i.level == ValidationLevel.ERROR])
        warning_count = len([i for i in issues if i.level == ValidationLevel.WARNING])
        
        if error_count > 0:
            overall_level = ValidationLevel.ERROR
            should_save = False
            is_valid = False
            summary = f"❌ HATA: CSV dosyası kaydedilemedi. {error_count} kritik sorun tespit edildi."
        elif warning_count > 0:
            overall_level = ValidationLevel.WARNING
            should_save = True
            is_valid = True
            summary = f"⚠️ UYARI: CSV dosyası kaydedildi ancak {warning_count} sorun tespit edildi."
        else:
            overall_level = ValidationLevel.SUCCESS
            should_save = True
            is_valid = True
            summary = f"✅ BAŞARILI: CSV dosyası başarıyla doğrulandı ve kaydedildi."
        
        stats.update({
            'error_count': error_count,
            'warning_count': warning_count,
            'total_issues': len(issues)
        })
        
        return CSVValidationResult(
            is_valid=is_valid,
            should_save=should_save,
            overall_level=overall_level,
            summary=summary,
            issues=issues,
            stats=stats
        )
    
    def _validate_structure(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate basic CSV structure"""
        issues = []
        
        # Check minimum requirements
        if len(df) == 0:
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                column=None,
                message="CSV dosyası boş - hiç veri satırı yok"
            ))
        elif len(df) < 2:
            issues.append(ValidationIssue(
                level=ValidationLevel.WARNING,
                column=None,
                message=f"Çok az veri satırı ({len(df)} satır) - analiz için yetersiz olabilir"
            ))
        
        if len(df.columns) == 0:
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                column=None,
                message="CSV dosyasında hiç sütun yok"
            ))
        elif len(df.columns) < 2:
            issues.append(ValidationIssue(
                level=ValidationLevel.WARNING,
                column=None,
                message=f"Çok az sütun ({len(df.columns)} sütun) - analiz için yetersiz olabilir"
            ))
        
        # Check for unnamed columns
        unnamed_cols = [col for col in df.columns if str(col).startswith('Unnamed:')]
        if unnamed_cols:
            issues.append(ValidationIssue(
                level=ValidationLevel.WARNING,
                column=None,
                message=f"İsimsiz sütunlar tespit edildi: {unnamed_cols}"
            ))
        
        return issues
    
    def _validate_missing_values(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate missing values in each column"""
        issues = []
        
        for column in df.columns:
            missing_count = df[column].isna().sum()
            missing_percentage = (missing_count / len(df)) * 100
            
            if missing_percentage > self.max_missing_percentage:
                issues.append(ValidationIssue(
                    level=ValidationLevel.WARNING,
                    column=column,
                    message=f"Yüksek eksik değer oranı",
                    count=missing_count,
                    percentage=missing_percentage
                ))
            elif missing_percentage > 0:
                issues.append(ValidationIssue(
                    level=ValidationLevel.SUCCESS,
                    column=column,
                    message=f"Kabul edilebilir eksik değer oranı",
                    count=missing_count,
                    percentage=missing_percentage
                ))
        
        return issues
    
    def _validate_data_types(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate data types and formats"""
        issues = []
        
        for column in df.columns:
            # Check for mixed data types
            sample_values = df[column].dropna().head(100).astype(str).tolist()
            
            if not sample_values:
                continue
                
            # Check for numeric columns with text
            if any(keyword in column.lower() for keyword in ['amount', 'price', 'quantity', 'count', 'number']):
                non_numeric = []
                negative_values = []
                for val in sample_values[:10]:
                    try:
                        numeric_val = float(str(val).replace(',', ''))
                        # Check for negative values in price/quantity columns
                        if numeric_val < 0 and any(keyword in column.lower() for keyword in ['price', 'quantity', 'amount']):
                            negative_values.append(val)
                    except (ValueError, TypeError):
                        non_numeric.append(val)

                if non_numeric:
                    issues.append(ValidationIssue(
                        level=ValidationLevel.ERROR,
                        column=column,
                        message=f"Sayısal sütunda metin değerler",
                        examples=non_numeric[:3]
                    ))

                if negative_values:
                    issues.append(ValidationIssue(
                        level=ValidationLevel.ERROR,
                        column=column,
                        message=f"Negatif değerler tespit edildi",
                        examples=negative_values[:3]
                    ))
            
            # Check for date columns
            if any(keyword in column.lower() for keyword in ['date', 'time', 'created', 'updated']):
                invalid_dates = []
                for val in sample_values[:10]:
                    if not self._is_valid_date_format(str(val)):
                        invalid_dates.append(val)
                
                if invalid_dates:
                    issues.append(ValidationIssue(
                        level=ValidationLevel.ERROR,
                        column=column,
                        message=f"Geçersiz tarih formatı",
                        examples=invalid_dates[:3]
                    ))
        
        return issues
    
    def _validate_duplicates(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate duplicate records"""
        issues = []
        
        duplicate_count = df.duplicated().sum()
        duplicate_percentage = (duplicate_count / len(df)) * 100
        
        if duplicate_percentage > self.max_duplicate_percentage:
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                column=None,
                message=f"Çok yüksek duplicate kayıt oranı - veri kalitesi düşük",
                count=duplicate_count,
                percentage=duplicate_percentage
            ))
        elif duplicate_percentage > 10:
            issues.append(ValidationIssue(
                level=ValidationLevel.WARNING,
                column=None,
                message=f"Yüksek duplicate kayıt oranı",
                count=duplicate_count,
                percentage=duplicate_percentage
            ))
        
        return issues
    
    def _validate_critical_columns(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate critical business columns"""
        issues = []
        
        # Check for critical columns
        found_critical = []
        for col in df.columns:
            if any(critical in col.lower() for critical in self.critical_columns):
                found_critical.append(col)
                
                # Check if critical column has too many missing values
                missing_percentage = (df[col].isna().sum() / len(df)) * 100
                if missing_percentage > 10:  # Stricter for critical columns
                    issues.append(ValidationIssue(
                        level=ValidationLevel.ERROR,
                        column=col,
                        message=f"Kritik sütunda çok fazla eksik değer",
                        percentage=missing_percentage
                    ))
        
        if not found_critical:
            issues.append(ValidationIssue(
                level=ValidationLevel.WARNING,
                column=None,
                message=f"Kritik iş sütunları tespit edilemedi (ID, tarih, miktar vb.)"
            ))
        
        return issues
    
    def _validate_data_consistency(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate data consistency and business rules"""
        issues = []
        
        # Check for negative values in amount/price columns
        for column in df.columns:
            if any(keyword in column.lower() for keyword in ['amount', 'price', 'total']):
                try:
                    numeric_col = pd.to_numeric(df[column], errors='coerce')
                    negative_count = (numeric_col < 0).sum()
                    if negative_count > 0:
                        issues.append(ValidationIssue(
                            level=ValidationLevel.WARNING,
                            column=column,
                            message=f"Negatif değerler tespit edildi",
                            count=negative_count
                        ))
                except:
                    pass
        
        return issues
    
    def _is_valid_date_format(self, date_str: str) -> bool:
        """Check if string is a valid date format"""
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'^\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
            r'^\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
            r'^\d{4}/\d{2}/\d{2}',  # YYYY/MM/DD
        ]
        
        for pattern in date_patterns:
            if re.match(pattern, str(date_str)):
                return True
        return False

def validate_csv_content(csv_content: str, delimiter: str = ',', filename: str = "uploaded_file.csv") -> CSVValidationResult:
    """
    Validate CSV content string
    
    Args:
        csv_content: CSV content as string
        delimiter: CSV delimiter
        filename: Original filename
        
    Returns:
        CSVValidationResult
    """
    try:
        # Parse CSV content
        import io
        df = pd.read_csv(io.StringIO(csv_content), delimiter=delimiter)
        
        # Create validator and validate
        validator = CSVValidator()
        return validator.validate_csv(df, filename)
        
    except Exception as e:
        return CSVValidationResult(
            is_valid=False,
            should_save=False,
            overall_level=ValidationLevel.ERROR,
            summary=f"❌ HATA: CSV dosyası okunamadı - {str(e)}",
            issues=[ValidationIssue(
                level=ValidationLevel.ERROR,
                column=None,
                message=f"CSV parsing hatası: {str(e)}"
            )],
            stats={'error': str(e)}
        )
