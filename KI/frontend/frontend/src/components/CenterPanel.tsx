import React, { useState, useRef, useEffect, useCallback } from 'react';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import SendIcon from '@mui/icons-material/Send';
import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ListSubheader from '@mui/material/ListSubheader';
import Divider from '@mui/material/Divider';
import LinearProgress from '@mui/material/LinearProgress';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import TableChartIcon from '@mui/icons-material/TableChart';
import DescriptionIcon from '@mui/icons-material/Description';
import TaskAltIcon from '@mui/icons-material/TaskAlt';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import StorageIcon from '@mui/icons-material/Storage';
import CloudQueueIcon from '@mui/icons-material/CloudQueue';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

import {
  uploadFile,
  loginUser,
  sendMessageToAgent,
  AnalyzeResponse,
  getChatHistory
} from '../app/services/api';
import { Plan, ChatMessage } from './types';
import ChartDisplay from './ChartDisplay';

interface CenterPanelProps {
  userId: string;
  currentChatId: string;
  isLoading: boolean;
  lastAgentMessage: ChatMessage | null;
  clearLastAgentMessage: () => void;
  onLogin: (userId: string, username: string) => void;
  onTaskKeyChange?: (taskKey: string | null) => void;
  onTaskStart: () => void;
  onInitialTaskData?: (taskData: {
    task_key: string;
    plan?: {
      reason: string;
      task_list: string[];
      agents?: Array<{
        agent_id: string;
        agent_name: string;
        task: string;
        reason: string;
      }>;
    };
  }) => void;
}

const CenterPanel: React.FC<CenterPanelProps> = ({
  userId: propUserId,
  currentChatId: propCurrentChatId,
  isLoading,
  lastAgentMessage,
  clearLastAgentMessage,
  onLogin,
  onTaskKeyChange,
  onTaskStart,
  onInitialTaskData
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for file attachment dialog
  const [attachmentDialogOpen, setAttachmentDialogOpen] = useState(false);
  const [selectedFileType, setSelectedFileType] = useState<'csv' | 'excel' | 'document' | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [fileUploading, setFileUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [uploadProgress, setUploadProgress] = useState({ current: 0, total: 0 });

  // User state (only used for login)
  const [username, setUsername] = useState<string>('');
  const [loginDialogOpen, setLoginDialogOpen] = useState(!propUserId);
  const [loginError, setLoginError] = useState('');
  const [isUserLoading, setIsUserLoading] = useState(false);

  // User profile menu
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);
  const profileMenuOpen = Boolean(profileMenuAnchor);

  // Effect to update login dialog state when userId prop changes
  useEffect(() => {
    // Close login dialog if user is logged in
    if (propUserId) {
      setLoginDialogOpen(false);
    } else {
      // Only show login dialog if user is not logged in
      setLoginDialogOpen(true);

      // Check if we have a stored username to pre-fill the field
      const storedUsername = localStorage.getItem('username');
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, [propUserId]);

  const scrollToBottom = (behavior: "smooth" | "auto" = "smooth") => {
    messagesEndRef.current?.scrollIntoView({ behavior });
  }

  // Effect to scroll to bottom when messages change
  useEffect(() => {
    // Scroll immediately after history load or new message
    scrollToBottom('auto');
  }, [messages]);

  // Function to load/refresh chat history
  const loadChatHistory = useCallback(async (chatId: string | null) => {
    if (!chatId) {
      setMessages([]);
      setIsHistoryLoading(false);
      return;
    }
    console.log(`Refreshing history for chat: ${chatId}`);
    setIsHistoryLoading(true);
    // Do NOT clear messages here anymore
    // setMessages([]);

    try {
      const historyResponse = await getChatHistory(chatId);
      if (historyResponse.success && Array.isArray(historyResponse.messages)) {
         // Ensure we create a new array reference and sort
        const sortedMessages = [...historyResponse.messages].sort((a, b) => a.timestamp - b.timestamp);
        console.log('[loadChatHistory] Setting messages:', sortedMessages);
        // Set state directly with fetched history
        setMessages(sortedMessages);
      } else {
        console.error('Failed to refresh chat history or invalid format');
        // Set to empty array only on failure/invalid format after trying
        setMessages([]);
      }
    } catch (error) {
      console.error('Error during chat history refresh:', error);
      // Set to empty array only on error after trying
      setMessages([]);
    } finally {
      setIsHistoryLoading(false);
    }
  }, [setMessages, setIsHistoryLoading]);

  // Effect to log messages state when it changes
  useEffect(() => {
    console.log('[State Update] Messages state changed:', messages);
  }, [messages]);

  // Effect for initial history load when chat changes
  useEffect(() => {
    loadChatHistory(propCurrentChatId);
  }, [propCurrentChatId, loadChatHistory]);

  // Effect to add the last agent message when the prop changes
  useEffect(() => {
    if (lastAgentMessage) {
      console.log('[CenterPanel] Received new lastAgentMessage prop:', {
        ...lastAgentMessage,
        hasTaskKey: !!lastAgentMessage.taskKey,
        isTaskOutput: !!lastAgentMessage.isTaskOutput,
        textLength: lastAgentMessage.text ? lastAgentMessage.text.length : 0
      });
      
      setMessages(prevMessages => {
        // Ensure lastAgentMessage has taskKey and isTaskOutput for this logic path
        if (lastAgentMessage.taskKey && lastAgentMessage.isTaskOutput) {
          const placeholderIndex = prevMessages.findIndex(
            msg => msg.taskKey === lastAgentMessage.taskKey && !msg.isTaskOutput
          );

          if (placeholderIndex !== -1) {
            // Update placeholder
            const updatedMessages = [...prevMessages];
            updatedMessages[placeholderIndex] = {
              ...prevMessages[placeholderIndex], // Preserve original id, timestamp, initial plan
              text: lastAgentMessage.text, // Update text
              error: lastAgentMessage.error, // Update error status
              isTaskOutput: true, // Mark as final output
              // If lastAgentMessage might carry new sources or an updated plan, handle here:
              // sources: lastAgentMessage.sources || prevMessages[placeholderIndex].sources,
              // plan: lastAgentMessage.plan || prevMessages[placeholderIndex].plan,
            };
            console.log('[CenterPanel] Updated placeholder message for taskKey:', lastAgentMessage.taskKey);
            return updatedMessages;
          } else {
            // No placeholder found, or it was already final. Add as new if not a true duplicate.
            const isTrulyDuplicate = prevMessages.some(msg =>
              msg.taskKey === lastAgentMessage.taskKey &&
              msg.isTaskOutput && // Crucially, check if existing message is ALSO a final output
              msg.text === lastAgentMessage.text &&
              msg.sender === lastAgentMessage.sender
            );
            if (!isTrulyDuplicate) {
              console.log('[CenterPanel] Adding new final message (no suitable placeholder or not duplicate final) for taskKey:', lastAgentMessage.taskKey);
              return [...prevMessages, lastAgentMessage];
            } else {
              console.log('[CenterPanel] Duplicate final message detected for taskKey, not adding:', lastAgentMessage.taskKey);
              return prevMessages;
            }
          }
        } else {
          // Fallback for messages that are not final task outputs or don't have a taskKey
          const isSimpleDuplicate = prevMessages.some(msg =>
            msg.sender === lastAgentMessage.sender && msg.text === lastAgentMessage.text
          );
          if (!isSimpleDuplicate) {
            console.log('[CenterPanel] Adding non-task-specific or non-final message (simple duplicate check).');
            return [...prevMessages, lastAgentMessage];
          } else {
            console.log('[CenterPanel] Simple duplicate (non-task, non-final) detected, not adding.');
            return prevMessages;
          }
        }
      });
      clearLastAgentMessage();
    }
  }, [lastAgentMessage, clearLastAgentMessage, setMessages]);

  // Handle user login
  const handleLogin = async () => {
    // Input validation
    if (!username.trim()) {
      setLoginError('Kullanıcı adı gereklidir');
      return;
    }

    if (username.trim().length < 3) {
      setLoginError('Kullanıcı adı en az 3 karakter olmalıdır');
      return;
    }

    setIsUserLoading(true);
    setLoginError('');

    try {
      // The API will check if user exists and return the UUID, or create a new one if needed
      // This enables persistent sessions via localStorage without requiring login each time
      const response = await loginUser(username);

      if (response && response.user_id) {
        // Call the parent component's onLogin function which will:
        // 1. Update the app state with user info
        // 2. Store data in localStorage for persistent sessions
        onLogin(response.user_id, username);
        setLoginDialogOpen(false);
      } else {
        throw new Error('Kullanıcı ID alınamadı');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      if (error.response?.status === 422) {
        // Validation error from backend
        setLoginError(error.response?.data?.detail || 'Geçersiz kullanıcı adı');
      } else if (error.response?.status === 503) {
        // Database error
        setLoginError('Veritabanı bağlantı hatası. Lütfen daha sonra tekrar deneyin.');
      } else {
        // Generic error
        setLoginError(error.response?.data?.detail || 'Giriş sırasında bir hata oluştu');
      }
    } finally {
      setIsUserLoading(false);
    }
  };

  // Handle logout
  const handleLogout = () => {
    // Clear local storage
    localStorage.removeItem('userId');
    localStorage.removeItem('username');

    // Reset state
    setUsername('');
    setMessages([]);

    // Close menu
    setProfileMenuAnchor(null);

    // Show login dialog
    setLoginDialogOpen(true);
  };

  // Handle chat selection
  const handleSelectChat = (chatId: string) => {
    // Here you would load messages for the selected chat
    setMessages([]);
  };

  const handleSend = async () => {
    if (currentInput.trim() === '' || isLoading || !propUserId || !propCurrentChatId) return;

    const newUserMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      sender: 'user',
      text: currentInput.trim(),
      timestamp: Date.now(),
    };
    setMessages(prev => [...prev, newUserMessage]);
    const messageToSend = currentInput.trim();
    setCurrentInput('');
    onTaskStart();

    try {
      const agentApiResponse: AnalyzeResponse = await sendMessageToAgent(propUserId, propCurrentChatId, messageToSend);

      // Check if the response is successful (not failed)
      if (agentApiResponse.status !== 'failed') {
        console.log('✅ CenterPanel: Successful analyze response:', agentApiResponse);
        
        // Pass initial task data to RightPanel via parent
        if (onInitialTaskData && agentApiResponse.task_key) {
          onInitialTaskData({
            task_key: agentApiResponse.task_key,
            plan: agentApiResponse.plan
          });
        }
        
        // Set task key for tracking
        if (onTaskKeyChange && agentApiResponse.task_key) {
          onTaskKeyChange(agentApiResponse.task_key);
        }

        const agentMessageText = agentApiResponse.message || "Yanıt işleniyor...";
        const newAgentMessage: ChatMessage = {
          id: `agent-${Date.now()}`,
          sender: 'agent',
          text: agentMessageText,
          timestamp: Date.now(),
          error: false,
          isTaskOutput: false,
          taskKey: agentApiResponse.task_key,
          plan: agentApiResponse.plan
        };
        setMessages(prev => [...prev, newAgentMessage]);
      } else {
        const errorMessage = agentApiResponse?.error || 'Agent\'ten beklenen formatta bir yanıt alınamadı veya işlem başarısız oldu.';
        const errorAgentMessage: ChatMessage = {
          id: `agent-error-${Date.now()}`,
          sender: 'agent',
          text: errorMessage,
          timestamp: Date.now(),
          error: true,
        };
        setMessages(prev => [...prev, errorAgentMessage]);
        if (onTaskKeyChange) {
          onTaskKeyChange(null);
        }
      }

    } catch (error: any) {
      console.error('Error sending message:', error);
      const catchErrorMessageText = 'Mesaj gönderilirken bir hata oluştu: ' + (error?.message || 'Bilinmeyen bir istemci tarafı hatası');
      const catchErrorMessage: ChatMessage = {
        id: `agent-catch-error-${Date.now()}`,
        sender: 'agent',
        text: catchErrorMessageText,
        timestamp: Date.now(),
        error: true,
      };
      setMessages(prev => [...prev, catchErrorMessage]);
      if (onTaskKeyChange) {
          onTaskKeyChange(null);
      }
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentInput(event.target.value);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  // --- File Attachment Handlers

  const handleOpenAttachmentDialog = () => {
    setAttachmentDialogOpen(true);
    setFileUploading(false);
    setUploadSuccess(false);
    setUploadError('');
    setSelectedFile(null);
    setSelectedFiles([]);
    setSelectedFileType(null);
    setUploadProgress({ current: 0, total: 0 });
  };

  const handleCloseAttachmentDialog = () => {
    setAttachmentDialogOpen(false);
  };

  const handleFileTypeSelect = (fileType: 'csv' | 'excel' | 'document') => {
    setSelectedFileType(fileType);
    let acceptAttr = '';
    if (fileType === 'csv') {
      acceptAttr = '.csv,.xlsx,.xls';  // CSV seçeneği artık hem CSV hem Excel kabul ediyor
    } else if (fileType === 'excel') {
      acceptAttr = '.xlsx,.xls';
    } else if (fileType === 'document') {
      acceptAttr = '.pdf,.doc,.docx';
    }
    
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('accept', acceptAttr);
      fileInputRef.current.setAttribute('multiple', 'true');
      fileInputRef.current.click();
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0 && selectedFileType) {
      const fileArray = Array.from(files);
      setSelectedFiles(fileArray);
      setSelectedFile(fileArray[0]);
      setFileUploading(true);
      setUploadSuccess(false);
      setUploadError('');
      setUploadProgress({ current: 0, total: fileArray.length });

      try {
        for (let i = 0; i < fileArray.length; i++) {
          const file = fileArray[i];
          setSelectedFile(file);
          setUploadProgress({ current: i, total: fileArray.length });

          if (selectedFileType === 'csv' || selectedFileType === 'excel') {
            await new Promise<void>((resolve, reject) => {
              // For CSV and Excel, we just need to upload the file.
              // The backend will handle processing.
              (async () => {
                try {
                  const uploadResponse = await uploadFile(propCurrentChatId, propUserId, file, selectedFileType);
                  console.log(`✅ ${selectedFileType.toUpperCase()} upload successful:`, uploadResponse);

                  if (onTaskStart) {
                    onTaskStart();
                  }

                  // Set task key for tracking if available
                  if (onTaskKeyChange && uploadResponse.task_key) {
                    console.log('🔑 Setting task key from CSV/Excel upload:', uploadResponse.task_key);
                    onTaskKeyChange(uploadResponse.task_key);
                  }

                  // Pass initial task data to RightPanel if task_key is available
                  if (onInitialTaskData && uploadResponse.task_key) {
                    onInitialTaskData({
                      task_key: uploadResponse.task_key,
                      plan: undefined // CSV/Excel upload doesn't have initial plan
                    });
                  }

                  resolve();
                } catch (error) {
                  console.error('File upload failed:', error);
                  reject(error);
                }
              })();
            });
          } else if (selectedFileType === 'document') {
            try {
              await new Promise<void>((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = async (e) => {
                  try {
                    const content = e.target?.result;
                    if (!content) {
                      throw new Error("Could not read file content.");
                    }
                    // Determine specific file type based on extension
                    const fileName = file.name.toLowerCase();
                    let specificFileType: 'PDF' | 'DOC' | 'DOCX' = 'PDF'; // default fallback
                    if (fileName.endsWith('.pdf')) {
                      specificFileType = 'PDF';
                    } else if (fileName.endsWith('.doc')) {
                      specificFileType = 'DOC';
                    } else if (fileName.endsWith('.docx')) {
                      specificFileType = 'DOCX';
                    }

                    // Send the specific file type to the backend
                    const uploadResponse = await uploadFile(propCurrentChatId, propUserId, file, specificFileType);
                    console.log('✅ Document upload successful:', uploadResponse);

                    // Trigger task start callback to enable WebSocket listening
                    if (onTaskStart) {
                      console.log('🎯 Triggering task start for document upload');
                      onTaskStart();
                    }

                    // Set task key for tracking if available (documents usually don't have immediate task_key)
                    if (onTaskKeyChange && uploadResponse.task_key) {
                      console.log('🔑 Setting task key from document upload:', uploadResponse.task_key);
                      onTaskKeyChange(uploadResponse.task_key);
                    }

                    resolve();
                  } catch (error) {
                    console.error('Document upload failed:', error);
                    reject(error);
                  }
                };
                reader.onerror = (error) => {
                  console.error("File reading error:", error);
                  reject(error);
                };
                reader.readAsArrayBuffer(file);
              });
            } catch (error) {
              console.error('Document upload failed:', error);
              setUploadError('Dosya yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
            }
          }
        }

        setUploadSuccess(true);
        setTimeout(() => {
          handleCloseAttachmentDialog();
        }, 1500);
      } catch (error) {
        console.error('File upload failed:', error);
        setUploadError('Dosya yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      } finally {
        setFileUploading(false);
      }
    }
    if (e.target) {
       e.target.value = '';
    }
  };

  return (
    <div className="center-panel">
      {/* Message Display Area */}
      <Box sx={{ flexGrow: 1, overflowY: 'auto', p: 2, mb: 1, position: 'relative' }}>
        {/* History Loading Indicator */}
        {isHistoryLoading && (
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            p: 1,
            display: 'flex',
            justifyContent: 'center',
            bgcolor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 1
          }}>
            <CircularProgress size={20} />
            <Typography variant="caption" sx={{ ml: 1 }}>Geçmiş yükleniyor...</Typography>
          </Box>
        )}

        {/* Render Messages */}
        {!isHistoryLoading && messages.length === 0 && propCurrentChatId && (
          <Typography sx={{ textAlign: 'center', color: 'text.secondary', p: 3 }}>
            Sohbet geçmişi boş veya yüklenemedi.
          </Typography>
        )}
        {messages.map((msg) => (
          <Paper
            key={msg.id}
            elevation={0}
            sx={{
              p: '10px 14px',
              mb: 1.5,
              maxWidth: msg.isTaskOutput ? '95%' : '75%',
              ml: msg.sender === 'user' ? 'auto' : '0',
              mr: msg.sender === 'agent' ? 'auto' : '0',
              bgcolor: msg.sender === 'user' ? 'primary.main' : (msg.error ? 'error.light' : 'background.paper'),
              color: msg.sender === 'user' ? 'primary.contrastText' : (msg.error ? 'error.contrastText' : 'text.primary'),
              borderRadius: msg.sender === 'user'
                ? theme => `${theme.shape.borderRadius}px ${theme.shape.borderRadius}px 4px ${theme.shape.borderRadius}px`
                : theme => `${theme.shape.borderRadius}px ${theme.shape.borderRadius}px ${theme.shape.borderRadius}px 4px`,
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
            }}
          >
            {msg.isTaskOutput ? (
              <ReactMarkdown>{msg.text}</ReactMarkdown>

            ) : (
              <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>{msg.text}</Typography>
            )}

            {/* Render charts if available */}
            {msg.charts && msg.charts.length > 0 && (
              <Box sx={{ mt: 2 }}>
                {msg.charts.map((chart, index) => (
                  <Box key={chart.id || index} sx={{ mb: 2 }}>
                    <ChartDisplay
                      chartType={chart.type}
                      data={chart.data}
                      options={chart.options}
                    />
                  </Box>
                ))}
              </Box>
            )}

            {msg.sources && msg.sources.length > 0 && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Kaynaklar:</Typography>
              </Box>
            )}
            {msg.plan && (
              <Box sx={{ mt: 1.5, p: 1.5, bgcolor: 'rgba(0, 0, 0, 0.05)', borderRadius: 1 }}>
                 <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>Planlama:</Typography>
                 <Typography variant="body2" sx={{ fontStyle: 'italic', mb: 1 }}>{msg.plan.reason}</Typography>
                 <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>Görevler:</Typography>
                 <List dense disablePadding>
                    {msg.plan.task_list.map((task, index) => (
                      <ListItem key={index} sx={{ p: 0 }}>
                         <ListItemIcon sx={{ minWidth: '20px' }}>
                            <TaskAltIcon fontSize="small" color="action" />
                          </ListItemIcon>
                        <ListItemText primaryTypographyProps={{ variant: 'body2' }} primary={task} />
                      </ListItem>
                    ))}
                  </List>
              </Box>
            )}
            <Typography variant="caption" display="block" sx={{ mt: 0.5, textAlign: 'right', fontSize: '0.65rem', opacity: 0.7 }}>
              {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute:'2-digit' })}
            </Typography>
          </Paper>
        ))}
        <div ref={messagesEndRef} />
      </Box>

      {/* Loading Indicator for Sending */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
          <CircularProgress size={24} />
        </Box>
      )}

      {/* Input Area */}
      <Paper
        elevation={2}
        square
        sx={{
            display: 'flex',
            alignItems: 'center',
            p: 1.5,
            bgcolor: 'background.paper'
        }}
      >
        {/* Hidden file input */}
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          onChange={handleFileSelect}
        />
        {/* Attach file button */}
        <Tooltip title="Dosya ekle (birden fazla seçilebilir)">
          <span>
            <IconButton
              color="primary"
              onClick={handleOpenAttachmentDialog}
              disabled={isLoading || !propUserId || !propCurrentChatId}
              sx={{ p: 1, mr: 1 }}
            >
              <AttachFileIcon />
            </IconButton>
          </span>
        </Tooltip>

        {/* Text Field */}
        <TextField
          fullWidth
          variant="outlined"
          size="small"
          placeholder={propUserId && propCurrentChatId ? "Sorunuzu yazın..." : "Giriş yapmanız gerekiyor..."}
          value={currentInput}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          disabled={isLoading || !propUserId || !propCurrentChatId}
          multiline
          maxRows={4}
          sx={{
            mr: 1.5,
            bgcolor: 'background.default',
            '& .MuiOutlinedInput-root': {
                '& fieldset': {
                    borderColor: 'transparent',
                },
                 '&:hover fieldset': {
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                },
                '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                },
            },
          }}
        />
        <Button
          variant="contained"
          color="primary"
          onClick={handleSend}
          disabled={isLoading || currentInput.trim() === '' || !propUserId || !propCurrentChatId}
          endIcon={<SendIcon />}
          sx={{ flexShrink: 0 }}
        >
          Gönder
        </Button>
      </Paper>

      {/* File Attachment Dialog */}
      <Dialog
        open={attachmentDialogOpen}
        onClose={handleCloseAttachmentDialog}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={fileUploading}
      >
       <DialogTitle>
          {fileUploading ? 'Dosya Yükleniyor...' :
           uploadSuccess ? 'Yükleme Başarılı' :
           'Veri Kaynağı Seçin'}
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {fileUploading ? (
            <Box display="flex" flexDirection="column" alignItems="center" p={3}>
              <CircularProgress />
              <Typography variant="body1" sx={{ mt: 2 }}>
                {uploadProgress.total > 1
                  ? `Dosya yükleniyor... (${uploadProgress.current + 1}/${uploadProgress.total})`
                  : 'Dosya yükleniyor...'}
              </Typography>
              {selectedFile && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {selectedFile.name}
                </Typography>
              )}
              {uploadProgress.total > 1 && (
                <Box sx={{ width: '100%', mt: 2 }}>
                  <LinearProgress
                    variant="determinate"
                    value={(uploadProgress.current + 1) / uploadProgress.total * 100}
                  />
                </Box>
              )}
            </Box>
          ) : uploadSuccess ? (
            <Box display="flex" flexDirection="column" alignItems="center" p={3}>
              <Typography
                variant="body1"
                color="success.main"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <TaskAltIcon />
                {selectedFiles.length > 1
                  ? `${selectedFiles.length} dosya başarıyla yüklendi!`
                  : 'Dosya başarıyla yüklendi!'}
              </Typography>
            </Box>
          ) : uploadError ? (
            <Box display="flex" flexDirection="column" alignItems="center" p={3}>
              <Typography
                variant="body1"
                color="error"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <ErrorOutlineIcon />
                {uploadError}
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleOpenAttachmentDialog}
                sx={{ mt: 2 }}
              >
                Tekrar Dene
              </Button>
            </Box>
          ) : (
            <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
              <ListSubheader sx={{ bgcolor: 'inherit' }}>Dosya Yükle</ListSubheader>
              <ListItem button onClick={() => handleFileTypeSelect('csv')}>
                <ListItemIcon>
                  <TableChartIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="CSV/Excel Veri Dosyası"
                  secondary="CSV ve Excel tablolar (.csv, .xlsx formatları, birden fazla seçilebilir)"
                />
              </ListItem>
              <ListItem button onClick={() => handleFileTypeSelect('document')}>
                <ListItemIcon>
                  <DescriptionIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Doküman"
                  secondary="PDF, Word vb. (birden fazla seçilebilir)"
                />
              </ListItem>

              <Divider sx={{ my: 1 }} />

              <ListSubheader sx={{ bgcolor: 'inherit' }}>CRM/CMS Entegrasyonları</ListSubheader>
              <ListItem disabled>
                <ListItemIcon>
                  <StorageIcon />
                </ListItemIcon>
                <ListItemText primary="Salesforce" secondary="Yakında" />
              </ListItem>
              <ListItem disabled>
                <ListItemIcon>
                   <StorageIcon />
                </ListItemIcon>
                <ListItemText primary="Microsoft Dynamics 365" secondary="Yakında" />
              </ListItem>
               <ListItem disabled>
                <ListItemIcon>
                   <StorageIcon />
                </ListItemIcon>
                <ListItemText primary="HubSpot" secondary="Yakında" />
              </ListItem>
               <ListItem disabled>
                <ListItemIcon>
                   <StorageIcon />
                </ListItemIcon>
                <ListItemText primary="Zoho" secondary="Yakında" />
              </ListItem>

              <Divider sx={{ my: 1 }} />

              <ListSubheader sx={{ bgcolor: 'inherit' }}>Bulut Depolama</ListSubheader>
              <ListItem disabled>
                <ListItemIcon>
                  <CloudQueueIcon />
                </ListItemIcon>
                <ListItemText primary="Google Drive" secondary="Yakında" />
              </ListItem>
              <ListItem disabled>
                <ListItemIcon>
                  <CloudQueueIcon />
                </ListItemIcon>
                <ListItemText primary="OneDrive" secondary="Yakında" />
              </ListItem>
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseAttachmentDialog}
            disabled={fileUploading}
          >
            {uploadSuccess ? 'Kapat' : 'İptal'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Login Dialog - only render when loginDialogOpen is true */}
      {loginDialogOpen && (
        <Dialog
          open={true}
          onClose={() => {
            if (!isUserLoading && propUserId) setLoginDialogOpen(false);
          }}
          maxWidth="xs"
          fullWidth
          disableEscapeKeyDown={isUserLoading}
        >
          <DialogTitle>{username ? 'Tekrar Hoş Geldiniz' : 'Kullanıcı Girişi'}</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {username ?
                  'Önceki oturumunuz hatırlandı. Devam etmek için giriş yapın.' :
                  'Kullanıcı adınızı girin. Eğer hesabınız yoksa, otomatik olarak oluşturulacaktır.'
                }
              </Typography>
              <TextField
                margin="normal"
                required
                fullWidth
                id="username"
                label="Kullanıcı Adı"
                name="username"
                autoComplete="username"
                autoFocus
                value={username}
                onChange={(e) => {
                  setUsername(e.target.value);
                  setLoginError('');
                }}
                error={!!loginError}
                helperText={loginError}
                disabled={isUserLoading}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleLogin();
                  }
                }}
                inputProps={{ minLength: 3 }}
              />
            </Box>
            {isUserLoading && (
              <Box display="flex" flexDirection="column" alignItems="center" mt={2}>
                <CircularProgress size={24} />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Giriş yapılıyor...
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleLogin}
              disabled={isUserLoading || !username.trim() || username.trim().length < 3}
              fullWidth
            >
              {username ? 'Devam Et' : 'Giriş Yap'}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </div>
  );
};

export default CenterPanel;