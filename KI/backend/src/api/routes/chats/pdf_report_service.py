import json
import os
import re
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml
from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat
from sqlalchemy import text

from src.agents.agno_agents.memory_manager import MemoryManager
from src.agents.agno_agents.tool_manager import AgnoToolManager
from src.agents.agno_agents.user_context_manager import create_user_context_hook
from src.api.routes.chats.chat_service import get_chat_messages
from src.rag_engine.analitic.db.postgresql.database import get_session


class PDFReportService:
    def __init__(self):
        project_root = Path.cwd()
        config_path = (
            project_root
            / "src"
            / "agents"
            / "agno_agents"
            / "config"
            / "pdf_report_agent.yml"
        )
        with open(config_path, "r", encoding="utf-8") as f:
            pdf_report_agent_config = yaml.safe_load(f)

        self.agent_config = pdf_report_agent_config.get("agent", {})

        # Manually initialize tool manager to bypass AgentFactory __init__
        tool_manager = AgnoToolManager()
        self.tools_map = tool_manager.get_all_tool_objects_map()

    def _create_model_instance(
        self, model_config: Optional[Dict] = None
    ) -> Union[Claude, OpenAIChat]:
        """
        Creates a model instance based on configuration.
        Logic copied from AgentFactory._get_model to bypass factory bug.
        """
        effective_model_config = model_config if model_config is not None else {}

        provider = effective_model_config.get(
            "provider", os.getenv("LLM_PROVIDER", "openai")
        ).lower()
        model_id = effective_model_config.get("model", os.getenv("LLM_MODEL"))
        api_key = effective_model_config.get("api_key", os.getenv("LLM_API_KEY"))
        base_url = effective_model_config.get("base_url", os.getenv("LLM_URL"))

        if not model_id:
            raise ValueError(
                "LLM_MODEL environment variable or agent config's 'model' key must be set."
            )

        if provider == "anthropic":
            return Claude(
                id=model_id,
                api_key=api_key,
                base_url=base_url,
            )
        # Default to OpenAI
        return OpenAIChat(
            id=model_id,
            api_key=api_key,
            base_url=base_url,
            max_tokens=8000,  # Adjusted for report generation
            temperature=0.2,  # Lower temperature for more deterministic LaTeX
        )

    def _slugify(self, text: str) -> str:
        """
        Converts a string into a URL-friendly slug.
        """
        text = text.lower()
        text = (
            text.replace("ı", "i")
            .replace("ğ", "g")
            .replace("ü", "u")
            .replace("ş", "s")
            .replace("ö", "o")
            .replace("ç", "c")
        )
        text = re.sub(r"[^a-z0-9_]+", "_", text)
        text = re.sub(r"__+", "_", text)
        return text.strip("_")

    def _execute_sql(self, sql_query: str) -> Optional[Dict[str, List]]:
        """
        Executes a raw SQL query and returns data formatted for Chart.js.
        """
        db_session = get_session()
        try:
            result = db_session.execute(text(sql_query))
            rows = result.fetchall()
            if not rows:
                return None

            labels = [row[0] for row in rows]
            data = [row[1] for row in rows]

            return {"labels": labels, "data": data}
        except Exception as e:
            print(f"Error executing SQL for chart: {e}")
            return None
        finally:
            db_session.close()

    def _generate_chart_images(
        self, messages: List[Dict[str, Any]], temp_path: Path
    ) -> List[Dict[str, str]]:
        """
        Renders charts from messages to PNG images using a Node.js script.
        """
        chart_images = []
        chart_count = 1

        for msg in messages:
            if (
                msg.get("sender") == "agent"
                and msg.get("assets")
                and "charts" in msg["assets"]
            ):
                for chart_asset in msg["assets"]["charts"]:
                    chart_json = chart_asset.get("chart_schema")
                    chart_title = chart_asset.get("table_name", f"Chart {chart_count}")

                    if not chart_json:
                        continue

                    if "data_sql" in chart_asset and chart_asset["data_sql"]:
                        print(f"Executing SQL for chart: {chart_title}")
                        chart_data = self._execute_sql(chart_asset["data_sql"])
                        if chart_data:
                            chart_json["data"]["labels"] = chart_data["labels"]
                            if chart_json["data"]["datasets"]:
                                chart_json["data"]["datasets"][0]["data"] = chart_data[
                                    "data"
                                ]

                    filename = f"{self._slugify(chart_title)}_{chart_count}.png"
                    image_path = temp_path / filename
                    chart_config_str = json.dumps(chart_json)
                    renderer_script = (
                        Path.cwd() / "src" / "chart_renderer" / "render.js"
                    )

                    try:
                        print(f"Rendering chart '{chart_title}' to {image_path}...")
                        subprocess.run(
                            [
                                "node",
                                str(renderer_script),
                                str(image_path),
                                chart_config_str,
                            ],
                            check=True,
                            capture_output=True,
                            text=True,
                            encoding="utf-8",
                        )
                        chart_images.append({"filename": filename, "title": chart_title})
                        chart_count += 1
                        print("...Rendering complete.")
                    except subprocess.CalledProcessError as e:
                        print(f"Error rendering chart '{chart_title}':")
                        print(f"STDOUT: {e.stdout}")
                        print(f"STDERR: {e.stderr}")
                    except FileNotFoundError:
                        print(
                            "Error: 'node' command not found. Ensure Node.js is installed."
                        )

        return chart_images

    def _compile_latex_to_pdf(
        self, latex_content: str, chat_id: str, temp_path: Path
    ) -> Optional[Path]:
        """Compile LaTeX content to a PDF file in the given temporary directory."""
        if not latex_content.strip():
            return None

        tex_file_path = temp_path / f"report_{chat_id}.tex"
        pdf_path = temp_path / f"report_{chat_id}.pdf"

        with open(tex_file_path, "w", encoding="utf-8") as f:
            f.write(latex_content)

        try:
            print("\n--- Compiling Final LaTeX Document ---")
            for _ in range(2):
                subprocess.run(
                    [
                        "pdflatex",
                        "-interaction=nonstopmode",
                        "-output-directory",
                        str(temp_path),
                        str(tex_file_path),
                    ],
                    check=True,
                    capture_output=True,
                    text=True,
                    encoding="utf-8",
                )
            print(f"Successfully compiled PDF: {pdf_path}")
            return pdf_path
        except subprocess.CalledProcessError as e:
            log_path = tex_file_path.with_suffix(".log")
            if log_path.exists():
                print(
                    f"PDF compilation failed. See log file for details: {log_path.read_text()}"
                )
            else:
                print(f"PDF compilation failed. LaTeX log:\n{e.output}")
            return None
        except FileNotFoundError:
            print(
                "Error: 'pdflatex' command not found. Ensure a TeX distribution is installed."
            )
            return None

    def _store_pdf_report(self, pdf_path: Path, chat_id: str):
        """Moves the compiled PDF to the final reports directory."""
        if not pdf_path or not pdf_path.exists():
            print("Storing PDF failed: source file does not exist.")
            return

        reports_dir = Path.cwd() / "reports" / "pdf"
        reports_dir.mkdir(parents=True, exist_ok=True)
        final_pdf_path = reports_dir / f"report_{chat_id}.pdf"

        try:
            shutil.move(str(pdf_path), str(final_pdf_path))
            print(f"Report saved to: {final_pdf_path}")
        except Exception as e:
            print(f"Error moving PDF report: {e}")

    async def generate_pdf_report(self, chat_id: str) -> None:
        """
        Generates a PDF report for a given chat session, including charts.
        """
        # Step 1: Retrieve chat messages
        chat_messages, _ = await get_chat_messages(chat_id)
        if not chat_messages:
            print("No messages found for this chat session.")
            return

        # Step 2: Create a temporary directory for all artifacts
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Step 3: Generate chart images from chat history
            print("--- Generating Chart Images ---")
            chart_images = self._generate_chart_images(chat_messages, temp_path)

            # Step 4: Manually create the agent to bypass the faulty factory
            print("\n--- Creating Agent Instance ---")
            agent_config = self.agent_config
            model = self._create_model_instance(agent_config)
            tool_names = agent_config.get("tools", [])
            selected_tools = [
                self.tools_map[name] for name in tool_names if name in self.tools_map
            ]

            agent = Agent(
                name=agent_config.get("name", "PDF Report Agent"),
                role=agent_config.get("role", ""),
                model=model,
                tools=selected_tools,
                enable_user_memories=True,
                memory=MemoryManager().get_memory_instance(),
                instructions=agent_config.get("instructions", []),
                markdown=True,
                show_tool_calls=True,
                debug_mode=True,
                tool_hooks=[create_user_context_hook()],
            )

            print("\n--- Generating LaTeX Content from Agent ---")
            prompt = "Aşağıdaki sohbet geçmişini analiz ederek bir PDF raporu için LaTeX içeriği oluşturun.\n"
            prompt += "Standart LaTeX paketlerini kullanın (article, inputenc, fontenc, graphicx, geometry, babel, hyperref).\n"
            prompt += "Önemli: Yanıtınızda sadece ve sadece geçerli LaTeX kodu bulunmalıdır. Açıklama veya kod bloğu işaretleri eklemeyin.\n"
            
            if chart_images:
                prompt += "\nKULLANILABİLİR GRAFİK DOSYALARI:\n"
                prompt += "Bu grafikleri \\includegraphics{{dosya_adi}} komutuyla rapora ekleyebilirsiniz. Sadece bu listede verilen dosya adlarını kullanın.\n"
                for chart in chart_images:
                    prompt += f"- Dosya Adı: {chart['filename']}, Başlık: {chart['title']}\n"
            else:
                prompt += "\nUYARI: Bu rapor için kullanılabilir grafik dosyası bulunmamaktadır. Lütfen LaTeX çıktısına \\includegraphics komutu EKLEMEYİN.\n"

            prompt += "\nSOHBET GEÇMİŞİ:\n"
            for msg in chat_messages:
                prompt += f"Gönderen: {msg.get('sender', 'Bilinmiyor')}\nMesaj: {msg.get('text', '')}\n\n"

            response = agent.run(prompt)
            latex_content = response.content if response else ""

            if not latex_content or not latex_content.strip():
                print("Agent did not return any LaTeX content. Aborting.")
                return

            # Step 5: Compile LaTeX to PDF
            pdf_path = self._compile_latex_to_pdf(latex_content, chat_id, temp_path)

            # Step 6: Store the final PDF
            if pdf_path:
                self._store_pdf_report(pdf_path, chat_id)
