'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import dynamic from 'next/dynamic';
import Box from '@mui/material/Box';
import Navbar from '../components/Navbar';
// import { ChatInterface } from './components/ChatInterface';
import LeftPanel from '../components/LeftPanel';
import CenterPanel from '../components/CenterPanel';
// Dynamically import RightPanel with SSR disabled
const RightPanel = dynamic(() => import('../components/RightPanel'), { ssr: false });
import theme from './utils/theme';
import './globals.css';
import { ChatMessage } from '../components/types';
import { ResizableBox } from 'react-resizable'; // Import ResizableBox
import 'react-resizable/css/styles.css'; // Import Resizable CSS

export default function Home() {
  // User state
  const [userId, setUserId] = useState<string>('');
  const [username, setUsername] = useState<string>('');
  
  // Chat state
  const [currentChatId, setCurrentChatId] = useState<string>('');
  
  // Task tracking state
  const [currentTaskKey, setCurrentTaskKey] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [lastAgentMessage, setLastAgentMessage] = useState<ChatMessage | null>(null);
  const [initialTaskData, setInitialTaskData] = useState<{
    task_key: string;
    plan?: {
      reason: string;
      task_list: string[];
      agents?: Array<{
        agent_id: string;
        agent_name: string;
        task: string;
        reason: string;
      }>;
    };
  } | null>(null);
  
  // Panel collapse states
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);

  // Check for existing user session on load
  useEffect(() => {
    const storedUserId = localStorage.getItem('userId');
    const storedUsername = localStorage.getItem('username');
    
    if (storedUserId && storedUsername) {
      console.log('Found existing user session:', { userId: storedUserId, username: storedUsername });
      setUserId(storedUserId);
      setUsername(storedUsername);
      
      // We could optionally load default chat here if needed
      // This would happen automatically via the ConversationList component
      // which loads chats when it receives a userId
    } else {
      console.log('No existing user session found');
    }
  }, []);

  // Handle user login
  const handleLogin = (newUserId: string, newUsername: string) => {
    setUserId(newUserId);
    setUsername(newUsername);
    localStorage.setItem('userId', newUserId);
    localStorage.setItem('username', newUsername);
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('userId');
    localStorage.removeItem('username');
    setUserId('');
    setUsername('');
    setCurrentChatId('');
    setCurrentTaskKey(null);
    setIsLoading(false);
    setLastAgentMessage(null);
  };

  // Handle chat selection
  const handleSelectChat = (chatId: string) => {
    if (chatId !== currentChatId) {
      setCurrentChatId(chatId);
      setCurrentTaskKey(null);
      setIsLoading(false);
      setLastAgentMessage(null);
      setInitialTaskData(null);
    }
  };

  // Handle task key update from CenterPanel
  const handleTaskKeyChange = useCallback((taskKey: string | null) => {
    setCurrentTaskKey(taskKey);
    if (taskKey) {
      console.log('[Page] Task starting, setting loading TRUE');
      setIsLoading(true);
      setLastAgentMessage(null);
    } else {
      //setIsLoading(false);
    }
  }, []);

  const handleTaskStart = useCallback(() => {
    setIsLoading(true)
  }, []);

  // Handle initial task data from CenterPanel
  const handleInitialTaskData = useCallback((taskData: {
    task_key: string;
    plan?: {
      reason: string;
      task_list: string[];
      agents?: Array<{
        agent_id: string;
        agent_name: string;
        task: string;
        reason: string;
      }>;
    };
  }) => {
    console.log('[Page] Received initial task data:', taskData);
    setInitialTaskData(taskData);
  }, []);

  // Called by RightPanel when a final completion/failure message is detected
  const handleTaskCompletion = useCallback((messageData: { text: string; error: boolean; taskKey: string; isTaskOutput: true }) => {
    console.log('[Page] Task completion detected, setting loading FALSE, preparing message');
    setIsLoading(false);
    setCurrentTaskKey(null);
    setLastAgentMessage({
      id: `agent-final-${Date.now()}`,
      sender: 'agent',
      text: messageData.text,
      timestamp: Date.now(),
      error: messageData.error,
      isTaskOutput: true,
      taskKey: messageData.taskKey
    });
  }, []);

  // Callback for CenterPanel to clear the message after processing
  const clearLastAgentMessage = useCallback(() => {
    setLastAgentMessage(null);
  }, []);

  // Handlers for panel collapse
  const handleLeftPanelCollapse = useCallback((isCollapsed: boolean) => {
    setIsLeftPanelCollapsed(isCollapsed);
  }, []);

  const handleRightPanelCollapse = useCallback((isCollapsed: boolean) => {
    setIsRightPanelCollapsed(isCollapsed);
  }, []);

  // Define container class based on panel states
  const containerClassName = `app-container ${isLeftPanelCollapsed ? 'left-collapsed' : ''} ${isRightPanelCollapsed ? 'right-collapsed' : ''}`;

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {/* <ChatInterface /> */}
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
        <Navbar />
        <div className={containerClassName}>
          <LeftPanel 
            userId={userId}
            username={username}
            currentChatId={currentChatId}
            onSelectChat={handleSelectChat}
            onLogout={handleLogout}
            onCollapse={handleLeftPanelCollapse}
          />
          <CenterPanel 
            userId={userId} 
            currentChatId={currentChatId}
            isLoading={isLoading}
            lastAgentMessage={lastAgentMessage}
            clearLastAgentMessage={clearLastAgentMessage}
            onLogin={handleLogin}
            onTaskKeyChange={handleTaskKeyChange}
            onTaskStart={handleTaskStart}
            onInitialTaskData={handleInitialTaskData}
          />
          {/* Wrap RightPanel with ResizableBox */}
          <ResizableBox
            className="right-panel-resizable-container" // Add a class for potentially targeting the container
            width={500} // Initial width
            minConstraints={[350, Infinity]} // Min width 250px, no height constraint
            maxConstraints={[800, Infinity]} // Max width 600px, no height constraint
            axis="x" // Allow resizing horizontally only
            resizeHandles={['w']} // Show handle on the left ('west') side
            handle={<span className="custom-resize-handle custom-resize-handle-w" />} // Custom handle styling
            style={{
              overflow: 'hidden', // Prevent content overflow during resize
              position: 'relative', // Needed for handle positioning
              zIndex: 1 // Ensure it's above CenterPanel if overlapping during drag
            }}
          >
            {/* The ResizableBox requires a single child with 100% height/width */}
            <Box sx={{ height: '100%', width: '100%', display: 'flex', flexDirection: 'column' }}>
              <RightPanel 
                currentChatId={currentChatId}
                userId={userId}
                taskKey={currentTaskKey || undefined}
                initialTaskData={initialTaskData || undefined}
                onTaskCompletion={handleTaskCompletion}
                onCollapse={handleRightPanelCollapse}
              />
            </Box>
          </ResizableBox>
        </div>
      </Box>
    </ThemeProvider>
  );
}
