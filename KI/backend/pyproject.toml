[project]
name = "ki-backend"
version = "0.1.0"
description = "KI Backend Service"
requires-python = ">=3.10"
dependencies = [
    "crewai==0.108.0",
    "fastapi",
    "uvicorn[standard]",
    "pydantic",
    "pydantic-settings",
    "python-dotenv",
    "httpx",
    "PyYAML",
    "sentence-transformers==4.0.2",
    "transformers==4.50.3",
    "qdrant-client==1.13.3",
    "sqlalchemy",
    "pandas",
    "openpyxl",
    "sqlparse",
    "PyPDF2",
    "asyncpg",
    "greenlet",
    "psycopg2-binary",
    "mammoth",
    "redis>=6.0.0",
    "llama-parse>=0.6.21",
    "docx>=0.2.4",
    "python-docx>=1.1.2",
    "reportlab>=4.4.0",
    "agno",
    "anthropic",
    "langchain>=0.3.25",
    "python-multipart>=0.0.20",
    "PyMuPDF>=1.23.26",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0", 
    "plotly>=5.17.0",
    "jinja2>=3.1.0",
    "subprocess32",
    "pylatex>=1.4.0",
    "xlsxwriter",
    "openpyxl"
]

