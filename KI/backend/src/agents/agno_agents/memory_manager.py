from agno.memory.v2.memory import Memory
from agno.memory.v2.db.postgres import PostgresMemoryDb
import os


class MemoryManager:
    """
    Memory Manager for Agno Agents using Agno's Memory.V2.

    This class initializes and provides a single Memory instance
    that can handle multi-user and multi-session data intrinsically
    when user_id and session_id are provided to agent/team run methods.
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MemoryManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """
        Initialize the Memory Manager.
        This creates a single Memory instance. The underlying database
        (defaulting to SQLite) will be managed by Agno's Memory.V2.
        """
        if not MemoryManager._initialized:
            self.memory = Memory(
                db=PostgresMemoryDb(
                    table_name="agent_memory",
                    db_url=os.getenv("DATABASE_URL")
                )
            )
            MemoryManager._initialized = True

    def get_memory_instance(self) -> Memory:
        """
        Get the shared Memory instance.

        Returns:
            The Agno Memory.V2 instance.
        """
        return self.memory