{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "name": "Kai API V1", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/health", "host": ["{{base_url}}"], "path": ["apiv1", "health"]}}, "response": [{"name": "Successful Health Check", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/health", "host": ["{{base_url}}"], "path": ["apiv1", "health"]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"status\": \"healthy\",\n        \"version\": \"1.0.0\"\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-health-123\"\n}"}]}]}, {"name": "Users", "item": [{"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"username\": \"testuser\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/apiv1/users/login", "host": ["{{base_url}}"], "path": ["apiv1", "users", "login"]}}, "response": [{"name": "User Login/Creation Successful", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"username\": \"new_or_existing_user\"}"}, "url": {"raw": "{{base_url}}/apiv1/users/login", "host": ["{{base_url}}"], "path": ["apiv1", "users", "login"]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"user_id\": \"user-uuid-12345\"\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-login-130\"\n}"}, {"name": "Login - <PERSON><PERSON><PERSON> (Too Short)", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"username\": \"us\"}"}, "url": {"raw": "{{base_url}}/apiv1/users/login", "host": ["{{base_url}}"], "path": ["apiv1", "users", "login"]}}, "status": "Unprocessable Entity", "code": 422, "_postman_AlgunStatusText": "Unprocessable Entity", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": null,\n    \"error\": {\n        \"code\": \"invalid_parameter_value\",\n        \"message\": \"Kullanıcı adı en az 3 karakter olmalıdır.\",\n        \"field\": \"username\",\n        \"target\": null,\n        \"details\": null\n    },\n    \"request_id\": \"example-req-login-err-131\"\n}"}]}, {"name": "Get User Chats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/users/:user_id/chats", "host": ["{{base_url}}"], "path": ["apiv1", "users", ":user_id", "chats"], "variable": [{"key": "user_id", "value": "YOUR_USER_ID_EXAMPLE", "description": "ID of the user"}]}}, "response": [{"name": "User Chats Retrieved Successfully", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/users/user-uuid-12345/chats", "host": ["{{base_url}}"], "path": ["apiv1", "users", "user-uuid-12345", "chats"]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"chats\": [\n            {\"chat_id\": \"chat-uuid-abc\", \"chat_name\": \"Chat 1\", \"created_at\": \"2023-10-27T10:00:00Z\"},\n            {\"chat_id\": \"chat-uuid-def\", \"chat_name\": \"Important Analysis\", \"created_at\": \"2023-10-28T11:30:00Z\"}\n        ]\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-getuserchats-132\"\n}"}, {"name": "Get User Chats - User Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/users/non_existent_user/chats", "host": ["{{base_url}}"], "path": ["apiv1", "users", "non_existent_user", "chats"]}}, "status": "Not Found", "code": 404, "_postman_AlgunStatusText": "Not Found", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": null,\n    \"error\": {\n        \"code\": \"record_not_found_in_db\",\n        \"message\": \"Kullanıcı 'non_existent_user' bulunamadı.\",\n        \"field\": null,\n        \"target\": \"User\",\n        \"details\": null\n    },\n    \"request_id\": \"example-req-getuserchats-err-133\"\n}"}]}]}, {"name": "Chats", "item": [{"name": "Create Chat Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"YOUR_USER_ID\",\n\t\"chat_name\": \"My New Chat\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/apiv1/chats", "host": ["{{base_url}}"], "path": ["apiv1", "chats"]}}, "response": [{"name": "Chat Session Created Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"user_id\": \"valid_user_id\", \"chat_name\": \"My Test Chat\"}"}, "url": {"raw": "{{base_url}}/apiv1/chats", "host": ["{{base_url}}"], "path": ["apiv1", "chats"]}}, "status": "Created", "code": 201, "_postman_AlgunStatusText": "Created", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"chat_id\": \"newly-created-chat-uuid\",\n        \"user_id\": \"valid_user_id\",\n        \"chat_name\": \"My Test Chat\"\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-createchat-123\"\n}"}, {"name": "Create Chat - Missing User ID", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"chat_name\": \"My Test Chat\"}"}, "url": {"raw": "{{base_url}}/apiv1/chats", "host": ["{{base_url}}"], "path": ["apiv1", "chats"]}}, "status": "Bad Request", "code": 400, "_postman_AlgunStatusText": "Bad Request", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": null,\n    \"error\": {\n        \"code\": \"missing_required_field\",\n        \"message\": \"Gerekli bir alan eksik.\",\n        \"field\": \"user_id\",\n        \"target\": null,\n        \"details\": null\n    },\n    \"request_id\": \"example-req-createchat-err-124\"\n}"}]}, {"name": "Upload File to Chat", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "user_id", "value": "YOUR_USER_ID", "type": "text"}, {"key": "file_type", "value": "document", "type": "text", "description": "\"csv\" or \"document\""}, {"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/files", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "files"], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat"}]}}, "response": [{"name": "File Upload Successful (Processing Started)", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "user_id", "value": "user123", "type": "text"}, {"key": "file_type", "value": "document", "type": "text"}, {"key": "file", "type": "file", "src": "dummy.pdf"}]}, "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/files", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "files"]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"message\": \"Dosya alındı ve arka planda işlenmeye başlandı.\",\n        \"filename\": \"dummy.pdf\",\n        \"chat_id\": \"chat_abc\"\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-upload-125\"\n}"}, {"name": "Upload File - Chat Not Found", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "user_id", "value": "user123", "type": "text"}, {"key": "file_type", "value": "document", "type": "text"}, {"key": "file", "type": "file", "src": "dummy.pdf"}]}, "url": {"raw": "{{base_url}}/apiv1/chats/non_existent_chat/files", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "non_existent_chat", "files"]}}, "status": "Not Found", "code": 404, "_postman_AlgunStatusText": "Not Found", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": null,\n    \"error\": {\n        \"code\": \"chat_session_not_found\",\n        \"message\": \"Chat oturumu 'non_existent_chat' bulunamadı.\",\n        \"field\": null,\n        \"target\": \"ChatSession\",\n        \"details\": null\n    },\n    \"request_id\": \"example-req-upload-err-126\"\n}"}]}, {"name": "Analyze Content in Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"YOUR_USER_ID\",\n\t\"input_text\": \"Analyze this sales data and provide insights.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/analyze", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "analyze"], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat"}]}}, "response": [{"name": "Analysis Started Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"user_id\": \"user123\", \"input_text\": \"Analyze this sales data and provide insights.\"}"}, "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/analyze", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "analyze"]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"task_key\": \"chat_abc_user123_a1b2c3d4-e5f6-7890-1234-567890abcdef\",\n        \"plan\": {\n            \"reason\": \"Analysis plan created\",\n            \"task_list\": [\n                \"Data Analyst: Analyze sales data trends and patterns\",\n                \"Business Intelligence Agent: Generate business insights\",\n                \"Report Generator: Create comprehensive analysis report\"\n            ],\n            \"agents\": [\n                {\n                    \"agent_id\": \"data_analyst\",\n                    \"agent_name\": \"Data Analyst\",\n                    \"task\": \"Analyze sales data trends and patterns\",\n                    \"reason\": \"User requested analysis of sales data which requires statistical analysis expertise\"\n                },\n                {\n                    \"agent_id\": \"business_intelligence\",\n                    \"agent_name\": \"Business Intelligence Agent\",\n                    \"task\": \"Generate business insights\",\n                    \"reason\": \"Sales data analysis needs business context and actionable insights\"\n                },\n                {\n                    \"agent_id\": \"report_generator\",\n                    \"agent_name\": \"Report Generator\",\n                    \"task\": \"Create comprehensive analysis report\",\n                    \"reason\": \"User needs a well-formatted report summarizing all findings\"\n                }\n            ]\n        }\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-analyze-success-127\"\n}"}, {"name": "Analyze - Planning Failed", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"user_id\": \"user123\", \"input_text\": \"Analyze this data.\"}"}, "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/analyze", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "analyze"]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"task_key\": \"chat_abc_user123_failed-task-key\",\n        \"plan\": null\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-analyze-failed-128\"\n}"}, {"name": "Analyze - Chat Not Found", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"user_id\": \"user123\", \"input_text\": \"Analyze this data.\"}"}, "url": {"raw": "{{base_url}}/apiv1/chats/non_existent_chat/analyze", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "non_existent_chat", "analyze"]}}, "status": "Not Found", "code": 404, "_postman_AlgunStatusText": "Not Found", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": null,\n    \"error\": {\n        \"code\": \"chat_session_not_found\",\n        \"message\": \"Chat oturumu 'non_existent_chat' bulunamadı.\",\n        \"field\": null,\n        \"target\": \"ChatSession\",\n        \"details\": null\n    },\n    \"request_id\": \"example-req-analyze-err-129\"\n}"}]}, {"name": "Get Chat Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/messages?limit=100&skip=0", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "messages"], "query": [{"key": "limit", "value": "100", "description": "Max messages to return"}, {"key": "skip", "value": "0", "description": "Messages to skip"}], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat"}]}}, "response": [{"name": "Chat Messages Retrieved Successfully", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/messages?limit=2", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "messages"], "query": [{"key": "limit", "value": "2"}]}}, "status": "OK", "code": 200, "_postman_AlgunStatusText": "OK", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"messages\": [\n            {\"id\": \"msg1\", \"sender\": \"user\", \"text\": \"Hello\", \"timestamp\": 1678886400.0},\n            {\"id\": \"msg2\", \"sender\": \"agent\", \"text\": \"Hi there!\", \"timestamp\": 1678886460.0}\n        ],\n        \"total_count\": 2\n    },\n    \"error\": null,\n    \"request_id\": \"example-req-getmsg-128\"\n}"}, {"name": "Get Chat Messages - Server Error", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_error_case/messages", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_error_case", "messages"]}}, "status": "Internal Server Error", "code": 500, "_postman_AlgunStatusText": "Internal Server Error", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": null,\n    \"error\": {\n        \"code\": \"database_query_failed\",\n        \"message\": \"Chat 'chat_error_case' için mesajlar alınırken bir veritabanı hatası oluştu.\",\n        \"field\": null,\n        \"target\": null,\n        \"details\": null\n    },\n    \"request_id\": \"example-req-getmsg-err-129\"\n}"}]}, {"name": "WebSocket: Chat Task Updates", "request": {"method": "GET", "header": [], "url": {"raw": "{{websocket_protocol}}://{{websocket_host}}:{{websocket_port}}/apiv1/chats/:chat_id/ws", "protocol": "{{websocket_protocol}}", "host": ["{{websocket_host}}"], "port": "{{websocket_port}}", "path": ["apiv1", "chats", ":chat_id", "ws"], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat"}]}, "description": "Connect to this WebSocket for real-time updates for a specific chat.\n**URL**: `{{websocket_protocol}}://{{websocket_host}}:{{websocket_port}}/apiv1/chats/:chat_id/ws`\n\n**Instructions**:\n1. In Postman, create a **new WebSocket request** (File > New > WebSocket Request).\n2. Use the constructed URL (e.g., ws://localhost:8000/apiv1/chats/YOUR_CHAT_ID_EXAMPLE/ws) to connect."}, "response": []}, {"name": "Get Chat Assets", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/assets", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "assets"], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat to retrieve assets from"}]}, "description": "Retrieve all assets (charts, visualizations, reports, etc.) generated during chat interactions.\n\nThis endpoint returns a comprehensive list of assets created during the chat session, including:\n- Charts and visualizations\n- Generated CSV/Excel reports\n- Analysis results\n\nEach asset includes metadata such as:\n- Creation timestamp\n- Associated message ID\n- Asset type\n- Context of creation"}, "response": [{"name": "Successful Response with Assets", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/assets", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "assets"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"chat_id\": \"chat_abc\",\n        \"assets\": {\n            \"charts\": [\n                {\n                    \"id\": \"chart_123\",\n                    \"type\": \"line_chart\",\n                    \"title\": \"Sales Trend Analysis\",\n                    \"message_id\": \"msg_456\",\n                    \"created_at\": \"2024-01-15T14:30:00Z\",\n                    \"data\": {\n                        \"chart_data\": \"base64_encoded_chart_data\",\n                        \"chart_type\": \"line\",\n                        \"config\": {\n                            \"x_axis\": \"Date\",\n                            \"y_axis\": \"Sales Amount\"\n                        }\n                    },\n                    \"context\": \"Generated during sales trend analysis\"\n                }\n            ],\n            \"reports\": [\n                {\n                    \"id\": \"report_456\",\n                    \"type\": \"csv_report\",\n                    \"title\": \"Top Selling Products Report\",\n                    \"file_name\": \"top_products_20241215_143022.csv\",\n                    \"file_path\": \"/path/to/reports/top_products_20241215_143022.csv\",\n                    \"message_id\": \"msg_789\",\n                    \"created_at\": \"2024-01-15T14:30:22Z\",\n                    \"table_name\": \"user_abc123_sales_data\",\n                    \"description\": \"Analysis of top-selling products based on sales volume\",\n                    \"context\": \"Generated CSV report for product analysis\"\n                }\n            ],\n            \"other\": [\n                {\n                    \"id\": \"asset_789\",\n                    \"type\": \"data_summary\",\n                    \"title\": \"Statistical Summary\",\n                    \"message_id\": \"msg_458\",\n                    \"created_at\": \"2024-01-15T14:40:00Z\",\n                    \"data\": {\n                        \"format\": \"json\",\n                        \"content\": {\n                            \"mean\": 1234.56,\n                            \"median\": 1000.00,\n                            \"std_dev\": 234.56\n                        }\n                    },\n                    \"context\": \"Statistical analysis of sales data\"\n                }\n            ]\n        },\n        \"metadata\": {\n            \"total_assets\": 3,\n            \"total_charts\": 1,\n            \"total_reports\": 1,\n            \"total_other\": 1,\n            \"last_updated\": \"2024-01-15T14:40:00Z\"\n        }\n    },\n    \"error\": null,\n    \"request_id\": \"req_assets_123\"\n}"}, {"name": "No Assets Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_no_assets/assets", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_no_assets", "assets"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"chat_id\": \"chat_no_assets\",\n        \"assets\": {\n            \"charts\": [],\n            \"reports\": [],\n            \"other\": []\n        },\n        \"metadata\": {\n            \"total_assets\": 0,\n            \"total_charts\": 0,\n            \"total_reports\": 0,\n            \"total_other\": 0,\n            \"last_updated\": null\n        }\n    },\n    \"error\": null,\n    \"request_id\": \"req_assets_124\"\n}"}, {"name": "Chat Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/non_existent_chat/assets", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "non_existent_chat", "assets"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"data\": null,\n    \"error\": {\n        \"code\": \"chat_not_found\",\n        \"message\": \"Chat session 'non_existent_chat' not found\",\n        \"field\": null,\n        \"target\": \"ChatSession\",\n        \"details\": null\n    },\n    \"request_id\": \"req_assets_125\"\n}"}, {"name": "Server Error", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/error_chat/assets", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "error_chat", "assets"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"data\": null,\n    \"error\": {\n        \"code\": \"internal_server_error\",\n        \"message\": \"An error occurred while retrieving chat assets\",\n        \"field\": null,\n        \"target\": null,\n        \"details\": \"Internal server error occurred during asset retrieval\"\n    },\n    \"request_id\": \"req_assets_126\"\n}"}]}, {"name": "Download Chat Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/reports/:file_name?user_id={{user_id}}", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "reports", ":file_name"], "query": [{"key": "user_id", "value": "{{user_id}}", "description": "User ID for authorization"}], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat containing the report"}, {"key": "file_name", "value": "top_products_20241215_143022.csv", "description": "Name of the report file to download"}]}, "description": "Download a specific CSV/Excel report generated during chat interactions.\n\nThis endpoint streams the actual file content for download (not in response body). The file is served using FastAPI's FileResponse for efficient streaming of large files.\n\n**Features**:\n- Streams file directly (efficient for large CSV files)\n- Proper download headers (Content-Disposition: attachment)\n- Media type: application/octet-stream\n\n**Security**: \n- User must own the chat to download reports\n- File must exist and be accessible\n- Returns 404 if file not found or user doesn't have access"}, "response": [{"name": "Successful File Download", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/reports/top_products_20241215_143022.csv?user_id=user123", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "reports", "top_products_20241215_143022.csv"], "query": [{"key": "user_id", "value": "user123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "application/octet-stream"}, {"key": "Content-Disposition", "value": "attachment; filename=top_products_20241215_143022.csv"}, {"key": "Content-Length", "value": "1024"}], "cookie": [], "body": "[Binary file content - File will be downloaded directly to browser/client]"}, {"name": "Report Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/reports/non_existent_report.csv?user_id=user123", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "reports", "non_existent_report.csv"], "query": [{"key": "user_id", "value": "user123"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Report file 'non_existent_report.csv' not found for chat 'chat_abc'\"\n}"}, {"name": "Unauthorized Access", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/other_user_chat/reports/report.csv?user_id=user123", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "other_user_chat", "reports", "report.csv"], "query": [{"key": "user_id", "value": "user123"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Chat 'other_user_chat' not found or you don't have access\"\n}"}, {"name": "Missing User ID", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/reports/report.csv", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "reports", "report.csv"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"user_id query parameter is required\"\n}"}]}, {"name": "Generate PDF Chat Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"{{user_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/reports", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "reports"], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat to generate a report for"}]}, "description": "Triggers an asynchronous background task to generate a PDF report for the chat session. The report can be downloaded from the download endpoint once ready."}, "response": [{"name": "Report Generation Started", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"user123\"\n}"}, "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/reports", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "reports"]}}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"PDF report generation has been started.\",\n    \"chat_id\": \"chat_abc\"\n}"}, {"name": "Generate Report - Chat Not Found", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"user123\"\n}"}, "url": {"raw": "{{base_url}}/apiv1/chats/non_existent_chat/reports", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "non_existent_chat", "reports"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Chat 'non_existent_chat' not found or you don't have access.\"\n}"}]}, {"name": "Download PDF Chat Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/:chat_id/reports/download?user_id={{user_id}}", "host": ["{{base_url}}"], "path": ["apiv1", "chats", ":chat_id", "reports", "download"], "query": [{"key": "user_id", "value": "{{user_id}}", "description": "User ID for authorization"}], "variable": [{"key": "chat_id", "value": "YOUR_CHAT_ID_EXAMPLE", "description": "ID of the chat containing the report"}]}, "description": "Downloads a specific PDF report generated during chat interactions. This endpoint streams the actual file content."}, "response": [{"name": "Successful PDF Download", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_abc/reports/download?user_id=user123", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_abc", "reports", "download"], "query": [{"key": "user_id", "value": "user123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "application/pdf"}, {"key": "Content-Disposition", "value": "attachment; filename=report_chat_abc.pdf"}], "cookie": [], "body": "[Binary PDF file content]"}, {"name": "Download PDF - Report Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apiv1/chats/chat_def/reports/download?user_id=user123", "host": ["{{base_url}}"], "path": ["apiv1", "chats", "chat_def", "reports", "download"], "query": [{"key": "user_id", "value": "user123"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Report for chat 'chat_def' not found. Please generate it first.\"\n}"}]}]}, {"name": "Data Management", "item": [{"name": "Get User Tables", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/:user_id/tables", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", ":user_id", "tables"], "variable": [{"key": "user_id", "value": "{{user_id}}", "description": "User ID to get tables for"}]}, "description": "Get all tables owned by a user with comprehensive metadata including row counts, column information, and creation dates."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/user123/tables", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", "user123", "tables"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"user_id\": \"user123\",\n        \"tables_count\": 2,\n        \"tables\": [\n            {\n                \"id\": 1,\n                \"name\": \"user_user123_sales_data_20241201_abc123\",\n                \"original_file_name\": \"sales_data.csv\",\n                \"created_at\": \"2024-12-01T10:30:00Z\",\n                \"updated_at\": \"2024-12-01T10:30:00Z\",\n                \"row_count\": 1500,\n                \"column_count\": 8,\n                \"columns\": [\n                    {\n                        \"name\": \"customer_id\",\n                        \"type\": \"INTEGER\",\n                        \"nullable\": true\n                    },\n                    {\n                        \"name\": \"product_name\",\n                        \"type\": \"VARCHAR(255)\",\n                        \"nullable\": true\n                    },\n                    {\n                        \"name\": \"sale_amount\",\n                        \"type\": \"NUMERIC\",\n                        \"nullable\": true\n                    },\n                    {\n                        \"name\": \"sale_date\",\n                        \"type\": \"DATE\",\n                        \"nullable\": true\n                    }\n                ]\n            },\n            {\n                \"id\": 2,\n                \"name\": \"user_user123_inventory_20241201_def456\",\n                \"original_file_name\": \"inventory.xlsx\",\n                \"created_at\": \"2024-12-01T11:15:00Z\",\n                \"updated_at\": \"2024-12-01T11:15:00Z\",\n                \"row_count\": 850,\n                \"column_count\": 5,\n                \"columns\": [\n                    {\n                        \"name\": \"product_id\",\n                        \"type\": \"INTEGER\",\n                        \"nullable\": true\n                    },\n                    {\n                        \"name\": \"product_name\",\n                        \"type\": \"VARCHAR(255)\",\n                        \"nullable\": true\n                    },\n                    {\n                        \"name\": \"quantity\",\n                        \"type\": \"INTEGER\",\n                        \"nullable\": true\n                    }\n                ]\n            }\n        ]\n    },\n    \"message\": \"Retrieved 2 tables for user user123\",\n    \"error\": null\n}"}]}, {"name": "Get Table Details", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/:user_id/tables/:table_id?sample_rows=5", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", ":user_id", "tables", ":table_id"], "query": [{"key": "sample_rows", "value": "5", "description": "Number of sample rows to return (1-50)"}], "variable": [{"key": "user_id", "value": "{{user_id}}", "description": "User ID who owns the table"}, {"key": "table_id", "value": "1", "description": "Table ID to get details for"}]}, "description": "Get detailed information about a specific table including both metadata and sample data preview."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/user123/tables/1?sample_rows=5", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", "user123", "tables", "1"], "query": [{"key": "sample_rows", "value": "5"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"metadata\": {\n            \"id\": 1,\n            \"name\": \"user_user123_sales_data_20241201_abc123\",\n            \"original_file_name\": \"sales_data.csv\",\n            \"created_at\": \"2024-12-01T10:30:00Z\",\n            \"updated_at\": \"2024-12-01T10:30:00Z\",\n            \"row_count\": 1500,\n            \"column_count\": 4,\n            \"columns\": [\n                {\n                    \"name\": \"customer_id\",\n                    \"type\": \"INTEGER\",\n                    \"nullable\": true\n                },\n                {\n                    \"name\": \"product_name\",\n                    \"type\": \"VARCHAR(255)\",\n                    \"nullable\": true\n                },\n                {\n                    \"name\": \"sale_amount\",\n                    \"type\": \"NUMERIC\",\n                    \"nullable\": true\n                },\n                {\n                    \"name\": \"sale_date\",\n                    \"type\": \"DATE\",\n                    \"nullable\": true\n                }\n            ]\n        },\n        \"preview\": {\n            \"table_id\": 1,\n            \"table_name\": \"user_user123_sales_data_20241201_abc123\",\n            \"columns\": [\"customer_id\", \"product_name\", \"sale_amount\", \"sale_date\"],\n            \"sample_rows\": [\n                {\n                    \"customer_id\": 1001,\n                    \"product_name\": \"Laptop Pro\",\n                    \"sale_amount\": 1299.99,\n                    \"sale_date\": \"2024-11-15\"\n                },\n                {\n                    \"customer_id\": 1002,\n                    \"product_name\": \"Wireless Mouse\",\n                    \"sale_amount\": 29.99,\n                    \"sale_date\": \"2024-11-16\"\n                },\n                {\n                    \"customer_id\": 1003,\n                    \"product_name\": \"Monitor 4K\",\n                    \"sale_amount\": 399.99,\n                    \"sale_date\": \"2024-11-17\"\n                },\n                {\n                    \"customer_id\": 1004,\n                    \"product_name\": \"Keyboard Mechanical\",\n                    \"sale_amount\": 149.99,\n                    \"sale_date\": \"2024-11-18\"\n                },\n                {\n                    \"customer_id\": 1005,\n                    \"product_name\": \"Webcam HD\",\n                    \"sale_amount\": 79.99,\n                    \"sale_date\": \"2024-11-19\"\n                }\n            ],\n            \"total_rows\": 1500,\n            \"preview_rows_count\": 5\n        }\n    },\n    \"message\": \"Retrieved details for table user_user123_sales_data_20241201_abc123\",\n    \"error\": null\n}"}]}, {"name": "Get Table Preview with Pagination", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/:user_id/tables/:table_id/preview?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", ":user_id", "tables", ":table_id", "preview"], "query": [{"key": "limit", "value": "10", "description": "Number of rows to return (1-100)"}, {"key": "offset", "value": "0", "description": "Number of rows to skip for pagination"}], "variable": [{"key": "user_id", "value": "{{user_id}}", "description": "User ID who owns the table"}, {"key": "table_id", "value": "1", "description": "Table ID to preview"}]}, "description": "Get a paginated preview of table data, useful for browsing through larger datasets."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/user123/tables/1/preview?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", "user123", "tables", "1", "preview"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"table_id\": 1,\n        \"table_name\": \"user_user123_sales_data_20241201_abc123\",\n        \"columns\": [\"customer_id\", \"product_name\", \"sale_amount\", \"sale_date\"],\n        \"sample_rows\": [\n            {\n                \"customer_id\": 1001,\n                \"product_name\": \"Laptop Pro\",\n                \"sale_amount\": 1299.99,\n                \"sale_date\": \"2024-11-15\"\n            },\n            {\n                \"customer_id\": 1002,\n                \"product_name\": \"Wireless Mouse\",\n                \"sale_amount\": 29.99,\n                \"sale_date\": \"2024-11-16\"\n            }\n        ],\n        \"total_rows\": 1500,\n        \"preview_rows_count\": 10\n    },\n    \"message\": \"Retrieved 10 rows from table user_user123_sales_data_20241201_abc123\",\n    \"error\": null\n}"}]}, {"name": "Delete User Table", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/:user_id/tables/:table_id", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", ":user_id", "tables", ":table_id"], "variable": [{"key": "user_id", "value": "{{user_id}}", "description": "User ID who owns the table"}, {"key": "table_id", "value": "1", "description": "Table ID to delete"}]}, "description": "Delete a user's table and all associated data. Use with caution as this action cannot be undone."}, "response": [{"name": "Not Yet Implemented", "originalRequest": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/user123/tables/1", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", "user123", "tables", "1"]}}, "status": "Not Implemented", "code": 501, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Table deletion functionality is not yet implemented\"\n}"}]}]}, {"name": "Error Cases - Data Management", "item": [{"name": "Get Tables - Invalid User ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/ /tables", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", " ", "tables"]}, "description": "Test error handling for empty or invalid user ID"}, "response": [{"name": "Bad Request", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/ /tables", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", " ", "tables"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"User ID is required and cannot be empty\"\n}"}]}, {"name": "Get Table Details - Table Not Found", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/:user_id/tables/99999", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", ":user_id", "tables", "99999"], "variable": [{"key": "user_id", "value": "{{user_id}}", "description": "Valid user ID"}]}, "description": "Test error handling for non-existent table ID"}, "response": [{"name": "Not Found", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/apiv1/data/users/user123/tables/99999", "host": ["{{base_url}}"], "path": ["apiv1", "data", "users", "user123", "tables", "99999"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Table with ID 99999 not found for user user123\"\n}"}]}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "websocket_protocol", "value": "ws", "type": "string"}, {"key": "websocket_host", "value": "localhost", "type": "string"}, {"key": "websocket_port", "value": "8000", "type": "string"}, {"key": "user_id", "value": "user123", "type": "string", "description": "Default user ID for testing"}]}